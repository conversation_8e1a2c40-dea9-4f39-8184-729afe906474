package com.dib.framework.aspectj;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.ParserConfig;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mzt.logapi.beans.LogRecord;
import com.mzt.logapi.service.ILogRecordService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Service
@Slf4j
public class LogAspectServiceImpl  implements ILogRecordService {

    @Value("${common.permissionUrl}")
    private String permissionUrl;

    @Value("${common.logSaveUrl}")
    private String logSaveUrl;


    private ResponseEntity<JSONObject> doRequest(String url, HttpMethod method, JSONObject body) {
        RestTemplate restTemplate = new RestTemplate();
        String tokenValue = StpUtil.getTokenValue();
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + tokenValue);
        headers.setContentType(MediaType.APPLICATION_JSON);
        // 可以添加其他需要的请求头
        HttpEntity<JSONObject> entity = new HttpEntity<>(body, headers);
        return restTemplate.exchange(
                url,
                method,
                entity,
                JSONObject.class
        );
    }

    public void fillUserFields(OperateLogCreateReqDTO operateLogCreateReqDTO) {
        ResponseEntity<JSONObject> remote = doRequest(permissionUrl,HttpMethod.GET,null);
        JSONObject obj = remote.getBody();
        if(obj == null) {
            return;
        }
        obj = obj.getJSONObject("user");
        operateLogCreateReqDTO.setUserId(obj.getString("id"));
        operateLogCreateReqDTO.setUserName(obj.getString("nickname"));
        operateLogCreateReqDTO.setTenantId("1");
//        String userId = (String) StpUtil.getLoginIdByToken(StpUtil.getTokenValue());
//        SaSession session = StpUtil.getSessionByLoginId(userId);
//        Object user = session.get("user");
//        JSONObject userObj = JSON.parseObject(JSON.toJSONString(user));
//        operateLogCreateReqDTO.setTenantId(userObj.getString("tenantId"));
    }


    /**
     * 填充模块相关字段
     */
    public static void fillModuleFields(OperateLogCreateReqDTO reqDTO, LogRecord logRecord) {
        reqDTO.setType(String.valueOf(logRecord.getType())); // 大模块类型
        reqDTO.setSubType(logRecord.getSubType()); // 操作名称
        reqDTO.setBizId(logRecord.getBizNo()); // 业务编号
        reqDTO.setAction(logRecord.getAction()); // 操作内容
        reqDTO.setExtra(logRecord.getExtra()); // 拓展字段

    }

    /**
     * 填充请求相关字段
     */
    private static void fillRequestFields(OperateLogCreateReqDTO reqDTO) {
        HttpServletRequest request = ServletUtils.getRequest();
        if (request == null) {
            return;
        }
        reqDTO.setRequestMethod(request.getMethod());
        reqDTO.setRequestUrl(request.getRequestURI());
        reqDTO.setUserIp(ServletUtils.getClientIP(request));
        reqDTO.setUserAgent(ServletUtils.getUserAgent(request));
    }

    @Override
    public void record(LogRecord logRecord) {
        OperateLogCreateReqDTO reqDTO = new OperateLogCreateReqDTO();
        try {
            // 补充用户信息
            fillUserFields(reqDTO);
            // 补全模块信息
            fillModuleFields(reqDTO, logRecord);
            // 补全请求信息
            fillRequestFields(reqDTO);
            // 异步记录日志 远程调用能调用到调用这个地址就可以 http地址为： "common服务地址"/api/v1/operate-log/save-log
            ResponseEntity<JSONObject> stringResponseEntity = doRequest(logSaveUrl, HttpMethod.POST, JSONObject.parseObject(JSON.toJSONString(reqDTO)));
            log.info("日志记录返回结果：{}",stringResponseEntity.getBody());
//            operateLogFeignClient.saveLogAsync(JSON.parseObject(JSON.toJSONString(reqDTO), OperateLogCreateReqDTO.class));
        } catch (Throwable ex) {
            log.error("[record][url({}) log({}) 发生异常]", reqDTO.getRequestUrl(), reqDTO, ex);
        }
    }

    @Override
    public List<LogRecord> queryLog(String bizNo, String type) {
        throw new UnsupportedOperationException("使用 OperateLogApi 进行操作日志的查询");
    }

    @Override
    public List<LogRecord> queryLogByBizNo(String bizNo, String type, String subType) {
        throw new UnsupportedOperationException("使用 OperateLogApi 进行操作日志的查询");
    }
}
