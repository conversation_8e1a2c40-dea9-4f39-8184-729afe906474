package com.dib.index.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.common.utils.StringUtils;
import com.dib.index.domain.TIndicatorConnection;
import com.dib.index.domain.TIndicatorInfo;
import com.dib.index.domain.TIndicatorManage;
import com.dib.index.enums.DataLevelEnum;
import com.dib.index.enums.IndTypeEnum;
import com.dib.index.mapper.TIndicatorInfoMapper;
import com.dib.index.mapper.TIndicatorManageMapper;
import com.dib.index.service.TIndicatorService;
import com.dib.index.service.TIndicatorConnectionService;
import com.dib.index.utils.ReturnT;
import com.dib.index.vo.*;
import com.dib.metadata.entity.MetadataColumnEntity;
import com.dib.metadata.entity.MetadataTableEntity;
import com.dib.metadata.service.MetadataColumnService;
import com.dib.metadata.service.MetadataTableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
@Transactional
public class IndicatorServiceImpl extends ServiceImpl<TIndicatorInfoMapper, TIndicatorInfo> implements TIndicatorService {
    @Autowired
    private TIndicatorManageMapper  tIndicatorManageMapper;
    @Autowired
    private MetadataTableService metadataTableService;
    @Autowired
    private MetadataColumnService metadataColumnService;
    @Autowired
    private TIndicatorConnectionService tIndicatorConnectionService;

    @Override
    public List<TIndicatorInfo> ListByIndexType(Integer indexType) {

        // 参数校验
        if (indexType == null) {
            log.debug("Index type is null, returning an empty list.");
            return Collections.emptyList();
        }

        // 调用数据库查询方法
        List<TIndicatorInfo> result = list(new LambdaQueryWrapper<TIndicatorInfo>().eq(TIndicatorInfo::getIndType, indexType));

        // 额外校验：确保返回结果不为 null
        if (result == null) {
            log.warn("Result from database is null for indexType: {}");
            return Collections.emptyList();
        }
        return result;

    }

    @Override
    public List<TIndicatorInfo> ListByIndexLibId(Long indexLibId) {
        if (indexLibId == null) {
            log.debug("IndexLibId is null, returning an empty list.");
            return Collections.emptyList();
        }

        return list(new LambdaQueryWrapper<TIndicatorInfo>().eq(TIndicatorInfo::getIndicatorLibNo, indexLibId));
    }

    @Override
    public List<TIndicatorInfoVo> groupAndIndexBylibId(Long indexLibId) {
        if (indexLibId == null) {
            log.debug("IndexLibId is null, returning an empty list.");
            return Collections.emptyList();
        }

        List<TIndicatorInfoVo> result = new ArrayList<>();
        //先查分组
        LambdaQueryWrapper<TIndicatorManage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TIndicatorManage::getPid, indexLibId);
        queryWrapper.eq(TIndicatorManage::getDataLevel, DataLevelEnum.INDICATOR_GROUP.getDataLevel());
        List<TIndicatorManage> indicatorManageList = tIndicatorManageMapper.selectList(queryWrapper);

        //AtomicInteger atomicInt = new AtomicInteger(0);
        List<TIndicatorInfoVo> result1 = indicatorManageList.stream().map(indicatorManage -> {
            TIndicatorInfoVo indicatorInfoVo = new TIndicatorInfoVo();
            indicatorInfoVo.setId(indicatorManage.getId());
            indicatorInfoVo.setMark(indicatorManage.getMark());
            indicatorInfoVo.setTitle(indicatorManage.getTitleName());
            indicatorInfoVo.setDataLevel(indicatorManage.getDataLevel() + "");
            return indicatorInfoVo;
        }).collect(Collectors.toList());
        //再查指标
        LambdaQueryWrapper<TIndicatorInfo> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(TIndicatorInfo::getIndicatorLibNo, indexLibId)
                .notIn(TIndicatorInfo::getFieldType, "维度列","数据期列");
        List<TIndicatorInfo> tIndicatorInfos = list(queryWrapper1);

        List<TIndicatorInfoVo> result2 = tIndicatorInfos.stream().map(indicatorInfo -> {
            TIndicatorInfoVo indicatorInfoVo = new TIndicatorInfoVo();
            indicatorInfoVo.setId(indicatorInfo.getId());
            indicatorInfoVo.setTitle(StrUtil.isNotBlank(indicatorInfo.getTitle()) ? indicatorInfo.getTitle() : indicatorInfo.getAlias());
            indicatorInfoVo.setMark(indicatorInfo.getMark());
            indicatorInfoVo.setIndType(indicatorInfo.getIndType());
            indicatorInfoVo.setDataLevel(indicatorInfo.getDataLevel());
            indicatorInfoVo.setIndLength(indicatorInfo.getIndLength());
            indicatorInfoVo.setDecimalDigits(indicatorInfo.getDecimalDigits());
            indicatorInfoVo.setBusinessCaliber(indicatorInfo.getBusinessCaliber());
            indicatorInfoVo.setExpress(indicatorInfo.getExpress());
            indicatorInfoVo.setIndGroup(indicatorInfo.getIndGroup());
            return indicatorInfoVo;
        }).collect(Collectors.toList());

        result.addAll(result1);
        result.addAll(result2);
        return result;
    }

    @Override
    public ReturnT<Boolean> removeIndicatorInfoById(Long id) {
        ReturnT<Boolean> returnT = new ReturnT<>();
        TIndicatorInfo tIndicatorInfo = getById(id);
        if (tIndicatorInfo == null) {
            returnT.setMsg("没查询到指标信息！");
            returnT.setData(false);
            return returnT;
        }

        //查询是否存在关联该指标的指标信息
        List<TIndicatorInfo> relateList = list(new LambdaQueryWrapper<TIndicatorInfo>().eq(TIndicatorInfo::getRelatedIndicatorId, id));
        if (CollectionUtil.isNotEmpty(relateList)) {
            returnT.setMsg("存在与该指标关联的指标数据！");
            returnT.setData(false);
            return returnT;
        }
        //表关联信息
        String connectionId = tIndicatorInfo.getConnectionId();
        if (StrUtil.isNotBlank(connectionId)) {
            List<TIndicatorInfo> relateIndicatorInfoList = list(new LambdaQueryWrapper<TIndicatorInfo>().eq(TIndicatorInfo::getConnectionId, connectionId));
            if (CollectionUtil.isEmpty(relateIndicatorInfoList)) {
                //删除表关联信息
                LambdaQueryWrapper<TIndicatorConnection> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(TIndicatorConnection::getConnectionId, connectionId);
                tIndicatorConnectionService.remove(wrapper);
            }
        }

        removeById(id);
        return ReturnT.success(true);
    }

    @Override
    public ReturnT<Boolean> batchSaveAtom(MetadataEntityVo metadataEntityVo) {
        //分组id
        Long parentId = metadataEntityVo.getParentId();
        //指标信息
        List<TableEntityVo> tables = metadataEntityVo.getTables();
        //查询分组信息
        TIndicatorManage tIndicatorManage = tIndicatorManageMapper.selectById(parentId);

        List<String> tableIds = tables.stream().map(TableEntityVo::getTableId).distinct().collect(Collectors.toList());

        //查询表的列信息
        List<MetadataColumnEntity> metadataColumnList = metadataColumnService.list(new LambdaQueryWrapper<MetadataColumnEntity>().in(MetadataColumnEntity::getTableId, tableIds));
        List<MetadataTableEntity> tableList = metadataTableService.list(new LambdaQueryWrapper<MetadataTableEntity>().in(MetadataTableEntity::getId, tableIds));
        //表分组
        Map<String, MetadataTableEntity> tableMap = tableList.stream().collect(Collectors.toMap(MetadataTableEntity::getId, Function.identity()));
        //列分组
        Map<String, Map<String, MetadataColumnEntity>> tableColumnMap = metadataColumnList.stream().collect(Collectors.groupingBy(MetadataColumnEntity::getTableId, Collectors.toMap(MetadataColumnEntity::getColumnName, column -> column, (existing, replacement) -> existing // 处理相同 tableId 和 columnName 的情况，这里保留现有的
        )));
        //获取指标信息
        List<TIndicatorInfo> atomicIndicatorInfoList = getAtomicIndicatorInfoList(tables, tIndicatorManage, tableColumnMap, parentId);
        //获取表关联关系信息
        List<TIndicatorConnection> tIndicatorConnectionList = getIndicatorConnectionList(metadataEntityVo, tableMap);

        //设置指标的关联信息id
        atomicIndicatorInfoList.forEach(info -> {
            info.setConnectionId(tIndicatorConnectionList.get(0).getConnectionId());
            info.setSourceId(metadataEntityVo.getSourceId());
        });

        if (metadataEntityVo.getOldRowId() != null) {
            //编辑指标信息
            TIndicatorInfo tIndicatorInfo = getById(metadataEntityVo.getOldRowId());
            String connectionId = tIndicatorInfo.getConnectionId();
            //关联指标信息
            List<TIndicatorInfo> existInfoList = list(new LambdaQueryWrapper<TIndicatorInfo>().eq(TIndicatorInfo::getConnectionId, connectionId));
            //根据表id和字段名称分组
            //已存在的指标数据
            Map<String, Map<String, TIndicatorInfo>> existGroupMap = existInfoList.stream().collect(Collectors.groupingBy(TIndicatorInfo::getTableId, Collectors.toMap(TIndicatorInfo::getFieldName, Function.identity())));
            //新增的指标数据
            Map<String, Map<String, TIndicatorInfo>> insertGroupMap = atomicIndicatorInfoList.stream().collect(Collectors.groupingBy(TIndicatorInfo::getTableId, Collectors.toMap(TIndicatorInfo::getFieldName, Function.identity())));
            //筛选出新增、删除、编辑的指标
            List<TIndicatorInfo> insert = new ArrayList<>();
            List<TIndicatorInfo> delete = new ArrayList<>();
            List<TIndicatorInfo> edit = new ArrayList<>();
            for (String tableId : insertGroupMap.keySet()) {
                if (existGroupMap.containsKey(tableId)) {
                    Map<String, TIndicatorInfo> insertMap = insertGroupMap.get(tableId);
                    Map<String, TIndicatorInfo> existMap = existGroupMap.get(tableId);
                    for (String fieldName : insertMap.keySet()) {
                        if (existMap.containsKey(fieldName)) {
                            //编辑
                            TIndicatorInfo info = insertMap.get(fieldName);
                            info.setId(existMap.get(fieldName).getId());
                            edit.add(info);
                            existMap.remove(fieldName);
                        } else {
                            insert.add(insertMap.get(fieldName));
                        }
                    }
                    if (CollectionUtil.isNotEmpty(existMap)) {
                        //删除
                        delete.addAll(existMap.values());
                    }
                } else {
                    //新增
                    insert.addAll(insertGroupMap.get(tableId).values());
                }
            }

            if (CollectionUtil.isNotEmpty(delete)) {
                //删除指标
                List<Long> deleteIds = delete.stream().map(TIndicatorInfo::getId).collect(Collectors.toList());
                //判断删除的指标有没有被其他指标引用关联
                List<TIndicatorInfo> relateInfos = list(new LambdaQueryWrapper<TIndicatorInfo>().in(TIndicatorInfo::getRelatedIndicatorId, deleteIds));
                if (CollectionUtil.isNotEmpty(relateInfos)) {
                    log.error("存在关联的指标信息，无法删除");
                    return ReturnT.fail("存在关联的指标信息，无法删除");
                }
                removeByIds(delete.stream().map(TIndicatorInfo::getId).collect(Collectors.toList()));
            }
            atomicIndicatorInfoList.clear();
            atomicIndicatorInfoList.addAll(insert);
            atomicIndicatorInfoList.addAll(edit);

            //删除旧的关联信息
            tIndicatorConnectionService.remove(new LambdaQueryWrapper<TIndicatorConnection>().eq(TIndicatorConnection::getConnectionId, connectionId));
        }

        //保存关联信息
        tIndicatorConnectionService.saveBatch(tIndicatorConnectionList);
        //批量保存指标信息
        saveOrUpdateBatch(atomicIndicatorInfoList);

        return ReturnT.success(true);
    }

    private List<TIndicatorConnection> getIndicatorConnectionList(MetadataEntityVo metadataEntityVo, Map<String, MetadataTableEntity> tableMap) {
        List<TIndicatorConnection> tIndicatorConnectionList = new ArrayList<>();
        if (CollectionUtil.isEmpty(tableMap)) {
            return tIndicatorConnectionList;
        }

        List<TableEntityVo> tables = metadataEntityVo.getTables();
        List<ConnectionEntityVo> connections = metadataEntityVo.getConnections();

        String connectionId = IdUtil.simpleUUID();
        if (CollectionUtil.isEmpty(connections)) {
            //单表
            MetadataTableEntity metadataTable = tableMap.get(tables.get(0).getTableId());
            TIndicatorConnection tIndicatorConnection = new TIndicatorConnection();
            tIndicatorConnection.setConnectionId(connectionId);
            tIndicatorConnection.setSourceId(metadataTable.getSourceId());
            tIndicatorConnection.setLeftTableId(metadataTable.getId());
            tIndicatorConnection.setLeftTableColumns(tables.stream().map(TableEntityVo::getName).collect(Collectors.toList()));
            tIndicatorConnection.setCreateDate(new Date());
            tIndicatorConnectionList.add(tIndicatorConnection);
        } else {
            for (ConnectionEntityVo connection : connections) {
                MetadataTableEntity leftTable = tableMap.get(connection.getSourceTableId());
                MetadataTableEntity rightTable = tableMap.get(connection.getTargetTableId());
                List<String> leftSelectColumnList = tables.stream().filter(t -> ObjectUtil.equal(t.getTableId(), connection.getSourceTableId())).map(TableEntityVo::getName).collect(Collectors.toList());
                List<String> rightSelectColumnList = tables.stream().filter(t -> ObjectUtil.equal(t.getTableId(), connection.getTargetTableId())).map(TableEntityVo::getName).collect(Collectors.toList());
                TIndicatorConnection tIndicatorConnection = new TIndicatorConnection();
                tIndicatorConnection.setConnectionId(connectionId);
                tIndicatorConnection.setSourceId(leftTable.getSourceId());
                tIndicatorConnection.setLeftTableId(leftTable.getId());
                tIndicatorConnection.setRightTableId(rightTable.getId());
                tIndicatorConnection.setConnectionType(connection.getJoinType());
                tIndicatorConnection.setLeftTableColumns(leftSelectColumnList);
                tIndicatorConnection.setRightTableColumns(rightSelectColumnList);
                List<SettingEntityVo> settings = connection.getSettings();
                List<List<String>> connectionColumns = new ArrayList<>();
                for (SettingEntityVo setting : settings) {
                    List<String> columns = new ArrayList<>(2);
                    columns.add(setting.getLeftColumn());
                    columns.add(setting.getRightColumn());
                    connectionColumns.add(columns);
                }
                tIndicatorConnection.setConnectionColumns(connectionColumns);
                tIndicatorConnection.setCreateDate(new Date());
                tIndicatorConnectionList.add(tIndicatorConnection);
            }
        }

        return tIndicatorConnectionList;
    }

    /**
     * 获取原子指标信息列表
     *
     * @param tables           表实体对象列表
     * @param tIndicatorManage 指标管理对象
     * @param tableColumnMap   表字段映射
     * @param parentId         父级标识
     * @return 原子指标信息列表
     */
    private List<TIndicatorInfo> getAtomicIndicatorInfoList(List<TableEntityVo> tables, TIndicatorManage tIndicatorManage, Map<String, Map<String, MetadataColumnEntity>> tableColumnMap, Long parentId) {
        List<TIndicatorInfo> tIndicatorInfoList = new ArrayList<>();
        String batchNo = IdUtil.getSnowflakeNextIdStr();
        for (TableEntityVo table : tables) {
            TIndicatorInfo tIndicatorInfo = new TIndicatorInfo();
            tIndicatorInfo.setId(table.getId());
            tIndicatorInfo.setTitle(table.getTitle());
            tIndicatorInfo.setFieldName(table.getName());
            tIndicatorInfo.setAlias(table.getAlias());
            tIndicatorInfo.setIndType(IndTypeEnum.ATOMIC_INDEX.getType());
            tIndicatorInfo.setIndGroup(tIndicatorManage != null ? tIndicatorManage.getTitleName() : "");
            tIndicatorInfo.setDataLevel(table.getType());
            tIndicatorInfo.setTableId(table.getTableId());
            tIndicatorInfo.setMark(table.getMark());
            Map<String, MetadataColumnEntity> columnMap = tableColumnMap.get(table.getTableId());
            if (columnMap != null && columnMap.containsKey(table.getName())) {
                MetadataColumnEntity metadataColumn = columnMap.get(table.getName());
                String dataLength = metadataColumn.getDataLength();
                String dataScale = metadataColumn.getDataScale();
                if (NumberUtil.isNumber(dataLength)) {
                    tIndicatorInfo.setIndLength(Integer.parseInt(dataLength));
                }
                if (NumberUtil.isNumber(dataScale)) {
                    tIndicatorInfo.setDecimalDigits(Integer.parseInt(dataScale));
                }
                tIndicatorInfo.setColNullable(metadataColumn.getColumnNullable());
            }
            tIndicatorInfo.setIndicatorLibNo(parentId);
            tIndicatorInfo.setFieldType(table.getColumnType());
            tIndicatorInfo.setInnerDomainTable(table.getInnerdomaintable());
            tIndicatorInfo.setDataDateType(table.getDataDateType());
            tIndicatorInfo.setModifyDate(new Date());
            tIndicatorInfo.setBatchNo(batchNo);
            tIndicatorInfoList.add(tIndicatorInfo);
        }

        return tIndicatorInfoList;
    }

    @Override
    public TIndicatorInfoVo getIndicatorInfoById(Long id) {
        TIndicatorInfo tIndicatorInfo = getById(id);
        if (tIndicatorInfo == null) {
            return null;
        }

        TIndicatorInfoVo tIndicatorInfoVo = new TIndicatorInfoVo();
        BeanUtil.copyProperties(tIndicatorInfo, tIndicatorInfoVo);
        tIndicatorInfoVo.setPid(tIndicatorInfo.getIndicatorLibNo());
        if (StrUtil.isBlank(tIndicatorInfo.getTitle())) {
            tIndicatorInfoVo.setTitle(tIndicatorInfo.getAlias());
        }
        Map<String, MetadataTableEntity> tableMap = new HashMap<>();
        //查询表信息
        if (StrUtil.isNotBlank(tIndicatorInfo.getTableId())) {
            MetadataTableEntity metadataTableEntity = metadataTableService.getById(tIndicatorInfo.getTableId());
            if (metadataTableEntity != null) {
                tIndicatorInfoVo.setTableName(metadataTableEntity.getTableName());
                tableMap.put(tIndicatorInfo.getTableId(), metadataTableEntity);
            }
        }

        if (IndTypeEnum.getByType(tIndicatorInfo.getIndType()) == IndTypeEnum.ATOMIC_INDEX && StrUtil.isNotBlank(tIndicatorInfo.getConnectionId())) {
            //原子指标需要获取表关联信息
            //查询关联信息
            LambdaQueryWrapper<TIndicatorConnection> connectionWrapper = new LambdaQueryWrapper<>();
            connectionWrapper.eq(TIndicatorConnection::getConnectionId, tIndicatorInfo.getConnectionId());
            List<TIndicatorConnection> conlist = tIndicatorConnectionService.list(connectionWrapper);
            if (CollectionUtil.isNotEmpty(conlist)) {
                Set<String> tableIds = new HashSet<>(tableMap.keySet());
                for (TIndicatorConnection connection : conlist) {
                    tableIds.add(connection.getLeftTableId());
                    tableIds.add(connection.getRightTableId());
                }
                tableIds.remove(tIndicatorInfo.getTableId());
                //查询表信息
                List<MetadataTableEntity> tables = metadataTableService.listByIds(tableIds);
                if (CollectionUtil.isNotEmpty(tables)) {
                    Map<String, MetadataTableEntity> map = tables.stream().collect(Collectors.toMap(MetadataTableEntity::getId, Function.identity(), (key1, key2) -> key2));
                    tableMap.putAll(map);
                }

                List<TIndicatorConnectionVo> connections = new ArrayList<>();
                for (TIndicatorConnection connection : conlist) {
                    TIndicatorConnectionVo connectionVo = new TIndicatorConnectionVo();
                    connectionVo.setId(connection.getId());
                    connectionVo.setConnectionId(tIndicatorInfo.getConnectionId());
                    connectionVo.setSourceId(connection.getSourceId());
                    connectionVo.setLeftTableId(connection.getLeftTableId());
                    connectionVo.setRightTableId(connection.getRightTableId());
                    connectionVo.setConnectionType(connection.getConnectionType());
                    connectionVo.setLeftSelectColumns(connection.getLeftTableColumns());
                    connectionVo.setRightSelectColumns(connection.getRightTableColumns());
                    List<SettingEntityVo> settings = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(connection.getConnectionColumns())) {
                        for (List<String> column : connection.getConnectionColumns()) {
                            SettingEntityVo setting = new SettingEntityVo();
                            if (tableMap.containsKey(connection.getLeftTableId())) {
                                setting.setLeftTable(tableMap.get(connection.getLeftTableId()).getTableName());
                            }
                            if (tableMap.containsKey(connection.getRightTableId())) {
                                setting.setRightTable(tableMap.get(connection.getRightTableId()).getTableName());
                            }
                            setting.setLeftColumn(column.get(0));
                            setting.setRightColumn(column.get(1));
                            settings.add(setting);
                        }
                    }
                    connectionVo.setSettings(settings);
                    connections.add(connectionVo);
                }
                tIndicatorInfoVo.setConnections(connections);

                //查询关联指标信息
                LambdaQueryWrapper<TIndicatorInfo> indicatorWrapper = new LambdaQueryWrapper<>();
                indicatorWrapper.eq(TIndicatorInfo::getConnectionId, tIndicatorInfo.getConnectionId());
                List<TIndicatorInfo> relateList = list(indicatorWrapper);
                List<TIndicatorInfoVo> relateIndicatorList = new ArrayList<>();
                for (TIndicatorInfo indicatorInfo : relateList) {
                    TIndicatorInfoVo indicatorInfoVo = new TIndicatorInfoVo();
                    BeanUtil.copyProperties(indicatorInfo, indicatorInfoVo);
                    if (StrUtil.isBlank(indicatorInfoVo.getTitle())) {
                        indicatorInfoVo.setTitle(indicatorInfoVo.getAlias());
                    }
                    if (indicatorInfo.getTableId() != null && tableMap.containsKey(indicatorInfo.getTableId())) {
                        indicatorInfoVo.setTableName(tableMap.get(indicatorInfo.getTableId()).getTableName());
                    }
                    indicatorInfoVo.setNullable("1".equals(indicatorInfo.getColNullable()) ? "true" : "false");
                    relateIndicatorList.add(indicatorInfoVo);
                }

                tIndicatorInfoVo.setRelatedIndicators(relateIndicatorList);
            }
        }
        return tIndicatorInfoVo;
    }

    @Override
    public boolean saveDerived(TIndicatorInfoVo indicatorInfoVo) {
        if (indicatorInfoVo == null || indicatorInfoVo.getRelatedIndicatorId() == null) {
            return false;
        }
        //保存指标信息
        TIndicatorInfo tIndicatorInfo = BeanUtil.copyProperties(indicatorInfoVo, TIndicatorInfo.class);
        tIndicatorInfo.setIndType(IndTypeEnum.DERIVED_INDEX.getType());
        tIndicatorInfo.setIndGroup(indicatorInfoVo.getIndGroup());
        Map<String, String> businessLimit = new HashMap<>();
        businessLimit.put("行政区划", indicatorInfoVo.getBusinessLimitArea());
        businessLimit.put("机构名称", indicatorInfoVo.getBusinessLimitInsName());
        tIndicatorInfo.setBusinessLimit(businessLimit);
        tIndicatorInfo.setIndicatorLibNo(indicatorInfoVo.getPid());

        return saveOrUpdate(tIndicatorInfo);
    }

    @Override
    public boolean saveComplex(TIndicatorInfoVo indicatorInfoVo) {
        //保存指标信息
        TIndicatorInfo tIndicatorInfo = BeanUtil.copyProperties(indicatorInfoVo, TIndicatorInfo.class);
        tIndicatorInfo.setIndType(IndTypeEnum.COMPOSITE_INDEX.getType());
        tIndicatorInfo.setIndicatorLibNo(indicatorInfoVo.getPid());
        tIndicatorInfo.setIndGroup(indicatorInfoVo.getIndGroup());
        return saveOrUpdate(tIndicatorInfo);
    }

    @Override
    public List<TIndicatorInfo> queryDimColumnInfo(List<String> indicatorIds) {

        if (CollectionUtil.isNotEmpty(indicatorIds)) {
            List<Long> indicatorIdList = indicatorIds.stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            QueryWrapper<TIndicatorInfo> wrapper = new QueryWrapper<>();
            wrapper.in("id", indicatorIdList);
            List<TIndicatorInfo> indicatorInfos = baseMapper.selectList(wrapper);
            List<String> batchNos = indicatorInfos.stream().map(zy -> zy.getBatchNo()).distinct().collect(Collectors.toList());
            wrapper.clear();
            List<TIndicatorInfo> indicatorInfoNew = new ArrayList<>();
            if(batchNos != null && batchNos.size() > 0) {
                 indicatorInfoNew = baseMapper.selectList(wrapper.in("batch_no", String.join(",", batchNos))).stream().filter(indicatorInfo -> StringUtils.isNotEmpty(indicatorInfo.getFieldType()) && indicatorInfo.getFieldType().equals("维度列")).collect(Collectors.toList());
            }
            return indicatorInfoNew;
        }
        return null;
    }

    @Override
    public List<TIndicatorInfo> queryDimIndictorInfo(List<String> ids) {
        if (CollectionUtil.isNotEmpty(ids)) {
            List<Long> indIdList = ids.stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            QueryWrapper<TIndicatorInfo> wrapper = new QueryWrapper<>();
            wrapper.in("id", indIdList);
            List<TIndicatorInfo> indicatorInfos = baseMapper.selectList(wrapper);
            return indicatorInfos;
        }
        return null;
    }

}