package com.dib.index.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.common.core.domain.AjaxResult;
import com.dib.common.database.constants.DbType;
import com.dib.core.util.DateUtil;
import com.dib.index.TDimBaseRelaColVo;
import com.dib.index.domain.*;
import com.dib.index.enums.DimTreeType;
import com.dib.index.enums.DimTypeEnum;
import com.dib.index.mapper.*;
import com.dib.index.service.TDimAttributeSettingService;
import com.dib.index.service.TDimBaseInfoService;
import com.dib.index.service.TDimColumnSettingService;
import com.dib.index.service.TDimDrillPathService;
import com.dib.index.utils.ReturnT;
import com.dib.index.vo.*;
import com.dib.metadata.dto.MetadataColumnMarketDto;
import com.dib.metadata.dto.MetadataCreateSqlDto;
import com.dib.metadata.dto.SqlConsoleDto;
import com.dib.metadata.entity.MetadataColumnEntity;
import com.dib.metadata.entity.MetadataSourceEntity;
import com.dib.metadata.entity.MetadataTableEntity;
import com.dib.metadata.service.*;
import com.dib.metadata.vo.SqlConsoleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TDimBaseInfoServiceImpl extends ServiceImpl<TDimBaseInfoMapper, TDimBaseInfo> implements TDimBaseInfoService {
    @Autowired
    private MetadataCreateDataBaseAndTableService metadataCreateTableService;
    @Autowired
    private MetadataSourceService metadataSourceService;
    @Autowired
    private MetadataTableService metadataTableService;
    @Autowired
    private MetadataColumnService metadataColumnService;
    @Autowired
    private TDimAttributeSettingService tDimAttributeSettingService;
    @Autowired
    private TDimDrillPathService tDimDrillPathService;
    @Autowired
    private SqlConsoleService sqlConsoleService;
    @Autowired
    private TDimColumnSettingService tDimColumnSettingService;


    @Override
    public List<TDimBaseInfoVo> listAllDimBaseInfo(Long dimGroupId) {
        List<TDimBaseInfoVo> tDimBaseInfoVos = null;
        LambdaQueryWrapper<TDimBaseInfo> queryWrapper = new LambdaQueryWrapper<>();
        if (dimGroupId != null && dimGroupId > 0) {
            queryWrapper.eq(TDimBaseInfo::getDimGroupId, dimGroupId);
        }
        List<TDimBaseInfo> tDimBaseInfos = list(queryWrapper);
        if (CollectionUtil.isNotEmpty(tDimBaseInfos)) {
            tDimBaseInfoVos = BeanUtil.copyToList(tDimBaseInfos, TDimBaseInfoVo.class);
        }
        return tDimBaseInfoVos;
    }

    @Override
    public TDimBaseInfoVo getDimGroupInfo(Long id) {
        TDimBaseInfo group = getOne(new LambdaQueryWrapper<TDimBaseInfo>().eq(TDimBaseInfo::getId, id).eq(TDimBaseInfo::getDimType, DimTypeEnum.DIM_GROUP.getType()));
        return BeanUtil.copyProperties(group, TDimBaseInfoVo.class);
    }

    @Transactional
    public boolean createOrUpdateDimGroup(TDimBaseInfoVo tDimBaseInfoVo) {
        TDimBaseInfo tDimBaseInfo = BeanUtil.copyProperties(tDimBaseInfoVo, TDimBaseInfo.class);
        tDimBaseInfo.setDimType(DimTypeEnum.DIM_GROUP.getType());
        return saveOrUpdate(tDimBaseInfo);
    }

    @Override
    @Transactional
    public ReturnT<String> createOrUpdateDimBaseInfo(TDimBaseInfoVo tDimBaseInfoVo) {
        DimTreeType dimTreeType = null;
        if (tDimBaseInfoVo == null || StrUtil.isBlank(tDimBaseInfoVo.getSourceId()) || StrUtil.isBlank(tDimBaseInfoVo.getIdField()) || StrUtil.isBlank(tDimBaseInfoVo.getTextField()) || (dimTreeType = DimTreeType.getByType(tDimBaseInfoVo.getTreeType())) == null) {
            return ReturnT.fail("参数错误");
        }

        //验证一些关联的参数
        if (dimTreeType == DimTreeType.SPILT) {
            String split = tDimBaseInfoVo.getSplitInfo();
            if (StrUtil.isNotBlank(split)) {
                List<String> stringList = StrUtil.split(split, "-");
                if (!stringList.stream().allMatch(NumberUtil::isNumber) || stringList.stream().allMatch("0"::equals) || stringList.stream().skip(1).anyMatch("0"::equals)) {
                    return ReturnT.fail("分段信息格式错误");
                }
            } else {
                return ReturnT.fail("分段信息不能为空");
            }
        }
        if (dimTreeType == DimTreeType.Drill) {
            String drillPath = tDimBaseInfoVo.getDrillPath();
            if (StrUtil.isBlank(drillPath) || CollectionUtil.isEmpty(tDimBaseInfoVo.getAttributeSettings())) {
                return ReturnT.fail("钻取信息不能为空");
            }
            List<String> attributeList = StrUtil.split(drillPath, "-");
            List<String> attributeNameList = tDimBaseInfoVo.getAttributeSettings().stream().map(TDimAttributeSettingVo::getAttributeName).distinct().collect(Collectors.toList());
            if (!CollectionUtil.containsAll(attributeNameList, attributeList)) {
                return ReturnT.fail("钻取路径中的属性不存在");
            }
        }

        TDimBaseInfo tDimBaseInfo = BeanUtil.copyProperties(tDimBaseInfoVo, TDimBaseInfo.class);
        tDimBaseInfo.setDimType(DimTypeEnum.NORMAL_DIM.getType());

        if (tDimBaseInfoVo.getId() != null) {
            //查询是否存在维度信息
            TDimBaseInfo info = getById(tDimBaseInfoVo.getId());
            if (info == null) {
                return ReturnT.fail("维度信息不存在");
            }
        }

        //连接池id
        String sourceId = tDimBaseInfo.getSourceId();
        //表id
        String tableId = tDimBaseInfo.getTableId();
        //表名称
        String tableName = tDimBaseInfo.getTableName();
        //ID字段名称
        String idField = tDimBaseInfoVo.getIdField();
        //文本字段名称
        String textField = tDimBaseInfoVo.getTextField();
        //字段属性列表处理
        List<TDimColumnSettingVo> columnSettings = tDimBaseInfoVo.getColumnSettings();

        if (CollectionUtil.isEmpty(columnSettings)) {
            //字段为空，不允许保存
            return ReturnT.fail("字段设置为空，不允许保存");
        }

        //查询数据库信息、表信息和列信息
        MetadataSourceEntity source = metadataSourceService.getById(sourceId);
        if (source == null) {
            return ReturnT.fail("数据源不存在");
        }
        MetadataTableEntity table = null;
        if (StrUtil.isNotBlank(tDimBaseInfo.getTableId())) {
            table = metadataTableService.getById(tableId);
            if (table == null) {
                return ReturnT.fail("表信息不存在");
            }
        } else {
            //新建表实体
            table = new MetadataTableEntity();
            table.setTableName(tDimBaseInfo.getTableName());
            table.setTableComment(StrUtil.format("维表-{}-{}", tDimBaseInfo.getDimName(), DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
        }

        MetadataCreateSqlDto metadataCreateSqlDto = createMetaDataCreateSqlDto(source, table, idField, sourceId);
        //判断是新建表还是修改旧表
        if (StrUtil.isNotBlank(sourceId) && StrUtil.isBlank(tableId)) {
            //新建维表
            metadataCreateSqlDto.setSqlType("createTable");
            List<MetadataColumnMarketDto> metadataColumnMarketDtoList = new ArrayList<>();
            for (int i = 0; i < columnSettings.size(); i++) {
                TDimColumnSettingVo columnSetting = columnSettings.get(i);
                MetadataColumnMarketDto metadataColumnMarketDto = createMetadataColumnMarketDto(sourceId, columnSetting, idField, i, source, table);
                metadataColumnMarketDto.setActionType("addColumn");
                metadataColumnMarketDto.setUpdateKeys(CollectionUtil.newArrayList("columnName", "columnComment", "dataLength", "dataScale", "fieldType"));
                metadataColumnMarketDtoList.add(metadataColumnMarketDto);
            }
            metadataCreateSqlDto.setMetadataColumnMarketDtoList(metadataColumnMarketDtoList);
        } else {
            //修改原表
            //查询表的字段数据
            List<MetadataColumnEntity> columns = metadataColumnService.list(new LambdaQueryWrapper<MetadataColumnEntity>().eq(MetadataColumnEntity::getSourceId, sourceId).eq(MetadataColumnEntity::getTableId, tableId));
            //根据列id分组
            Map<String, MetadataColumnEntity> oldColumnMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(columns)) {
                oldColumnMap.putAll(columns.stream().collect(Collectors.toMap(MetadataColumnEntity::getId, Function.identity())));
            }
            metadataCreateSqlDto.setSqlType("updateTable");
            List<MetadataColumnMarketDto> metadataColumnMarketDtoList = new ArrayList<>();
            for (int i = 0; i < columnSettings.size(); i++) {
                TDimColumnSettingVo columnSetting = columnSettings.get(i);
                MetadataColumnMarketDto metadataColumnMarketDto = createMetadataColumnMarketDto(sourceId, columnSetting, idField, i, source, table);
                metadataColumnMarketDto.setUpdateKeys(CollectionUtil.newArrayList("columnName", "columnComment", "columnPosition", "dataLength", "dataScale", "fieldType"));
                if (StrUtil.isNotBlank(columnSetting.getId())) {
                    if (oldColumnMap.containsKey(columnSetting.getId())) {
                        //判断有没有进行修改
                        MetadataColumnEntity oldColumn = oldColumnMap.get(columnSetting.getId());
                        if(Objects.equals(oldColumn.getColumnName(), metadataColumnMarketDto.getColumnName()) &&
                        Objects.equals(oldColumn.getColumnComment(),  metadataColumnMarketDto.getColumnComment()) &&
                        Objects.equals(oldColumn.getDataLength(), metadataColumnMarketDto.getDataLength()) &&
                        Objects.equals(oldColumn.getDataScale(), metadataColumnMarketDto.getDataScale()) &&
                        Objects.equals(oldColumn.getFieldType(), metadataColumnMarketDto.getFieldType())) {
                            //都一样，不需要对改字段进行修改
                            continue;
                        }
                        if(!Objects.equals(oldColumn.getColumnName(), metadataColumnMarketDto.getColumnName())) {
                            //修改字段名字需要传字段id
                            metadataColumnMarketDto.setId(oldColumn.getId());
                        }
                        metadataColumnMarketDto.setActionType("updateColumn");
                        oldColumnMap.remove(columnSetting.getId());
                    } else {
                        return ReturnT.fail("修改的字段不存在，id：" + columnSetting.getId());
                    }
                } else {
                    //新建
                    metadataColumnMarketDto.setActionType("addColumn");
                }

                metadataColumnMarketDtoList.add(metadataColumnMarketDto);
            }

            //剩下的都是删除的字段
            if (CollectionUtil.isNotEmpty(oldColumnMap)) {
                for (MetadataColumnEntity metadataColumnEntity : oldColumnMap.values()) {
                    TDimColumnSettingVo columnSetting = createTdimColumnSettingVo(metadataColumnEntity, tDimBaseInfo);
                    MetadataColumnMarketDto metadataColumnMarketDto = createMetadataColumnMarketDto(sourceId, columnSetting, idField, 999, source, table);
                    metadataColumnMarketDto.setActionType("delColumn");
                    metadataColumnMarketDto.setUpdateKeys(CollectionUtil.newArrayList());
                    metadataColumnMarketDtoList.add(metadataColumnMarketDto);
                }
            }
            metadataCreateSqlDto.setMetadataColumnMarketDtoList(metadataColumnMarketDtoList);
        }

        //生成sql
        String sql = metadataCreateTableService.createSql(metadataCreateSqlDto);
        metadataCreateSqlDto.setTableSql(sql);
        //执行sql
        metadataCreateTableService.runSql(metadataCreateSqlDto);
        if(StrUtil.isNotBlank(sourceId) && StrUtil.isBlank(tableId)) {
            //查询新表id
            table = metadataTableService.getOne(new LambdaQueryWrapper<MetadataTableEntity>().eq(MetadataTableEntity::getSourceId, sourceId).eq(MetadataTableEntity::getTableName, tableName));
            tableId = table.getId();
            tDimBaseInfo.setTableId(tableId);
        }
        //获取ID字段和文本字段的id
        List<MetadataColumnEntity> columnEntityList = metadataColumnService.list(new LambdaQueryWrapper<MetadataColumnEntity>().eq(MetadataColumnEntity::getSourceId, sourceId).eq(MetadataColumnEntity::getTableId, tableId));
        Map<String, MetadataColumnEntity> columnEntityMap = columnEntityList.stream().collect(Collectors.toMap(MetadataColumnEntity::getColumnName, Function.identity()));
        //保存列id
        tDimBaseInfo.setIdFieldColumnId(columnEntityMap.get(idField.toUpperCase()).getId());
        tDimBaseInfo.setTextFieldColumnId(columnEntityMap.get(textField.toUpperCase()).getId());
        //创建或更新维度信息
        saveOrUpdate(tDimBaseInfo);

        //属性设置列表入库
        List<TDimAttributeSettingVo> attributeSettings = tDimBaseInfoVo.getAttributeSettings();
        if (CollectionUtil.isNotEmpty(attributeSettings)) {
            //vo转实体
            List<TDimAttributeSetting> insertList = attributeSettings.stream().map(item -> {
                TDimAttributeSetting tDimAttributeSetting = BeanUtil.copyProperties(item, TDimAttributeSetting.class);
                //设置维度id，属性列id，文字列id
                tDimAttributeSetting.setDimNo(tDimBaseInfo.getId());
                tDimAttributeSetting.setAttributeColumnId(columnEntityMap.get(item.getAttributeColumn().toUpperCase()).getId());
                tDimAttributeSetting.setTextColumnId(columnEntityMap.get(item.getTextColumn().toUpperCase()).getId());
                return tDimAttributeSetting;
            }).collect(Collectors.toList());
            List<Long> ids = insertList.stream().map(TDimAttributeSetting::getId).filter(Objects::nonNull).collect(Collectors.toList());
            //移除被删除掉的数据
            LambdaQueryWrapper<TDimAttributeSetting> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TDimAttributeSetting::getDimNo, tDimBaseInfo.getId());
            if (CollectionUtil.isNotEmpty(ids)) {
                wrapper.notIn(TDimAttributeSetting::getId, ids);
            }
            tDimAttributeSettingService.remove(wrapper);
            //保存或更新属性设置列表
            tDimAttributeSettingService.saveOrUpdateBatch(insertList);
        } else {
            //属性设置列表为空，但是传进来的维度id不为空，则表示在编辑时删除了全部属性设置
            if (tDimBaseInfoVo.getId() != null) {
                tDimAttributeSettingService.remove(new LambdaQueryWrapper<TDimAttributeSetting>().eq(TDimAttributeSetting::getDimNo, tDimBaseInfo.getId()));
            }
        }

        //钻取路径列表入库
        List<TDimDrillPathVo> drillPaths = tDimBaseInfoVo.getDrillPaths();
        if (CollectionUtil.isNotEmpty(drillPaths)) {
            //vo转实体
            List<TDimDrillPath> insertList = drillPaths.stream().map(item -> BeanUtil.copyProperties(item, TDimDrillPath.class)).peek(item -> item.setDimNo(tDimBaseInfo.getId())).collect(Collectors.toList());
            List<Long> ids = insertList.stream().map(TDimDrillPath::getId).filter(Objects::nonNull).collect(Collectors.toList());
            //移除被删除掉的数据
            LambdaQueryWrapper<TDimDrillPath> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TDimDrillPath::getDimNo, tDimBaseInfo.getId());
            if (CollectionUtil.isNotEmpty(ids)) {
                wrapper.notIn(TDimDrillPath::getId, ids);
            }
            tDimDrillPathService.remove(wrapper);
            //保存或更新属性设置列表
            tDimDrillPathService.saveOrUpdateBatch(insertList);
        } else {
            //钻取路径列表为空，但是传进来的维度id不为空，则表示编辑时删除了全部钻取路径
            if (tDimBaseInfo.getId() != null) {
                tDimDrillPathService.remove(new LambdaQueryWrapper<TDimDrillPath>().eq(TDimDrillPath::getDimNo, tDimBaseInfo.getId()));
            }
        }

        return ReturnT.success(tDimBaseInfo.getId() + "");
    }

    private TDimColumnSettingVo createTdimColumnSettingVo(MetadataColumnEntity metadataColumnEntity, TDimBaseInfo tDimBaseInfo) {
        TDimColumnSettingVo tDimColumnSettingVo = new TDimColumnSettingVo();
        tDimColumnSettingVo.setId(metadataColumnEntity.getId());
        tDimColumnSettingVo.setColumnName(metadataColumnEntity.getColumnName());
        tDimColumnSettingVo.setColumnType(metadataColumnEntity.getDataType().toUpperCase());
        tDimColumnSettingVo.setColumnComment(metadataColumnEntity.getColumnComment());
        tDimColumnSettingVo.setLength(metadataColumnEntity.getDataLength());
        if (StrUtil.isNotBlank(metadataColumnEntity.getDataPrecision())) {
            tDimColumnSettingVo.setDecimalDigits(metadataColumnEntity.getDataPrecision());
        }
        tDimColumnSettingVo.setEnablePk("1".equals(metadataColumnEntity.getColumnKey()));
        tDimColumnSettingVo.setDimNo(tDimBaseInfo.getId() + "");
        tDimColumnSettingVo.setFieldType(metadataColumnEntity.getFieldType() + "");
        return tDimColumnSettingVo;
    }

    private MetadataCreateSqlDto createMetaDataCreateSqlDto(MetadataSourceEntity source, MetadataTableEntity table, String columnKey, String sourceId) {
        MetadataCreateSqlDto metadataCreateSqlDto = new MetadataCreateSqlDto();
        metadataCreateSqlDto.setTargetId(source.getId());
        metadataCreateSqlDto.setTargetSchemaName(source.getDbSchema().getSid());
        metadataCreateSqlDto.setTargetTableName(table.getTableName());
        metadataCreateSqlDto.setSourceTableRemarks(table.getTableComment());
        metadataCreateSqlDto.setTargetPrimaryKeys(CollectionUtil.newArrayList(columnKey.toUpperCase()));
        metadataCreateSqlDto.setSourceIds(CollectionUtil.newHashSet(sourceId));
        return metadataCreateSqlDto;
    }

    private MetadataColumnMarketDto createMetadataColumnMarketDto(String sourceId, TDimColumnSettingVo columnSetting, String columnKey, int index, MetadataSourceEntity source, MetadataTableEntity table) {
        MetadataColumnMarketDto metadataColumnMarketDto = new MetadataColumnMarketDto();
        metadataColumnMarketDto.setSourceId(sourceId);
        metadataColumnMarketDto.setTableId(table.getId());
        metadataColumnMarketDto.setColumnName(" "+columnSetting.getColumnName());
        metadataColumnMarketDto.setColumnComment(columnSetting.getColumnComment());
        metadataColumnMarketDto.setColumnKey(Objects.equals(columnSetting.getColumnName().toUpperCase(), columnKey.toUpperCase()) ? "1" : "0");
        metadataColumnMarketDto.setColumnNullable(Objects.equals(columnSetting.getColumnName().toUpperCase(), columnKey.toUpperCase()) ? "0" : "1");
        metadataColumnMarketDto.setColumnPosition(index);
        metadataColumnMarketDto.setDataType(columnSetting.getColumnType().toUpperCase());
        metadataColumnMarketDto.setDataLength(columnSetting.getLength());
        metadataColumnMarketDto.setDataPrecision(null);
        metadataColumnMarketDto.setDataScale(columnSetting.getDecimalDigits());
        metadataColumnMarketDto.setDataDefault(null);
        metadataColumnMarketDto.setSourceName(source.getSourceName());
        metadataColumnMarketDto.setTableName(table.getTableName());
        metadataColumnMarketDto.setTableComment(table.getTableComment());
        //metadataColumnMarketDto.setFieldType(Integer.parseInt(columnSetting.getFieldType()));
        metadataColumnMarketDto.setAutoIncrement(false);
        return metadataColumnMarketDto;
    }

    @Override
    public boolean updateDimBaseInfo(TDimBaseInfo tDimBaseInfo) {
        return updateById(tDimBaseInfo);
    }

    @Override
    @Transactional
    public ReturnT<Boolean> deleteDimBaseInfo(Long id) {
        //查询维度信息
        TDimBaseInfo tDimBaseInfo = getById(id);
        if(tDimBaseInfo == null) {
            log.error("维度信息或维度分组不存在！维度id：{}", id);
            return ReturnT.fail("维度信息或维度分组不存在");
        }


        DimTypeEnum dimTypeEnum = DimTypeEnum.getByType(tDimBaseInfo.getDimType());
        if(DimTypeEnum.DIM_MANAGE == dimTypeEnum) {
            //根节点不允许删除
            return ReturnT.fail("根节点不允许删除");
        }
        if(DimTypeEnum.SYSTEM_DIM == dimTypeEnum) {
            //系统维度不允许删除
            return ReturnT.fail("系统维度不允许删除");
        }

        //批量删除的维度id集合
        List<TDimBaseInfo> delInfoList = new ArrayList<>();
        if(DimTypeEnum.NORMAL_DIM == dimTypeEnum) {
            //维度信息
            delInfoList.add(tDimBaseInfo);
        }else {
            //获取所有的子维度和子分组
            LambdaQueryWrapper<TDimBaseInfo> wrapper = new LambdaQueryWrapper<>();
            List<Long> parentIds = new ArrayList<>();
            parentIds.add(id);
            while (true) {
                wrapper.clear();
                wrapper.in(TDimBaseInfo::getDimGroupId, parentIds);
                List<TDimBaseInfo> list = list(wrapper);
                if(CollectionUtil.isEmpty(list)) {
                    break;
                }
                parentIds.clear();
                for (TDimBaseInfo dimBaseInfo : list) {
                    Integer dimType = dimBaseInfo.getDimType();
                    if(Objects.equals(DimTypeEnum.DIM_GROUP.getType(), dimType)) {
                        parentIds.add(dimBaseInfo.getId());
                    }
                    if(Objects.equals(DimTypeEnum.SYSTEM_DIM.getType(), dimType)) {
                        //存在系统维表，不允许删除
                        return ReturnT.fail("存在系统维表，不允许删除");
                    }

                    delInfoList.add(dimBaseInfo);
                }

                if(CollectionUtil.isEmpty(parentIds)) {
                    break;
                }
            }
        }

        if(CollectionUtil.isNotEmpty(delInfoList)) {
            List<Long> ids = delInfoList.stream().map(TDimBaseInfo::getId).toList();
            //删除维度关联的其他信息
            tDimAttributeSettingService.remove(new LambdaQueryWrapper<TDimAttributeSetting>().in(TDimAttributeSetting::getDimNo, ids));
            tDimDrillPathService.remove(new LambdaQueryWrapper<TDimDrillPath>().in(TDimDrillPath::getDimNo, ids));
            //批量删除维度或分组信息
            removeBatchByIds(ids);
            //筛选出维度列表
            List<TDimBaseInfo> dimBaseInfoList = delInfoList.stream()
                    .filter(info -> !Objects.equals(info.getDimType(), DimTypeEnum.DIM_GROUP.getType())).toList();
            if(CollectionUtil.isNotEmpty(dimBaseInfoList)) {
                for (TDimBaseInfo info : dimBaseInfoList) {
                    //异步删除数据库表结构
                    CompletableFuture.runAsync(deleteDimDataSourceTable(info.getSourceId(), info.getTableId()));
                }
            }
        }

        return ReturnT.success(true);
    }

    /**
     * 异步删除维表对应的数据库表结构
     * @return  任务
     */
    private Runnable deleteDimDataSourceTable(String sourceId, String tableId) {
        return () -> {
            if(StrUtil.isBlank(sourceId) || StrUtil.isBlank(tableId)) {
                log.error("删除维表数据库表结构-数据源id或表id为空");
                return;
            }
            //查询连接池信息
            MetadataSourceEntity source = metadataSourceService.getById(sourceId);
            //查询表信息
            MetadataTableEntity table = metadataTableService.getOne(new LambdaQueryWrapper<MetadataTableEntity>()
                    .eq(MetadataTableEntity::getSourceId, sourceId)
                    .eq(MetadataTableEntity::getId, tableId));
            if(source == null || table == null) {
                log.error("删除维表数据库表结构-表信息或连接池信息不存在");
                return;
            }

            try {
                DbType dbType = DbType.getDbType(source.getDbType());
                String quote = dbType.getQuote();
                SqlConsoleDto sqlConsoleDto = new SqlConsoleDto();
                sqlConsoleDto.setSourceId(sourceId);
                sqlConsoleDto.setTableName(table.getTableName());
                sqlConsoleDto.setSqlKey(new Date().getTime() + "");
                String sql = "DROP TABLE " + quote + source.getSourceName() + quote + "." + quote + table.getTableName() + quote + ";";
                sqlConsoleDto.setSqlText(sql);
                sqlConsoleService.sqlRun(sqlConsoleDto);
            }catch (Exception e) {
                log.error("异步删除表结构异常！连接池id：{}，表id：{}", sourceId, tableId, e);
            }
        };
    }

    @Override
    public TDimBaseInfoVo getDimBaseInfoTooById(Long id) {
        TDimBaseInfoVo tDimBaseInfoVo = new TDimBaseInfoVo();
        TDimBaseInfo tDimBaseInfo = getById(id);
        if (tDimBaseInfo == null) {
            return null;
        }

        BeanUtil.copyProperties(tDimBaseInfo, tDimBaseInfoVo);

        // 查询维表关联数据
        List<TDimColumnSettingVo> columnSettings = new ArrayList<>();
        List<TDimAttributeSettingVo> attributeSettings = new ArrayList<>();
        List<TDimDrillPathVo> drillPaths = new ArrayList<>();

        //字段设置列表
        //查询表字段数据
        List<MetadataColumnEntity> columnEntityList = metadataColumnService.list(new LambdaQueryWrapper<MetadataColumnEntity>().eq(MetadataColumnEntity::getSourceId, tDimBaseInfo.getSourceId()).eq(MetadataColumnEntity::getTableId, tDimBaseInfo.getTableId()));
        //按id分组
        Map<String, MetadataColumnEntity> columnEntityMap = Optional.ofNullable(columnEntityList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(MetadataColumnEntity::getId, metadataColumnEntity -> metadataColumnEntity));
        MetadataColumnEntity idColumn = null;
        MetadataColumnEntity textColumn = null;
        if (CollectionUtil.isNotEmpty(columnEntityList)) {
            //id列
            idColumn = columnEntityMap.get(tDimBaseInfo.getIdFieldColumnId());
            //text列
            textColumn = columnEntityMap.get(tDimBaseInfo.getTextFieldColumnId());
            //封装字段属性列表
            for (MetadataColumnEntity metadataColumnEntity : columnEntityList) {
                TDimColumnSettingVo tDimColumnSettingVo = createTdimColumnSettingVo(metadataColumnEntity, tDimBaseInfo);
                columnSettings.add(tDimColumnSettingVo);
            }
        }

        //属性设置列表
        List<TDimAttributeSetting> tDimAttributeSettingList = tDimAttributeSettingService.list(new LambdaQueryWrapper<TDimAttributeSetting>().eq(TDimAttributeSetting::getDimNo, id));
        if (CollectionUtil.isNotEmpty(tDimAttributeSettingList)) {
            for (TDimAttributeSetting tDimAttributeSetting : tDimAttributeSettingList) {
                TDimAttributeSettingVo tDimAttributeSettingVo = new TDimAttributeSettingVo();
                tDimAttributeSettingVo.setId(tDimAttributeSetting.getId() + "");
                tDimAttributeSettingVo.setAttributeName(tDimAttributeSetting.getAttributeName());
                tDimAttributeSettingVo.setAttributeColumn(columnEntityMap.get(tDimAttributeSetting.getAttributeColumnId()).getColumnName());
                tDimAttributeSettingVo.setTextColumn(columnEntityMap.get(tDimAttributeSetting.getTextColumnId()).getColumnName());
                tDimAttributeSettingVo.setDimNo(tDimBaseInfo.getId() + "");
                attributeSettings.add(tDimAttributeSettingVo);
            }
        }

        //钻取路径列表
        List<TDimDrillPath> tDimDrillPathList = tDimDrillPathService.list(new LambdaQueryWrapper<TDimDrillPath>().eq(TDimDrillPath::getDimNo, id));
        if (CollectionUtil.isNotEmpty(tDimDrillPathList)) {
            for (TDimDrillPath tDimDrillPath : tDimDrillPathList) {
                TDimDrillPathVo tDimDrillPathVo = new TDimDrillPathVo();
                tDimDrillPathVo.setId(tDimDrillPath.getId() + "");
                tDimDrillPathVo.settPath(tDimDrillPath.getTPath());
                tDimDrillPathVo.setDimNo(tDimBaseInfoVo.getId() + "");
                drillPaths.add(tDimDrillPathVo);
            }
        }


        tDimBaseInfoVo.setColumnSettings(columnSettings);
        tDimBaseInfoVo.setAttributeSettings(attributeSettings);
        tDimBaseInfoVo.setDrillPaths(drillPaths);
        //设置id和text字段
        tDimBaseInfoVo.setIdField(idColumn != null ? idColumn.getColumnName() : "");
        tDimBaseInfoVo.setTextField(textColumn != null ? textColumn.getColumnName() : "");
        return tDimBaseInfoVo;
    }

    @Override
    public ReturnT<DimDataContentVo> getDimData(Long id) throws SQLException {
        //查询表数据
        TDimBaseInfo tDimBaseInfo = null;
        if (id == null || (tDimBaseInfo = getById(id)) == null) {
            return ReturnT.fail("维度信息不存在");
        }

        //连接池id
        String sourceId = tDimBaseInfo.getSourceId();
        //表id
        String tableId = tDimBaseInfo.getTableId();

        //查询维度关联的表信息
        MetadataTableEntity table = metadataTableService.getOne(new LambdaQueryWrapper<MetadataTableEntity>().eq(MetadataTableEntity::getSourceId, sourceId).eq(MetadataTableEntity::getId, tableId));
        if (table == null) {
            //维度关联的表信息不存在
            return ReturnT.fail("表信息不存在");
        }

        //查询列信息，并按序号排序
        List<MetadataColumnEntity> columnEntityList = getColumnAndSortByPosition(sourceId, tableId);

        //查询维表数据
        SqlConsoleVo sqlConsoleVo = selectTableData(sourceId, table.getTableName());
        if (sqlConsoleVo == null || !sqlConsoleVo.getSuccess()) {
            return ReturnT.fail("查询数据失败");
        }

        //封装返回数据
        //表头信息
        StringBuilder builder = new StringBuilder("#");
        for (int i = 0; i < columnEntityList.size(); i++) {
            MetadataColumnEntity metadataColumnEntity = columnEntityList.get(i);
            //格式：字段名称(类型缩写|长度)
            builder.append(metadataColumnEntity.getColumnName()).append("(").append(metadataColumnEntity.getDataType()).append("|").append(metadataColumnEntity.getDataLength()).append(")");
            builder.append(i < columnEntityList.size() - 1 ? "\t" : "\n");
        }

        //表数据
        List<Map<String, Object>> dataList = sqlConsoleVo.getDataList();
        for (Map<String, Object> dataMap : dataList) {
            for (int j = 0; j < columnEntityList.size(); j++) {
                MetadataColumnEntity metadataColumnEntity = columnEntityList.get(j);
                String value = dataMap.getOrDefault(metadataColumnEntity.getColumnName(), "").toString();
                builder.append(value);
                builder.append(j < columnEntityList.size() - 1 ? "\t" : "\n");
            }
        }

        DimDataContentVo dimDataContentVo = new DimDataContentVo();
        dimDataContentVo.setId(tDimBaseInfo.getId() + "");
        dimDataContentVo.setData(builder.toString());

        return ReturnT.success(dimDataContentVo);
    }

    @Override
    public ReturnT<List<DimDataTreeVo>> getDimDataTree(Long id) throws SQLException {
        List<DimDataTreeVo> tree = new ArrayList<>();
        TDimBaseInfo tDimBaseInfo = null;
        if (id == null || (tDimBaseInfo = getById(id)) == null) {
            return ReturnT.fail("维度信息不存在");
        }
        //数据源id
        String sourceId = tDimBaseInfo.getSourceId();
        //表id
        String tableId = tDimBaseInfo.getTableId();
        //主键字段列id
        final String idFieldColumnId = tDimBaseInfo.getIdFieldColumnId();
        //文字字段列id
        final String textFieldColumnId = tDimBaseInfo.getTextFieldColumnId();

        //查询表信息
        MetadataTableEntity table = metadataTableService.getById(tableId);
        if (table == null) {
            return ReturnT.fail("表信息不存在");
        }

        //查询列信息，并按序号排序
        List<MetadataColumnEntity> columnEntityList = getColumnAndSortByPosition(sourceId, tableId);
        if (CollectionUtil.isEmpty(columnEntityList)) {
            return ReturnT.success(tree);
        }

        //获取列名，按序号进行排序
        List<String> columnNameList = columnEntityList.stream().sorted(Comparator.comparing(MetadataColumnEntity::getColumnPosition)).map(MetadataColumnEntity::getColumnName).collect(Collectors.toList());
        //按id进行分组
        Map<String, MetadataColumnEntity> groupByIdMap = columnEntityList.stream().collect(Collectors.toMap(MetadataColumnEntity::getId, Function.identity()));

        //ID字段名称
        String idField = groupByIdMap.get(idFieldColumnId).getColumnName();
        //text字段名称
        String textField = groupByIdMap.get(textFieldColumnId).getColumnName();

        //查询维表数据
        SqlConsoleVo sqlConsoleVo = selectTableData(sourceId, table.getTableName());
        if (sqlConsoleVo == null || !sqlConsoleVo.getSuccess()) {
            return ReturnT.fail("查询数据失败");
        }
        List<Map<String, Object>> dataList = sqlConsoleVo.getDataList();
        if (CollectionUtil.isEmpty(dataList)) {
            //无数据
            return ReturnT.success(tree);
        }

        //校验主键是否为空或者重复，根据主键值进行分组
        Map<String, Map<String, Object>> groupByIdValueMap = new HashMap<>();
        for (Map<String, Object> map : dataList) {
            //获取主键
            Object idValue = map.get(idField);
            if (idValue == null || groupByIdValueMap.containsKey(idValue.toString())) {
                return ReturnT.fail("数据存在主键为空或重复");
            }
            groupByIdValueMap.put(idValue.toString(), map);
        }

        //设置的维度树型结构
        String treeType = tDimBaseInfo.getTreeType();
        DimTreeType dimTreeType = DimTreeType.getByType(treeType);

        if (dimTreeType == DimTreeType.SPILT && StrUtil.isNotBlank(tDimBaseInfo.getSplitInfo())) {
            //分段展示
            String splitInfo = tDimBaseInfo.getSplitInfo();
            List<String> split = StrUtil.split(splitInfo, "-").stream().map(String::trim).collect(Collectors.toList());
            //过滤掉0，字符串转数字
            List<Integer> infos = split.stream().filter(s -> !"0".equals(s)).map(Integer::parseInt).collect(Collectors.toList());

            Map<String, DimDataTreeVo> treeMap = new HashMap<>();
            //对id值进行排序
            List<String> idValueList = groupByIdValueMap.keySet().stream().sorted().collect(Collectors.toList());
            for (String idValue : idValueList) {
                Map<String, Object> map = groupByIdValueMap.get(idValue);
                String textValue = map.get(textField).toString();
                DimDataTreeVo node = new DimDataTreeVo();
                node.setId(idValue);
                node.setValue(idValue + " " + textValue);
                node.setChildren(new ArrayList<>());
                //对id进行拆分
                List<String> items = splitIdValueBySplitInfo(infos, idValue);
                for (int i = 0; i < items.size(); i++) {
                    String item = items.get(i);
                    if (StrUtil.isBlank(item) && i > 0 && StrUtil.isNotBlank(items.get(i - 1))) {
                        //获取上一个分段的值
                        DimDataTreeVo parent = treeMap.get(items.get(i - 1));
                        parent.getChildren().add(node);
                        break;
                    } else {
                        DimDataTreeVo vo = treeMap.get(item);
                        if (vo == null) {
                            if (i > 0) {
                                DimDataTreeVo parent = treeMap.get(items.get(i - 1));
                                parent.getChildren().add(node);
                            }
                            treeMap.put(item, node);
                            tree.add(node);
                            break;
                        }
                    }
                }
            }

            return ReturnT.success(tree);
        }

        //钻取展示
        if (dimTreeType == DimTreeType.Drill) {
            String drillPath = tDimBaseInfo.getDrillPath();
            //按“-”分割，获取每个属性名称，查询对应的属性设置列表
            List<String> attributeNameList = StrUtil.split(drillPath, "-");
            List<TDimAttributeSetting> attributeSettingList = tDimAttributeSettingService.list(new LambdaQueryWrapper<TDimAttributeSetting>().eq(TDimAttributeSetting::getDimNo, tDimBaseInfo.getId()).in(TDimAttributeSetting::getAttributeName, attributeNameList));
            //按属性名称将进行分组
            Map<String, TDimAttributeSetting> attributeMap = attributeSettingList.stream().collect(Collectors.toMap(TDimAttributeSetting::getAttributeName, Function.identity()));

            for (Map<String, Object> map : dataList) {
                //todo  根据钻取路径展示树结构

            }

        }

        //无层次结构展示
        for (Map<String, Object> map : dataList) {
            DimDataTreeVo node = new DimDataTreeVo();
            //获取主键
            Object idValue = map.get(idField);
            node.setId(idValue.toString());

            //拼接value
            StringBuilder sb = new StringBuilder();
            for (String columnName : columnNameList) {
                sb.append(map.getOrDefault(columnName, "")).append("\t");
            }
            sb.deleteCharAt(sb.length() - 1);
            node.setValue(sb.toString());
            tree.add(node);
        }
        return ReturnT.success(tree);
    }

    @Override
    public List<TDimBaseRelaColVo> queryDimAllList(String dimType) {

        List<TDimBaseRelaColVo> tDimBaseRelaColVos = new ArrayList<>();

        List<TDimBaseInfo> tDimBaseInfos = new ArrayList<>();
        if (dimType.equals("2")) {
            //查询所有具体维度列表
            tDimBaseInfos = list(new QueryWrapper<TDimBaseInfo>().eq("dim_type", 2));
        } else {
            tDimBaseInfos = list();
        }

        //查询所有维度关联的列信息
        List<TDimColumnSetting> tDimColumnList = tDimColumnSettingService.list(new QueryWrapper<TDimColumnSetting>().isNotNull("dim_no"));

        //封装返回数据结构
        for (TDimBaseInfo tDimBaseInfo : tDimBaseInfos) {
            TDimBaseRelaColVo tDimBaseRelaColVo = new TDimBaseRelaColVo();
            tDimBaseRelaColVo.setId(tDimBaseInfo.getId());
            tDimBaseRelaColVo.setDimName(tDimBaseInfo.getDimName());
            tDimBaseRelaColVo.setDatabasePool(tDimBaseInfo.getDatabasePool());
            tDimBaseRelaColVo.setSourceId(tDimBaseInfo.getSourceId());
            tDimBaseRelaColVo.setTableName(tDimBaseInfo.getTableName());
            tDimBaseRelaColVo.setTableId(tDimBaseInfo.getTableId());
            List<TDimColumnSetting> newDimColumnList = tDimColumnList.stream().filter(td -> td.getDimNo().equals(tDimBaseInfo.getId())).collect(Collectors.toList());
            List<String> newDimColumnVos = newDimColumnList.stream().map(TDimColumnSetting::getColumnName).toList();

            tDimBaseRelaColVo.setDimColumnSettingVoList(newDimColumnVos);
            tDimBaseRelaColVos.add(tDimBaseRelaColVo);
        }
        return tDimBaseRelaColVos;
    }

    @Override
    public AjaxResult getDimDataList(Long dimNo) throws SQLException {
        //查询表数据
        TDimBaseInfo tDimBaseInfo = null;
        if (dimNo == null || (tDimBaseInfo = getById(dimNo)) == null) {
            return AjaxResult.error("维度信息不存在");
        }

        //连接池id
        String sourceId = tDimBaseInfo.getSourceId();
        //表id
        String tableId = tDimBaseInfo.getTableId();

        //查询维度关联的表信息
        MetadataTableEntity table = metadataTableService.getOne(new LambdaQueryWrapper<MetadataTableEntity>().eq(MetadataTableEntity::getSourceId, sourceId).eq(MetadataTableEntity::getId, tableId));
        if (table == null) {
            //维度关联的表信息不存在
            return AjaxResult.error("表信息不存在");
        }

        //查询维表中的指定列
        List<TDimAttributeSetting> tDimAttributeSettingList = tDimAttributeSettingService.list(new QueryWrapper<TDimAttributeSetting>().eq("dim_no", dimNo));

        if (tDimAttributeSettingList == null || tDimAttributeSettingList.size() == 0) {
            //维度关联的列信息不存在
            return AjaxResult.error("维度关联的列信息不存在");
        }
        List<String> columnList = tDimAttributeSettingList.stream().map(TDimAttributeSetting::getAttributeColumn).collect(Collectors.toList());

        String queryColumn = String.join(",", columnList);

        String tableName = table.getTableName();
        //查询维表数据
        SqlConsoleDto sqlConsoleDto = new SqlConsoleDto();
        sqlConsoleDto.setSourceId(sourceId);
        sqlConsoleDto.setTableName(tableName);
        sqlConsoleDto.setSqlKey(new Date().getTime() + "");
        sqlConsoleDto.setSqlText(StrUtil.format("SELECT " + queryColumn + " FROM {}", tableName));
        List<SqlConsoleVo> sqlConsoleVoResult = sqlConsoleService.sqlRun(sqlConsoleDto);

        SqlConsoleVo sqlConsoleVo = null;
        if (CollectionUtil.isEmpty(sqlConsoleVoResult) || (sqlConsoleVo = sqlConsoleVoResult.get(0)) == null || !sqlConsoleVo.getSuccess()) {
            log.error("查询表：{} 数据失败！", tableName);
            return null;
        }
        return AjaxResult.success(sqlConsoleVo);
    }

    /**
     * 根据提供的分割信息将给定的ID值分割成多个子字符串。
     *
     * @param infos   分割信息列表，每个元素表示对应位置开始的子字符串长度。
     * @param idValue 待分割的ID值字符串。
     * @return 分割后的子字符串列表。如果某个子字符串长度超过ID值剩余部分长度，则该位置将返回一个空字符串。
     */
    public static List<String> splitIdValueBySplitInfo(List<Integer> infos, String idValue) {
        List<String> res = new ArrayList<>(infos.size());
        int position = 0;
        for (Integer item : infos) {
            if (position + item <= idValue.length()) {
                res.add(idValue.substring(0, position + item));
                position += item;
            } else if (position < idValue.length()) {
                res.add(idValue);
                position = idValue.length();
            } else {
                res.add("");
            }
        }
        return res;
    }

    /**
     * 根据数据源ID和表ID获取列信息并按序号进行排序
     *
     * @param sourceId 数据源ID
     * @param tableId  表ID
     * @return 列信息列表，按序号排序
     */
    private List<MetadataColumnEntity> getColumnAndSortByPosition(String sourceId, String tableId) {
        //查询列信息
        List<MetadataColumnEntity> columnEntityList = metadataColumnService.list(new LambdaQueryWrapper<MetadataColumnEntity>().eq(MetadataColumnEntity::getSourceId, sourceId).eq(MetadataColumnEntity::getTableId, tableId));
        //对字段进行排序
        columnEntityList = Optional.ofNullable(columnEntityList).orElse(new ArrayList<>()).stream().sorted(Comparator.comparing(MetadataColumnEntity::getColumnPosition)).collect(Collectors.toList());
        return columnEntityList;
    }

    private SqlConsoleVo selectTableData(String sourceId, String tableName) throws SQLException {
        SqlConsoleDto sqlConsoleDto = new SqlConsoleDto();
        sqlConsoleDto.setSourceId(sourceId);
        sqlConsoleDto.setTableName(tableName);
        //sqlConsoleDto.setIsLimit(false);
        sqlConsoleDto.setSqlKey(new Date().getTime() + "");
        sqlConsoleDto.setSqlText(StrUtil.format("SELECT * FROM {}", tableName));
        List<SqlConsoleVo> sqlConsoleVos = sqlConsoleService.sqlRun(sqlConsoleDto);
        SqlConsoleVo sqlConsoleVo = null;
        if (CollectionUtil.isEmpty(sqlConsoleVos) || (sqlConsoleVo = sqlConsoleVos.get(0)) == null || !sqlConsoleVo.getSuccess()) {
            log.error("查询表：{} 数据失败！", tableName);
            return null;
        }
        Integer count = sqlConsoleVo.getCount();
        log.info("查询表：{} 数据成功，共查询到{}条记录", tableName, count);
        return sqlConsoleVo;
    }

    @Override
    public ReturnT<Boolean> editDimData(DimDataContentVo editContentVo) throws SQLException {
        String dimId = editContentVo.getId();
        String data = editContentVo.getData();
        TDimBaseInfo tDimBaseInfo = null;
        if (dimId == null || (tDimBaseInfo = getById(Long.parseLong(dimId))) == null) {
            return ReturnT.fail("维度信息不存在");
        }

        //查询表信息
        MetadataTableEntity table = metadataTableService.getById(tDimBaseInfo.getTableId());
        //查询列信息
        List<MetadataColumnEntity> columnEntityList = metadataColumnService.list(new LambdaQueryWrapper<MetadataColumnEntity>().eq(MetadataColumnEntity::getSourceId, tDimBaseInfo.getSourceId()).eq(MetadataColumnEntity::getTableId, tDimBaseInfo.getTableId()));
        //对字段进行排序，获取字段名称列表
        List<String> columnNameList = Optional.ofNullable(columnEntityList).orElse(new ArrayList<>()).stream().sorted(Comparator.comparing(MetadataColumnEntity::getColumnPosition)).map(MetadataColumnEntity::getColumnName).collect(Collectors.toList());
        //根据id分组
        Map<String, MetadataColumnEntity> columnEntityMap = Optional.ofNullable(columnEntityList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(MetadataColumnEntity::getId, Function.identity()));
        //ID字段名称
        String idField = columnEntityMap.get(tDimBaseInfo.getIdFieldColumnId()).getColumnName();

        //sql执行对象
        SqlConsoleDto sqlConsoleDto = new SqlConsoleDto();
        sqlConsoleDto.setSourceId(tDimBaseInfo.getSourceId());
        sqlConsoleDto.setTableName(table.getTableName());
        sqlConsoleDto.setSqlKey(new Date().getTime() + "");
        StringBuilder sqlBuilder = new StringBuilder();
        //不管有没有传数据，都先清空表数据
        sqlBuilder.append("TRUNCATE TABLE ").append(table.getTableName()).append(";");
        if (StrUtil.isNotBlank(data)) {
            //根据换行符分隔数据，过滤空行
            List<String> textLines = StrUtil.split(data, "\n").stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
            //移除表头行
            if (textLines.get(0).trim().startsWith("#")) {
                textLines.remove(0);
            }

            if (CollectionUtil.isNotEmpty(textLines)) {
                //对每行进行数据分隔处理
                List<List<String>> lines = textLines.stream().map(line -> StrUtil.split(line, "\t")).collect(Collectors.toList());
                //验证主键值是否重复
                int index = columnNameList.indexOf(idField);
                //判断主键是否重复或者为空
                Set<String> idValueSet = new HashSet<>();
                for (List<String> line : lines) {
                    String idValue = null;
                    if (line.size() - 1 < index || StrUtil.isBlank(idValue = line.get(index)) || idValueSet.contains(idValue)) {
                        return ReturnT.fail("主键值重复或为空，请检查");
                    }
                    idValueSet.add(line.get(index));
                }
                idValueSet.clear();
                idValueSet = null;

                //拼接插入sql
                sqlBuilder.append("INSERT INTO ").append(table.getTableName()).append(" (");
                for (String column : columnNameList) {
                    sqlBuilder.append(column).append(",");
                }
                sqlBuilder.append(") VALUES ");

                for (List<String> line : lines) {
                    sqlBuilder.append("(");
                    for (String value : line) {
                        sqlBuilder.append("'").append(value).append("',");
                    }
                    sqlBuilder.append("),");
                }
                //移除最后一个逗号
                sqlBuilder.deleteCharAt(sqlBuilder.length() - 1);
            }
        }
        sqlConsoleDto.setSqlText(sqlBuilder.toString());
        //执行sql语句
        sqlConsoleService.sqlRun(sqlConsoleDto);
        return ReturnT.success(true);
    }

    @Override
    public List<TDimBaseInfoVo> getAllDimList(List<Long> ids, Boolean needData) {
        List<TDimBaseInfoVo> res = new ArrayList<>();
        LambdaQueryWrapper<TDimBaseInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.and(w ->
                w.eq(TDimBaseInfo::getDimType, DimTypeEnum.NORMAL_DIM.getType()).or()
                .eq(TDimBaseInfo::getDimType, DimTypeEnum.SYSTEM_DIM.getType()));
        if (CollectionUtil.isNotEmpty(ids)) {
            wrapper.in(TDimBaseInfo::getId, ids);
        }
        List<TDimBaseInfo> list = list(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            return res;
        }

        //列map
        Map<String, MetadataColumnEntity> columnMap = new HashMap<>();
        Map<String, MetadataTableEntity> tableMap = new HashMap<>();
        Map<String, MetadataSourceEntity> sourceMap = new HashMap<>();
        if(needData) {
            //批量获取文字字段列信息
            List<String> textColumnIds = list.stream().map(TDimBaseInfo::getTextFieldColumnId).distinct().toList();
            if(CollectionUtil.isNotEmpty(textColumnIds)) {
                List<MetadataColumnEntity> metadataColumnEntityList = metadataColumnService.listByIds(textColumnIds);
                if(CollectionUtil.isNotEmpty(metadataColumnEntityList)) {
                    columnMap = metadataColumnEntityList.stream().collect(Collectors.toMap(MetadataColumnEntity::getId, Function.identity()));
                    List<String> tableIds = metadataColumnEntityList.stream().map(MetadataColumnEntity::getTableId).distinct().toList();
                    List<String> sourceIds = metadataColumnEntityList.stream().map(MetadataColumnEntity::getSourceId).distinct().toList();
                    List<MetadataTableEntity> metadataTableEntityList = metadataTableService.listByIds(tableIds);
                    if(CollectionUtil.isNotEmpty(metadataTableEntityList)) {
                        tableMap = metadataTableEntityList.stream().collect(Collectors.toMap(MetadataTableEntity::getId, Function.identity()));
                    }
                    List<MetadataSourceEntity> metadataSourceEntityList = metadataSourceService.listByIds(sourceIds);
                    if(CollectionUtil.isNotEmpty(metadataSourceEntityList)) {
                        sourceMap = metadataSourceEntityList.stream().collect(Collectors.toMap(MetadataSourceEntity::getId, Function.identity()));
                    }
                }
            }
        }
        for (TDimBaseInfo tDimBaseInfo : list) {
            TDimBaseInfoVo tDimBaseInfoVo = BeanUtil.copyProperties(tDimBaseInfo, TDimBaseInfoVo.class);
            res.add(tDimBaseInfoVo);
            if(needData) {
                String tableId = tDimBaseInfo.getTableId();
                String textFieldColumnId = tDimBaseInfo.getTextFieldColumnId();
                MetadataTableEntity table = tableMap.get(textFieldColumnId);
            }
        }

        return res;
    }
}


