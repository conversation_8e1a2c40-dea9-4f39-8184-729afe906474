package com.dib.index.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dib.common.core.domain.AjaxResult;
import com.dib.index.domain.TDimBaseInfo;
import com.dib.index.utils.ReturnT;
import com.dib.index.vo.DimDataContentVo;
import com.dib.index.vo.DimDataTreeVo;
import com.dib.index.vo.TDimBaseInfoVo;
import com.dib.index.TDimBaseRelaColVo;

import java.sql.SQLException;
import java.util.List;

public interface TDimBaseInfoService extends IService<TDimBaseInfo> {

    // 获取所有维度基础信息
    List<TDimBaseInfoVo> listAllDimBaseInfo(Long dimGroupId);

    // 根据id获取维度基础信息
    TDimBaseInfoVo getDimGroupInfo(Long id);

    // 新建维度基础信息
    boolean createOrUpdateDimGroup(TDimBaseInfoVo tDimBaseInfoVo);

    // 修改维度基础信息
    boolean updateDimBaseInfo(TDimBaseInfo tDimBaseInfo);

    // 删除维度基础信息
    ReturnT<Boolean> deleteDimBaseInfo(Long id);

    /**
     * 新增维度基础信息及相关从表信息
     *
     * @param tdimBaseInfoVo 包含主表及关联从表数据的维度基础信息实体
     * @return 维表id
     */
    ReturnT<String> createOrUpdateDimBaseInfo(TDimBaseInfoVo tdimBaseInfoVo);

    /**
     * 根据id查询维度基础信息及相关从表信息
     *
     * @param id 维度基础信息的编号
     * @return 包含主表及关联从表数据的维度基础信息实体
     */
    TDimBaseInfoVo getDimBaseInfoTooById(Long id);

    /**
     * 获取维表列表
     *
     * @return 维表列表
     */
    List<TDimBaseInfoVo> getAllDimList(List<Long> ids, Boolean needData);

    /**
     * 查询维表数据
     *
     * @param id 维表id
     * @return 数据
     */
    ReturnT<DimDataContentVo> getDimData(Long id) throws SQLException;

    /**
     * 编辑维表数据
     *
     * @param editContentVo 维表数据
     * @return 编辑结果
     * @throws SQLException sql异常
     */
    ReturnT<Boolean> editDimData(DimDataContentVo editContentVo) throws SQLException;

    /**
     * 获取维表数据树结构
     *
     * @param id 维表id
     * @return 维表数据树结构
     */
    ReturnT<List<DimDataTreeVo>> getDimDataTree(Long id) throws SQLException;

    /**
     * 查询所有维表维度列
     *
     * @return 维表维度列
     */
    List<TDimBaseRelaColVo> queryDimAllList(String dimType);

    /**
     * 查询指定维表数据
     *
     * @param dimNo 维度编号
     * @return 具体维表数据
     */
    AjaxResult getDimDataList(Long dimNo)throws SQLException;
}
