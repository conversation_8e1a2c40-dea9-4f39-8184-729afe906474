package com.dib.metadata.enums;

import lombok.Getter;

/**
 * SQL类型枚举
 * createTable: 创建表
 * updateTable: 更新表结构
 */
@Getter
public enum SqlTypeEnum {

    CREATE_TABLE("createTable", "创建表"),
    UPDATE_TABLE("updateTable", "更新表结构"),
    CREATE_DATA_BASE("createDataBase", "创建数据库");

    private final String code;
    private final String desc;

    SqlTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 枚举值
     * @return 枚举对象
     */
    public static SqlTypeEnum getByCode(String code) {
        for (SqlTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
