package com.dib.metadata.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class MetadataCreateSqlDto {
    private static final long serialVersionUID=1L;

    @NotBlank(message = "目标数据源不能为空")
    private String targetId;

    @NotBlank(message = "目标数据源模式名不能为空")
    private String targetSchemaName;

    @NotBlank(message = "目标数据源模式名不能为空")
    private String targetTableName;

    //源数据表备注
    private String sourceTableRemarks;

    //目标数据库主键集
    @NotBlank(message = "主键不能为空")
    private List<String> targetPrimaryKeys;

    private  List<MetadataColumnMarketDto> metadataColumnMarketDtoList;

    private  List<MetadataIndexMarketDto> metadataIndexMarketDtoList;

    @NotBlank(message = "源数据Id不能为空")
    private Set<String> sourceIds;

    //sql类型 createTable/updateTable/createDataBase
    private String sqlType;

    private String tableSql;

    //创建sql备用参数
    Map<String, String> param;

}
