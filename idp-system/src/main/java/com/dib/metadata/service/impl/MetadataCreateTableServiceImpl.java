package com.dib.metadata.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dib.async.AsyncTask;
import com.dib.bigdata.entity.JobDatasource;
import com.dib.bigdata.service.JobDatasourceService;
import com.dib.common.database.constants.DbQueryProperty;
import com.dib.common.database.constants.DbType;
import com.dib.common.database.query.AbstractDbQueryFactory;
import com.dib.common.database.schema.ColumnDescription;
import com.dib.common.database.schema.IndexMetaData;
import com.dib.common.database.schema.SourceProperties;
import com.dib.common.database.service.DataSourceFactory;
import com.dib.common.database.service.DbQuery;
import com.dib.common.database.utils.GenerateSqlUtils;
import com.dib.metadata.dto.*;
import com.dib.metadata.entity.MetadataColumnEntity;
import com.dib.metadata.entity.MetadataSourceEntity;
import com.dib.metadata.entity.MetadataTableEntity;
import com.dib.metadata.mapper.MetadataTableDao;
import com.dib.metadata.service.MetadataColumnService;
import com.dib.metadata.service.MetadataCreateDataBaseAndTableService;
import com.dib.metadata.service.MetadataSourceService;
import com.dib.metadata.vo.MetadataSourceSyncVo;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;


import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class MetadataCreateTableServiceImpl implements MetadataCreateDataBaseAndTableService {

    @Autowired
    private AsyncTask asyncTask;
    @Autowired
    private JobDatasourceService jobDatasourceService;
    @Autowired
    private MetadataTableDao metadataTableDao;
    @Autowired
    private DataSourceFactory dataSourceFactory;
    @Autowired
    private MetadataSourceService metadataSourceService;
    @Autowired
    private MetadataColumnService metadataColumnService;


    @Override
    public String createSql(MetadataCreateSqlDto metadataCreateTableDto) {
        if (Objects.isNull(metadataCreateTableDto) || StringUtils.isBlank(metadataCreateTableDto.getSqlType())) {
            return null;
        }

        List<MetadataColumnMarketDto> metadataColumnMarketDtoList = metadataCreateTableDto.getMetadataColumnMarketDtoList();
        List<MetadataIndexMarketDto> metadataIndexMarketDtoList = metadataCreateTableDto.getMetadataIndexMarketDtoList();
        if (CollectionUtil.isEmpty(metadataColumnMarketDtoList) && CollectionUtil.isEmpty(metadataIndexMarketDtoList)) {
            return null;
        }

        //查询所有数据源连接信息
        Set<String> sourceIds = new HashSet<>();
        Map<String, MetadataColumnEntity> metaColumnByIdMap = new HashMap<>();
        MetadataSourceEntity dataSource = new MetadataSourceEntity();

        List<String> targetPrimaryKeys = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(metadataColumnMarketDtoList)) {
            Set<String> columnIds = new HashSet<>();
            for (MetadataColumnMarketDto metadataColumnMarketDto : metadataColumnMarketDtoList) {
                sourceIds.add(metadataColumnMarketDto.getSourceId());
                columnIds.add(metadataColumnMarketDto.getId());
                if ("1".equals(metadataColumnMarketDto.getColumnKey())) {
                    targetPrimaryKeys.add(metadataColumnMarketDto.getColumnName());
                }
            }

            List<MetadataColumnEntity> metadataColumnEntities = metadataColumnService.listByIds(columnIds);
            if (CollectionUtil.isNotEmpty(metadataColumnEntities)) {
                metaColumnByIdMap = metadataColumnEntities.stream().collect(Collectors.toMap(MetadataColumnEntity::getId, v -> v));
            }
        }
        if (CollectionUtil.isNotEmpty(metadataIndexMarketDtoList)) {
            List<String> strings = metadataIndexMarketDtoList.stream().map(MetadataIndexMarketDto::getSourceId).distinct().collect(Collectors.toList());
            sourceIds.addAll(strings);
        }
        metadataCreateTableDto.setSourceIds(sourceIds);

        Map<String, String> param = metadataCreateTableDto.getParam();

        Set<String> dataSourceIds = new HashSet<>(metadataCreateTableDto.getSourceIds());
        dataSourceIds.add(metadataCreateTableDto.getTargetId());

        List<MetadataSourceEntity> metadataSourceEntities = metadataSourceService.listByIds(dataSourceIds);

        DbQuery dbQuery = null;

        List<String> createSql = new ArrayList<>();
        Map<String, List<ColumnDescription>> columnBySqlTypeMap = new LinkedHashMap<>();

        if (CollectionUtil.isNotEmpty(metadataSourceEntities)) {

            Map<String, MetadataSourceEntity> metadataSourceByIdMap = metadataSourceEntities.stream().collect(Collectors.toMap(MetadataSourceEntity::getId, v -> v));

            //目标数据源
            dataSource = metadataSourceByIdMap.get(metadataCreateTableDto.getTargetId());
            DbType dbType = DbType.getDbType(dataSource.getDbType());

            DbSchema dbSchema = dataSource.getDbSchema();
            DbQueryProperty dbQueryProperty = new DbQueryProperty(dbType, dbSchema.getHost(), dbSchema.getUsername(), dbSchema.getPassword(), dbSchema.getPort(), dbSchema.getDbName(), dbSchema.getSid());
            dbQuery = dataSourceFactory.createDbQuery(dbQueryProperty);
            //创建sql,参数准备
            String targetSchemaName = metadataCreateTableDto.getTargetSchemaName();
            String targetTableName = metadataCreateTableDto.getTargetTableName();
            String sourceTableRemarks = metadataCreateTableDto.getSourceTableRemarks();

            String sqlType = metadataCreateTableDto.getSqlType();

            columnBySqlTypeMap.put("renameColumn", new ArrayList<>());
            columnBySqlTypeMap.put("addColumn", new ArrayList<>());
            columnBySqlTypeMap.put("delColumn", new ArrayList<>());
            columnBySqlTypeMap.put("updateColumn", new ArrayList<>());


            if (CollectionUtil.isNotEmpty(metadataColumnMarketDtoList)) {
                Map<String, List<MetadataColumnMarketDto>> columnBySourceIdMap = metadataColumnMarketDtoList.stream().collect(Collectors.groupingBy(MetadataColumnMarketDto::getSourceId));

                for (String sourceId : columnBySourceIdMap.keySet()) {

                    MetadataSourceEntity tmpMetadataSourceEntity = metadataSourceByIdMap.get(sourceId);
                    DbType sourceDbType = DbType.getDbType(tmpMetadataSourceEntity.getDbType());

                    List<MetadataColumnMarketDto> tmpMetadataColumnEntities = columnBySourceIdMap.get(sourceId);

                    for (MetadataColumnMarketDto metadataColumnEntity : tmpMetadataColumnEntities) {
                        ColumnDescription columnDescription = new ColumnDescription();
                        columnDescription.setFieldName(metadataColumnEntity.getColumnName());
                        columnDescription.setLabelName(metadataColumnEntity.getColumnName());
                        columnDescription.setFieldTypeName(metadataColumnEntity.getDataType());
                        columnDescription.setFieldType(metadataColumnEntity.getFieldType());
                        if (StringUtils.isNotBlank(metadataColumnEntity.getDataLength())) {
                            columnDescription.setDisplaySize(Integer.parseInt(metadataColumnEntity.getDataLength()));
                        }
                        if (StringUtils.isNotBlank(metadataColumnEntity.getDataScale())) {
                            columnDescription.setScaleSize(Integer.parseInt(metadataColumnEntity.getDataScale()));
                        }
                        if (StringUtils.isNotBlank(metadataColumnEntity.getDataPrecision())) {
                            columnDescription.setPrecisionSize(Integer.parseInt(metadataColumnEntity.getDataPrecision()));
                        }
                        columnDescription.setAutoIncrement(metadataColumnEntity.isAutoIncrement());
                        columnDescription.setNullable("1".equals(metadataColumnEntity.getColumnNullable()));
                        columnDescription.setRemarks(metadataColumnEntity.getColumnComment());
                        columnDescription.setProductType(sourceDbType);
                        columnDescription.setDefaultValue(metadataColumnEntity.getDataDefault());

                        List<String> updateKeys = metadataColumnEntity.getUpdateKeys();
                        String actionType = metadataColumnEntity.getActionType();

                        if (StringUtils.isEmpty(actionType)) {
                            continue;
                        }

                        List<ColumnDescription> columnDescriptions = columnBySqlTypeMap.get(actionType);
                        if (CollectionUtil.isEmpty(columnDescriptions)) {
                            columnDescriptions = new ArrayList<>();
                        }
                        columnDescriptions.add(columnDescription);
                        columnBySqlTypeMap.put(actionType, columnDescriptions);

                        if (actionType.equals("updateColumn")) {
                            if (MapUtil.isNotEmpty(metaColumnByIdMap) && metaColumnByIdMap.containsKey(metadataColumnEntity.getId())) {
                                String columnName = metaColumnByIdMap.get(metadataColumnEntity.getId()).getColumnName();
                                if (!columnName.equals(columnDescription.getFieldName()) && updateKeys.contains("columnName")) {
                                    actionType = "renameColumn";

                                    Gson gson = new Gson();
                                    ColumnDescription reNameColumnDescription = gson.fromJson(JSONObject.toJSONString(columnDescription), ColumnDescription.class);
                                    reNameColumnDescription.setOldName(columnName);
                                    List<ColumnDescription> reNameColumnDescriptions = columnBySqlTypeMap.get(actionType);
                                    if (CollectionUtil.isEmpty(reNameColumnDescriptions)) {
                                        reNameColumnDescriptions = new ArrayList<>();
                                    }
                                    reNameColumnDescriptions.add(reNameColumnDescription);
                                    columnBySqlTypeMap.put(actionType, reNameColumnDescriptions);
                                }
                            }
                        }
                    }
                }
            }


            Map<String, List<IndexMetaData>> indexBySqlTypeMap = new LinkedHashMap<>();
            indexBySqlTypeMap.put("addIndex", new ArrayList<>());
            indexBySqlTypeMap.put("delIndex", new ArrayList<>());

            if (CollectionUtil.isNotEmpty(metadataIndexMarketDtoList)) {
                for (MetadataIndexMarketDto metadataIndexEntity : metadataIndexMarketDtoList) {
                    IndexMetaData indexMetaData = new IndexMetaData();
                    BeanUtils.copyProperties(metadataIndexEntity, indexMetaData);

                    String actionType = metadataIndexEntity.getActionType();
                    if (StringUtils.isEmpty(actionType)) {
                        continue;
                    }
                    List<IndexMetaData> indexMetaDataList = indexBySqlTypeMap.get(actionType);
                    indexMetaDataList.add(indexMetaData);
                    indexBySqlTypeMap.put(actionType, indexMetaDataList);
                }
            }


            if (sqlType.equals("createTable")) {

                List<ColumnDescription> targetColumnDescriptions = columnBySqlTypeMap.get("addColumn");
                Assert.notNull(targetColumnDescriptions, "缺少建表字段信息");
                List<ColumnDescription> columnDescriptions = targetColumnDescriptions.stream()
                        .filter(column -> StringUtils.isNotBlank(column.getFieldName()))
                        .collect(Collectors.toList());

                // 生成建表语句
                createSql = GenerateSqlUtils.getDDLCreateTableSQL(
                        dbQuery,
                        columnDescriptions,
                        targetPrimaryKeys,
                        param,
                        targetSchemaName,
                        targetTableName,
                        sourceTableRemarks,
                        false,
                        getTblProperties(metadataCreateTableDto, dbQuery, columnDescriptions)
                );
            } else if (sqlType.contains("updateTable")) {

                if (MapUtil.isNotEmpty(indexBySqlTypeMap)) {
                    for (String type : indexBySqlTypeMap.keySet()) {
                        List<IndexMetaData> targetIndexMetaData = indexBySqlTypeMap.get(type);
                        if (CollectionUtil.isEmpty(targetIndexMetaData)) {
                            continue;
                        }

                        if (type.contains("Index")) {
                            // 生成新增索引、删除索引语句
                            createSql = GenerateSqlUtils.getDelAndAddIndexSQL(
                                    dbQuery,
                                    targetIndexMetaData.stream()
                                            .filter(column -> StringUtils.isNotBlank(column.getIndexName()))
                                            .collect(Collectors.toList()),
                                    targetSchemaName,
                                    targetTableName,
                                    type
                            );
                        }
                    }
                }

                if (MapUtil.isNotEmpty(columnBySqlTypeMap)) {
                    for (String type : columnBySqlTypeMap.keySet()) {
                        List<ColumnDescription> columnDescriptions = columnBySqlTypeMap.get(type);
                        if (CollectionUtil.isEmpty(columnDescriptions)) {
                            continue;
                        }

                        if (type.contains("Column")) {
                            // 生成新增字段、删除、修改字段语句
                            createSql.addAll(GenerateSqlUtils.getUpdateColumnSQL(
                                    dbQuery,
                                    columnDescriptions.stream()
                                            .filter(column -> StringUtils.isNotBlank(column.getFieldName()))
                                            .collect(Collectors.toList()),
                                    targetPrimaryKeys,
                                    targetSchemaName,
                                    targetTableName,
                                    type));
                        }
                    }
                }

            }


        }

        if (CollectionUtil.isNotEmpty(createSql)) {
            StringBuilder sb = new StringBuilder();
            createSql.stream().distinct().forEach(sql -> sb.append(sql).append(sql.endsWith(";") ? "" : ";").append("\n"));
            log.info("生成sql如下：" + sb);
            return sb.toString();
        }

        return null;

    }


    @Override
    public String runSql(MetadataCreateSqlDto metadataCreateTableDto) {

        //创建sql
        String tableSql = metadataCreateTableDto.getTableSql();
        if (StringUtils.isEmpty(tableSql)) {
            return null;
        }
        MetadataSourceEntity metadataSourceEntity = metadataSourceService.getById(metadataCreateTableDto.getTargetId());
        //执行建表sql
        DbSchema dbSchema = metadataSourceEntity.getDbSchema();
        DbType dbType = DbType.getDbType(metadataSourceEntity.getDbType());
        DbQueryProperty dbQueryProperty = new DbQueryProperty(dbType, dbSchema.getHost(), dbSchema.getUsername(), dbSchema.getPassword(), dbSchema.getPort(), dbSchema.getDbName(), dbSchema.getSid());
        AbstractDbQueryFactory tableDbQuery = (AbstractDbQueryFactory) dataSourceFactory.createDbQuery(dbQueryProperty);
        JdbcTemplate jdbcTemplate = tableDbQuery.getJdbcTemplate();

        try {
            String[] split = tableSql.split(";");
            for (String sql : split) {
                if (StringUtils.isNotBlank(sql.replaceAll("[\\n\\s]", ""))) {
                    jdbcTemplate.execute(sql + ";");
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            // 关键：如果 tableDbQuery 使用的是独立数据源，应释放资源
            if (tableDbQuery instanceof AutoCloseable) {
                try {
                    ((AutoCloseable) tableDbQuery).close();
                } catch (Exception e) {
                    log.warn("Failed to close tableDbQuery", e);
                }
            }
        }

        //同步新表元数据
        MetadataSourceSyncVo metadataSourceSyncVo = new MetadataSourceSyncVo();
        BeanUtils.copyProperties(metadataSourceEntity, metadataSourceSyncVo);
        List<String> tables = new ArrayList<>();
        tables.add(metadataCreateTableDto.getTargetTableName());
        metadataSourceSyncVo.setTables(tables);
        asyncTask.doSyncTask(metadataSourceSyncVo);

        //数据来源回填
        LambdaQueryWrapper<MetadataTableEntity> tableLqw = new LambdaQueryWrapper<>();
        tableLqw.eq(MetadataTableEntity::getSourceId, metadataCreateTableDto.getTargetId());
        tableLqw.eq(MetadataTableEntity::getTableName, metadataCreateTableDto.getTargetTableName());
        MetadataTableEntity metadataTableEntity = metadataTableDao.selectOne(tableLqw);

        if (ObjectUtil.isNotEmpty(metadataTableEntity)) {
            LambdaQueryWrapper<MetadataColumnEntity> columnLqw = new LambdaQueryWrapper<>();
            columnLqw.eq(MetadataColumnEntity::getTableId, metadataTableEntity.getId());
            List<MetadataColumnEntity> metadataColumnEntities = metadataColumnService.list(columnLqw);

            if (CollectionUtil.isNotEmpty(metadataColumnEntities)) {
                Map<String, MetadataColumnEntity> newMetaColumnByNameMap = metadataColumnEntities.stream().collect(Collectors.toMap(MetadataColumnEntity::getColumnName, metadataColumnEntity -> metadataColumnEntity));

                Map<String, MetadataColumnEntity> oldMetaColumnByNameMap = metadataCreateTableDto.getMetadataColumnMarketDtoList().stream().collect(Collectors.toMap(MetadataColumnEntity::getColumnName, metadataColumnEntity -> metadataColumnEntity));

                for (String columnName : newMetaColumnByNameMap.keySet()) {
                    if (oldMetaColumnByNameMap.containsKey(columnName)) {
                        MetadataColumnEntity metadataColumnEntity = newMetaColumnByNameMap.get(columnName);
                        MetadataColumnEntity oldMetadataColumnEntity = oldMetaColumnByNameMap.get(columnName);

                        metadataColumnEntity.setSourceColumnId(oldMetadataColumnEntity.getId());
                        metadataColumnEntity.setSourceTableId(oldMetadataColumnEntity.getTableId());
                        metadataColumnEntity.setSourceDataId(oldMetadataColumnEntity.getSourceId());
                    }
                }
                metadataColumnService.updateBatchById(metadataColumnEntities);
            }

            //更新缓存
//            metadataSourceService.refreshMetadata();
            return metadataTableEntity.getId();
        }

        return null;
    }

    public Long runDataBaseSql(MetadataSourceEntity metadataSourceEntity, String createSql, String dbName) {

        DbType dbType = DbType.getDbType(metadataSourceEntity.getDbType());

        DbSchema dbSchema = metadataSourceEntity.getDbSchema();
        dbSchema.setDbName(dbName);
        dbSchema.setSid(null);

        JobDatasource jobDatasource = jobDatasourceService.getById(metadataSourceEntity.getId());
        jobDatasource.setDatasourceName(null);
        jobDatasource.setId(null);
        jobDatasource.setSid(null);
        jobDatasource.setDatabaseName(dbName);
        jobDatasource.setHost(dbSchema.getHost());
        jobDatasource.setPort(dbSchema.getPort());
        jobDatasource.setDatabaseName(dbSchema.getDbName());
        jobDatasource.setIsSync("0");

        String url = dbType.getUrl().replace("${host}", dbSchema.getHost()).replace("${port}",String.valueOf(dbSchema.getPort())).replace("${dbName}",  dbName);
        jobDatasource.setJdbcUrl(url);

        jobDatasourceService.saveJobDatasource(jobDatasource);

        //创建sql
        if (StringUtils.isEmpty(createSql) || jobDatasource.getId() == null) {
            return null;
        }

        DbQueryProperty dbQueryProperty = new DbQueryProperty(dbType, dbSchema.getHost(), dbSchema.getUsername(), dbSchema.getPassword(), dbSchema.getPort(), dbSchema.getDbName(), dbSchema.getSid());
        AbstractDbQueryFactory tableDbQuery = (AbstractDbQueryFactory) dataSourceFactory.createDataBaseQuery(dbQueryProperty);
        JdbcTemplate jdbcTemplate = tableDbQuery.getJdbcTemplate();

        try {
            String[] split = createSql.split(";");
            for (String sql : split) {
                if (StringUtils.isNotBlank(sql.replaceAll("[\\n\\s]", ""))) {
                    jdbcTemplate.execute(sql + ";");
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            // 关键：如果 tableDbQuery 使用的是独立数据源，应释放资源
            if (tableDbQuery instanceof AutoCloseable) {
                try {
                    ((AutoCloseable) tableDbQuery).close();
                } catch (Exception e) {
                    log.warn("Failed to close tableDbQuery", e);
                }
            }
        }

        //同步新表元数据
        MetadataSourceSyncVo metadataSourceSyncVo = new MetadataSourceSyncVo();
        metadataSourceEntity.setId(String.valueOf(jobDatasource.getId()));
        metadataSourceEntity.setDbSchema(dbSchema);
        BeanUtils.copyProperties(metadataSourceEntity, metadataSourceSyncVo);
        asyncTask.doSyncTask(metadataSourceSyncVo);

        return jobDatasource.getId();
    }

    @Override
    public Long createDataBase(MetadataCreateSqlDto metadataCreateSqlDto) {

        MetadataSourceEntity dataSource = metadataSourceService.getById(metadataCreateSqlDto.getTargetId());
        String sqlType = metadataCreateSqlDto.getSqlType();

        if (Objects.isNull(dataSource) || StringUtils.isBlank(metadataCreateSqlDto.getSqlType())) {
            return null;
        }

        DbType dbType = DbType.getDbType(dataSource.getDbType());

        DbSchema dbSchema = dataSource.getDbSchema();
        DbQueryProperty dbQueryProperty = new DbQueryProperty(dbType, dbSchema.getHost(), dbSchema.getUsername(), dbSchema.getPassword(), dbSchema.getPort(), dbSchema.getDbName(), dbSchema.getSid());
        DbQuery dbQuery = dataSourceFactory.createDataBaseQuery(dbQueryProperty);

        String createSql = "";
        if ("createDataBase".equals(sqlType)) {
            createSql = GenerateSqlUtils.getDDlCreateDataBaseSQL(
                    dbQuery,
                    metadataCreateSqlDto.getTargetSchemaName()
            );
        }
        log.info("生成sql如下：" + createSql);



        return runDataBaseSql(dataSource, createSql, metadataCreateSqlDto.getTargetSchemaName());
    }

    public SourceProperties getTblProperties(MetadataCreateSqlDto metadataCreateTableDto, DbQuery dbQuery, List<ColumnDescription> columnDescriptions) {
        List<String> columnNames = columnDescriptions.stream()
                .map(ColumnDescription::getFieldName)
                .collect(Collectors.toList());
        DbType dbType = dbQuery.getDbType();
        SourceProperties param = new SourceProperties();
        param.setProductType(dbType);
        param.setDriverClass(dbType.getJdbc());
        param.setJdbcUrl(dbType.getUrl());
//        param.setUsername(sourceDataSource.getUserName());
//        param.setPassword(sourceDataSource.getPassword());
        param.setSchemaName(metadataCreateTableDto.getTargetSchemaName());
        param.setTableName(metadataCreateTableDto.getTargetTableName());
        param.setColumnNames(columnNames);
        return param;
    }


}
