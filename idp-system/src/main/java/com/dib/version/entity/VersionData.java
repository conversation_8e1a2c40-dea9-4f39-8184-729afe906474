package com.dib.version.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@TableName("data_version_contro")
@Schema(description = "FTP配置Model")
@Data
public class VersionData {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    public String id;

    @Schema(description = "版本号")
    @TableField("version")
    public String version;

    @Schema(description = "发布时间")
    @TableField("post_time")
    public Date postTime;

    @Schema(description = "发布内容")
    @TableField("content")
    public String content;
}
