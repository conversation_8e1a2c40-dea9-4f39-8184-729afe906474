package com.dib.bigdata.tool.flinkx.reader;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.dib.bigdata.entity.JobDatasource;
import com.dib.bigdata.tool.flinkx.BaseFlinkxPlugin;
import com.dib.bigdata.tool.pojo.FlinkxHbasePojo;
import com.dib.bigdata.tool.pojo.FlinkxHivePojo;
import com.dib.bigdata.tool.pojo.FlinkxMongoDBPojo;
import com.dib.bigdata.tool.pojo.FlinkxRdbmsPojo;
import com.dib.bigdata.util.AESUtil;
import com.dib.core.util.Constants;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * Reader
 *
 * <AUTHOR>
 * @ClassName BaseReaderPlugin
 * @Version 1.0
 * @since 2019/8/2 16:27
 */
public abstract class BaseReaderPlugin extends BaseFlinkxPlugin {

    /**
     * 默认的字段是 ["column1","column2"],如果不同 则需要覆盖掉
     * @param columns
     * @return
     */
    @Override
    public List<Object>  getColumn(List<String> columns) {
        List<Object> data = Lists.newArrayList();
        columns.forEach(c -> {
            data.add(c.split(Constants.SPLIT_SCOLON)[0]);
        });
        return data;
    }

    @Override
    public Map<String, Object> build(FlinkxRdbmsPojo plugin) {
        //构建
        Map<String, Object> readerObj = Maps.newLinkedHashMap();
        readerObj.put("name", getName());
        Map<String, Object> parameterObj = Maps.newLinkedHashMap();
        Map<String, Object> connectionObj = Maps.newLinkedHashMap();

        JobDatasource jobDatasource = plugin.getJobDatasource();
        //将用户名和密码进行解密
//        parameterObj.put("username", AESUtil.decrypt(jobDatasource.getJdbcUsername()));
//        parameterObj.put("password", AESUtil.decrypt(jobDatasource.getJdbcPassword()));
        parameterObj.put("username", jobDatasource.getJdbcUsername());
        parameterObj.put("password", jobDatasource.getJdbcPassword());

        //判断是否是 querySql
        if (StrUtil.isNotBlank(plugin.getQuerySql())) {
            connectionObj.put("querySql", ImmutableList.of(plugin.getQuerySql()));
        } else {
            parameterObj.put("column", plugin.getRdbmsColumns());
            //判断是否有where
            if (StringUtils.isNotBlank(plugin.getWhereParam())) {
                parameterObj.put("where", plugin.getWhereParam());
            }
            connectionObj.put("table", plugin.getTables());
        }
        parameterObj.put("splitPk",plugin.getSplitPk());
        connectionObj.put("jdbcUrl", ImmutableList.of(jobDatasource.getJdbcUrl()));

        parameterObj.put("connection", ImmutableList.of(connectionObj));

        readerObj.put("parameter", parameterObj);

        return readerObj;
    }

    @Override
    public Map<String, Object> buildHive(FlinkxHivePojo flinkxHivePojo) {
        return null;
    }

    @Override
    public Map<String, Object> buildHbase(FlinkxHbasePojo flinkxHbasePojo) { return null; }

    @Override
    public Map<String, Object> buildMongoDB(FlinkxMongoDBPojo flinkxMongoDBPojo) {
        return null;
    }
}
