package com.dib.bigdata.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.bigdata.entity.BaseForm;
import com.dib.bigdata.mapper.JobDatasourceMapper;
import com.dib.bigdata.entity.JobDatasource;
import com.dib.bigdata.service.JobDatasourceService;
import com.dib.bigdata.tool.query.BaseQueryTool;
import com.dib.bigdata.tool.query.HBaseQueryTool;
import com.dib.bigdata.tool.query.MongoDBQueryTool;
import com.dib.bigdata.tool.query.QueryToolFactory;
import com.dib.bigdata.util.AESUtil;
import com.dib.bigdata.util.JdbcConstants;
import com.dib.common.database.constants.DbType;
import com.dib.metadata.dto.DbSchema;
import com.dib.metadata.entity.MetadataSourceEntity;
import com.dib.metadata.service.MetadataSourceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.dib.common.utils.SecurityUtils.getUsername;

/**
 * Created by jingwk on 2022/01/30
 */
@Service
@Transactional(readOnly = true)
public class JobDatasourceServiceImpl extends ServiceImpl<JobDatasourceMapper, JobDatasource> implements JobDatasourceService {

    protected static final Logger logger = LoggerFactory.getLogger(JobDatasourceServiceImpl.class);


    @Autowired
    private JobDatasourceMapper datasourceMapper;
    @Autowired
    private MetadataSourceService metadataSourceService;

    @Override
    public Boolean  dataSourceTest(JobDatasource jobDatasource) throws IOException {
        if (JdbcConstants.HBASE.equals(jobDatasource.getDatasource())) {
            return new HBaseQueryTool(jobDatasource).dataSourceTest();
        }
//        String userName = AESUtil.decrypt(jobDatasource.getJdbcUsername());
        //  判断账密是否为密文
//        if (userName == null) {
//            jobDatasource.setJdbcUsername(AESUtil.encrypt(jobDatasource.getJdbcUsername()));
//        }
//        String pwd = AESUtil.decrypt(jobDatasource.getJdbcPassword());
//        if (pwd == null) {
//            jobDatasource.setJdbcPassword(AESUtil.encrypt(jobDatasource.getJdbcPassword()));
//        }
        if (JdbcConstants.MONGODB.equals(jobDatasource.getDatasource())) {
            return new MongoDBQueryTool(jobDatasource).dataSourceTest(jobDatasource.getDatabaseName());
        }
        DbType dbType = DbType.getDbTypeByDesc(jobDatasource.getDatasource());
        Assert.notNull(dbType, "数据库类型不存在");

        try {
            String url = dbType.getUrl().replace("${host}", jobDatasource.getHost()).replace("${port}",String.valueOf(jobDatasource.getPort())).replace("${dbName}",  jobDatasource.getDatabaseName());
            jobDatasource.setJdbcUrl(url);
            jobDatasource.setJdbcDriverClass(dbType.getJdbc());
        }catch (Exception e){
            log.error("还原url失败",e);
            return false;
        }

        BaseQueryTool queryTool = QueryToolFactory.getByDbType(jobDatasource);
        return queryTool.dataSourceTest();
    }

    @Override
    public int update(JobDatasource datasource) {
        return datasourceMapper.update(datasource);
    }

    @Override
    public List<JobDatasource> selectAllDatasource() {
        return datasourceMapper.selectList(null);
//        return jobDatasources.stream().peek(item -> {
//            String userName = AESUtil.decrypt(item.getJdbcUsername());
//            String decrypt = AESUtil.decrypt(item.getJdbcPassword());
//            item.setJdbcUsername(userName);
//            item.setJdbcPassword(decrypt);
//        }).collect(Collectors.toList());
    }

    @Override
    public List<JobDatasource> findDataSourceName() {
        return datasourceMapper.findDataSourceName();
    }

    @Override
    public JobDatasource getDataSourceById(Long datasourceId) {
        return datasourceMapper.getDataSourceById(datasourceId);
    }

    @Override
    @Transactional
    public Boolean saveJobDatasource(JobDatasource dataSource) {
        DbType dbType = DbType.getDbTypeByDesc(dataSource.getDatasource());
        Assert.notNull(dbType, "数据库类型不存在");

        try {
            dataSource.setCreateDate(new Date());
            dataSource.setUpdateDate(new Date());
            dataSource.setCreateBy(getUsername());
            dataSource.setUpdateBy(getUsername());
            String url = dbType.getUrl().replace("${host}", dataSource.getHost()).replace("${port}",String.valueOf(dataSource.getPort())).replace("${dbName}",  dataSource.getDatabaseName());
            dataSource.setJdbcUrl(url);
            dataSource.setJdbcDriverClass(dbType.getJdbc());
            datasourceMapper.insert(dataSource);

            metadataSourceService.saveMetadataSource(dataSource,dbType);
        }catch (Exception e){
            log.error("保存数据源失败",e);
            return false;
        }



        return true;
    }

    @Override
    @Transactional
    public Boolean updateJobDatasource(JobDatasource dataSource) {
        DbType dbType = DbType.getDbTypeByDesc(dataSource.getDatasource());
        Assert.notNull(dbType, "数据库类型不存在");

        try {
            dataSource.setUpdateDate(new Date());
            dataSource.setUpdateBy(getUsername());
            String url = dbType.getUrl().replace("${host}", dataSource.getHost()).replace("${port}",String.valueOf(dataSource.getPort())).replace("${dbName}",  dataSource.getDatabaseName());
            dataSource.setJdbcUrl(url);
            datasourceMapper.updateById(dataSource);

            metadataSourceService.updateMetadataSource(dataSource,dbType);
        }catch (Exception e){
            log.error("保存数据源失败",e);
            return false;
        }

        return true;
    }


    @Override
    @Transactional
    public Boolean removeJobDatasourceIds(List<Long> idList) {
        try {
            this.removeByIds(idList);
            List<String> newIdList = new ArrayList<>();
            idList.forEach(id -> {
                newIdList.add(String.valueOf(id));
            });

            metadataSourceService.removeByIds(newIdList);

        }catch (Exception e){
            log.error("删除数据源失败",e);
            return false;
        }

        return true;
    }

    @Override
    public IPage<JobDatasource> selectAll() {
        BaseForm form = new BaseForm();
        Page plusPagingQueryEntity = form.getPlusPagingQueryEntity();
        String sysId = form.getSysId();

        Page<JobDatasource> page = new Page<>(plusPagingQueryEntity.getCurrent(), plusPagingQueryEntity.getSize());
        LambdaQueryWrapper<JobDatasource> lqw = new LambdaQueryWrapper<>();
        lqw.eq(JobDatasource::getSysId,sysId);
        IPage<JobDatasource> jobDatasourcePage = datasourceMapper.selectPage(page, lqw);

        if (jobDatasourcePage.getTotal() > 0){
            // 取出所有的id，并转为String，收集为List<String>
            List<String> idList = jobDatasourcePage.getRecords().stream()
                    .map(JobDatasource::getId)
                    .map(String::valueOf)
                    .collect(Collectors.toList());

            List<MetadataSourceEntity> metadataSourcesByIds = metadataSourceService.getMetadataSourcesByIds(idList);
            // 创建一个Map来快速查找MetadataSourceEntity
            Map<String, MetadataSourceEntity> metadataSourceMap = metadataSourcesByIds.stream().filter(metadataSourceEntity -> metadataSourceEntity.getDbSchema() != null)
                    .collect(Collectors.toMap(MetadataSourceEntity::getId, metadataSource -> metadataSource));

            // 将dbSchema设置到JobDatasource对象中
            jobDatasourcePage.getRecords().forEach(jobDatasource -> {
                String id = String.valueOf(jobDatasource.getId());
                if (metadataSourceMap.containsKey(id)) {
                    MetadataSourceEntity metadataSource = metadataSourceMap.get(id);
                    jobDatasource.setIsSync(metadataSource.getIsSync());
                    DbSchema dbSchema = metadataSource.getDbSchema();
                    if (dbSchema != null){
                        jobDatasource.setHost(dbSchema.getHost());
                        jobDatasource.setPort(dbSchema.getPort());
                    }
                }
            });

        }


        return jobDatasourcePage;
    }

    @Override
    public List<JobDatasource> getSourceListBySysId(String sysId) {
        LambdaQueryWrapper<JobDatasource> lqw = new LambdaQueryWrapper<>();
        lqw.eq(JobDatasource::getSysId, sysId);
        return datasourceMapper.selectList(lqw);
//        return jobDatasources.stream().peek(item -> {
//            String userName = AESUtil.decrypt(item.getJdbcUsername());
//            String decrypt = AESUtil.decrypt(item.getJdbcPassword());
//            item.setJdbcUsername(userName);
//            item.setJdbcPassword(decrypt);
//        }).collect(Collectors.toList());
    }
}