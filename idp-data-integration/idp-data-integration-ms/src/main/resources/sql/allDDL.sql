create table if not exists public.alart_log
(
    id            serial,
    job_config_id bigint  not null,
    job_name      varchar(255),
    message       varchar(512),
    type          integer not null,
    status        integer not null,
    fail_log      text,
    is_deleted    integer not null,
    create_time   timestamp,
    edit_time     timestamp,
    creator       varchar(32),
    editor        varchar(32)
    );

alter table public.alart_log
    owner to postgres;

create unique index if not exists alart_log_id_idx
    on public.alart_log (id);

create table if not exists public.dev_env_setting
(
    id          serial,
    name        varchar(100),
    prop_value  varchar(300),
    description varchar(500),
    user_id     integer,
    flag        integer,
    create_time timestamp,
    update_time timestamp
    );

alter table public.dev_env_setting
    owner to postgres;

create table if not exists public.gen_table
(
    table_id          serial,
    table_name        varchar(200),
    table_comment     varchar(500),
    sub_table_name    varchar(64),
    sub_table_fk_name varchar(64),
    class_name        varchar(100),
    tpl_category      varchar(200),
    package_name      varchar(100),
    module_name       varchar(30),
    business_name     varchar(30),
    function_name     varchar(50),
    function_author   varchar(50),
    gen_type          varchar(1),
    gen_path          varchar(200),
    options           varchar(1000),
    create_by         varchar(64),
    create_time       timestamp,
    update_by         varchar(64),
    update_time       timestamp,
    remark            varchar(500)
    );

alter table public.gen_table
    owner to postgres;

create table if not exists public.gen_table_column
(
    column_id      serial,
    table_id       varchar(64),
    column_name    varchar(200),
    column_comment varchar(500),
    column_type    varchar(100),
    java_type      varchar(500),
    java_field     varchar(200),
    is_pk          varchar(1),
    is_increment   varchar(1),
    is_required    varchar(1),
    is_insert      varchar(1),
    is_edit        varchar(1),
    is_list        varchar(1),
    is_query       varchar(1),
    query_type     varchar(200),
    html_type      varchar(200),
    dict_type      varchar(200),
    sort           integer,
    create_by      varchar(64),
    create_time    timestamp,
    update_by      varchar(64),
    update_time    timestamp
    );

alter table public.gen_table_column
    owner to postgres;

create table if not exists public.ip_status
(
    id          serial,
    ip          varchar(64),
    status      integer   not null,
    last_time   timestamp,
    is_deleted  integer   not null,
    create_time timestamp not null,
    edit_time   timestamp not null,
    creator     varchar(32),
    editor      varchar(32)
    );

alter table public.ip_status
    owner to postgres;

create table if not exists public.job_alarm_config
(
    id          serial,
    job_id      bigint  not null,
    type        integer not null,
    version     integer not null,
    is_deleted  integer not null,
    create_time timestamp,
    edit_time   timestamp,
    creator     varchar(32),
    editor      varchar(32),
    cron        varchar(255)
    );

alter table public.job_alarm_config
    owner to postgres;

create table if not exists public.job_config
(
    id                      serial,
    job_name                varchar(64),
    job_desc                varchar(255),
    deploy_mode             varchar(64),
    flink_run_config        varchar(512),
    flink_sql               text,
    flink_checkpoint_config varchar(512),
    job_id                  varchar(64),
    is_open                 integer not null,
    status                  integer not null,
    ext_jar_path            varchar(2048),
    last_start_time         timestamp,
    last_run_log_id         bigint,
    version                 integer not null,
    job_type                integer not null,
    custom_args             varchar(128),
    custom_main_class       varchar(128),
    custom_jar_url          varchar(128),
    is_deleted              integer not null,
    create_time             timestamp,
    edit_time               timestamp,
    creator                 varchar(32),
    editor                  varchar(32),
    flinkcluster            bigint,
    cron                    varchar(255)
    );

alter table public.job_config
    owner to postgres;

create table if not exists public.job_config_history
(
    id                      serial,
    job_config_id           bigint  not null,
    job_name                varchar(64),
    job_desc                varchar(255),
    deploy_mode             varchar(64),
    flink_run_config        varchar(512),
    flink_sql               text,
    flink_checkpoint_config varchar(512),
    ext_jar_path            varchar(2048),
    version                 integer not null,
    job_type                integer not null,
    is_deleted              integer not null,
    create_time             timestamp,
    edit_time               timestamp,
    creator                 varchar(32),
    editor                  varchar(32)
    );

alter table public.job_config_history
    owner to postgres;

create table if not exists public.job_group
(
    id           serial,
    app_name     varchar(64),
    title        varchar(50),
    "order"      integer not null,
    address_type integer not null,
    address_list varchar(512)
    );

alter table public.job_group
    owner to postgres;

create table if not exists public.job_info
(
    id                        serial,
    job_group                 integer not null,
    job_cron                  varchar(128),
    job_desc                  varchar(255),
    project_id                integer,
    add_time                  timestamp,
    update_time               timestamp,
    user_id                   integer not null,
    alarm_email               varchar(255),
    executor_route_strategy   varchar(50),
    executor_handler          varchar(255),
    executor_param            varchar(512),
    executor_block_strategy   varchar(50),
    executor_timeout          integer not null,
    executor_fail_retry_count integer not null,
    glue_type                 varchar(50),
    glue_source               text,
    glue_remark               varchar(128),
    glue_updatetime           timestamp,
    child_jobid               varchar(255),
    trigger_status            integer not null,
    trigger_last_time         bigint  not null,
    trigger_next_time         bigint  not null,
    job_json                  text,
    replace_param             varchar(100),
    jvm_param                 varchar(200),
    inc_start_time            timestamp,
    partition_info            varchar(100),
    last_handle_code          integer,
    replace_param_type        varchar(255),
    reader_table              varchar(255),
    primary_key               varchar(50),
    inc_start_id              varchar(20),
    increment_type            integer,
    datasource_id             bigint
    );

alter table public.job_info
    owner to postgres;

create table if not exists public.job_jdbc_datasource
(
    id                varchar(50) not null,
    datasource_name   varchar(200),
    datasource        varchar(45),
    datasource_group  varchar(200),
    database_name     varchar(45),
    jdbc_username     varchar(100),
    jdbc_password     varchar(200),
    jdbc_url          varchar(500),
    jdbc_driver_class varchar(200),
    zk_adress         varchar(200),
    status            integer     not null,
    create_by         varchar(200),
    create_date       timestamp,
    update_by         varchar(200),
    update_date       timestamp,
    comments          varchar(1000),
    schema            varchar(255),
    sys_id            varchar(50)
    );

comment on column public.job_jdbc_datasource.sys_id is '菜单id';

alter table public.job_jdbc_datasource
    owner to postgres;

create table if not exists public.job_lock
(
    lock_name varchar(50)
    );

alter table public.job_lock
    owner to postgres;

create table if not exists public.job_log
(
    id                        bigint  not null,
    job_group                 integer not null,
    job_id                    integer not null,
    job_desc                  varchar(255),
    executor_address          varchar(255),
    executor_handler          varchar(255),
    executor_param            varchar(512),
    executor_sharding_param   varchar(20),
    executor_fail_retry_count integer,
    trigger_time              timestamp,
    trigger_code              integer not null,
    trigger_msg               text,
    handle_time               timestamp,
    handle_code               integer not null,
    handle_msg                text,
    alarm_status              integer,
    process_id                varchar(20),
    max_id                    bigint
    );

alter table public.job_log
    owner to postgres;

create table if not exists public.job_log_report
(
    id            serial,
    trigger_day   timestamp,
    running_count integer not null,
    suc_count     integer not null,
    fail_count    integer not null
);

alter table public.job_log_report
    owner to postgres;

create table if not exists public.job_logglue
(
    id          serial,
    job_id      integer not null,
    glue_type   varchar(50),
    glue_source text,
    glue_remark varchar(128),
    add_time    timestamp,
    update_time timestamp
    );

alter table public.job_logglue
    owner to postgres;

create table if not exists public.job_permission
(
    id          serial,
    name        varchar(50),
    description varchar(11),
    url         varchar(255),
    pid         integer
    );

alter table public.job_permission
    owner to postgres;

create table if not exists public.job_project
(
    id          serial,
    name        varchar(100),
    description varchar(200),
    user_id     integer,
    flag        boolean default true not null,
    create_time timestamp,
    update_time timestamp
    );

alter table public.job_project
    owner to postgres;

create table if not exists public.job_registry
(
    id             serial,
    registry_group varchar(50),
    registry_key   varchar(191),
    registry_value varchar(191),
    cpu_usage      double precision,
    memory_usage   double precision,
    load_average   double precision,
    update_time    timestamp
    );

alter table public.job_registry
    owner to postgres;

create table if not exists public.job_run_log
(
    id             serial,
    job_config_id  bigint  not null,
    job_name       varchar(64),
    job_desc       varchar(255),
    deploy_mode    varchar(64),
    job_id         varchar(64),
    local_log      text,
    run_ip         varchar(64),
    remote_log_url varchar(128),
    start_time     timestamp,
    end_time       timestamp,
    job_status     varchar(32),
    is_deleted     integer not null,
    create_time    timestamp,
    edit_time      timestamp,
    creator        varchar(32),
    editor         varchar(32)
    );

alter table public.job_run_log
    owner to postgres;

create table if not exists public.job_template
(
    id                        serial,
    job_group                 integer not null,
    job_cron                  varchar(128),
    job_desc                  varchar(255),
    add_time                  timestamp,
    update_time               timestamp,
    user_id                   integer not null,
    alarm_email               varchar(255),
    executor_route_strategy   varchar(50),
    executor_handler          varchar(255),
    executor_param            varchar(512),
    executor_block_strategy   varchar(50),
    executor_timeout          integer not null,
    executor_fail_retry_count integer not null,
    glue_type                 varchar(50),
    glue_source               text,
    glue_remark               varchar(128),
    glue_updatetime           timestamp,
    child_jobid               varchar(255),
    trigger_last_time         bigint  not null,
    trigger_next_time         bigint  not null,
    job_json                  text,
    jvm_param                 varchar(200),
    project_id                integer
    );

alter table public.job_template
    owner to postgres;

create table if not exists public.job_user
(
    id         serial,
    username   varchar(50),
    password   varchar(100),
    role       varchar(50),
    permission varchar(255)
    );

alter table public.job_user
    owner to postgres;

create table if not exists public.lark_base_resource
(
    id               serial,
    name             varchar(100),
    resource_address varchar(100),
    update_time      varchar(100),
    serverip         varchar(255),
    serveruser       varchar(255),
    serverpassword   varchar(255),
    type             varchar(255)
    );

alter table public.lark_base_resource
    owner to postgres;

create table if not exists public.market_api
(
    id                     varchar(50) not null,
    status                 varchar(50),
    create_by              varchar(50),
    create_time            timestamp,
    create_dept            varchar(50),
    update_by              varchar(50),
    update_time            timestamp,
    remark                 varchar(1000),
    api_name               varchar(255),
    api_version            varchar(10),
    api_url                varchar(255),
    req_method             varchar(10),
    res_type               varchar(10),
    deny                   varchar(2000),
    limit_json             text,
    config_json            text,
    req_json               text,
    res_json               text,
    api_code               varchar(255),
    asset_attribute_set_id varchar
    );

comment on column public.market_api.asset_attribute_set_id is '资产id';

alter table public.market_api
    owner to postgres;

create table if not exists public.market_api_log
(
    id            varchar(50),
    api_id        varchar(50),
    caller_id     varchar(50),
    caller_ip     varchar(50),
    caller_url    varchar(255),
    caller_params varchar(2000),
    caller_date   timestamp,
    caller_size   integer,
    time          integer,
    msg           varchar(2000),
    status        varchar(50)
    );

alter table public.market_api_log
    owner to postgres;

create table if not exists public.market_api_mask
(
    id          varchar(50),
    status      integer,
    create_by   varchar(50),
    create_time timestamp,
    create_dept varchar(50),
    update_by   varchar(50),
    update_time timestamp,
    remark      varchar(1000),
    api_id      varchar(50),
    mask_name   varchar(50),
    config_json text
    );

alter table public.market_api_mask
    owner to postgres;

create table if not exists public.market_service_integration
(
    id               varchar(50),
    status           integer,
    create_by        varchar(50),
    create_time      timestamp,
    create_dept      varchar(50),
    update_by        varchar(50),
    update_time      timestamp,
    remark           varchar(1000),
    service_no       varchar(255),
    service_name     varchar(255),
    service_type     integer,
    httpservice_json jsonb,
    webservice_json  jsonb
    );

alter table public.market_service_integration
    owner to postgres;

create table if not exists public.market_service_log
(
    id            varchar(50),
    service_id    varchar(50),
    caller_id     varchar(50),
    caller_ip     varchar(50),
    caller_date   timestamp,
    caller_header varchar(1000),
    caller_param  varchar(1000),
    caller_soap   varchar(1000),
    time          integer,
    msg           varchar(2000),
    status        integer
    );

alter table public.market_service_log
    owner to postgres;

create table if not exists public.metadata_change_record
(
    id              varchar(50),
    status          integer,
    create_by       varchar(50),
    create_time     timestamp,
    create_dept     varchar(50),
    update_by       varchar(50),
    update_time     timestamp,
    remark          varchar(1000),
    version         integer,
    object_type     varchar(255),
    object_id       varchar(255),
    field_name      varchar(255),
    field_old_value varchar(255),
    field_new_value varchar(255)
    );

alter table public.metadata_change_record
    owner to postgres;

create table if not exists public.quality_check_report
(
    id                varchar(50),
    check_rule_id     varchar(50),
    check_date        timestamp,
    check_result      varchar(1000),
    check_total_count integer,
    check_error_count integer,
    check_batch       varchar(50)
    );

alter table public.quality_check_report
    owner to postgres;

create table if not exists public.quality_check_rule
(
    id                  varchar(50),
    status              varchar(50),
    create_by           varchar(50),
    create_time         timestamp,
    create_dept         varchar(50),
    update_by           varchar(50),
    update_time         timestamp,
    remark              varchar(1000),
    rule_name           varchar(50),
    rule_type_id        varchar(50),
    rule_item_id        varchar(50),
    rule_level_id       varchar(50),
    rule_db_type        varchar(50),
    rule_source_id      varchar(50),
    rule_source         varchar(50),
    rule_table_id       varchar(50),
    rule_table          varchar(50),
    rule_table_comment  varchar(50),
    rule_column_id      varchar(50),
    rule_column         varchar(50),
    rule_column_comment varchar(50),
    config_json         jsonb,
    rule_sql            varchar(1000),
    last_check_batch    varchar(50)
    );

alter table public.quality_check_rule
    owner to postgres;

create table if not exists public.quality_rule_item
(
    id           varchar(50),
    rule_type_id varchar(50),
    item_code    varchar(50),
    item_explain varchar(1000)
    );

alter table public.quality_rule_item
    owner to postgres;

create table if not exists public.quality_rule_level
(
    id   varchar(50),
    code varchar(50),
    name varchar(50)
    );

alter table public.quality_rule_level
    owner to postgres;

create table if not exists public.quality_rule_type
(
    id   varchar(50),
    name varchar(50),
    code varchar(50)
    );

alter table public.quality_rule_type
    owner to postgres;

create table if not exists public.quality_schedule_job
(
    id              varchar(50),
    job_name        varchar(100),
    bean_name       varchar(100),
    method_name     varchar(100),
    method_params   varchar(255),
    cron_expression varchar(255),
    status          integer
    );

alter table public.quality_schedule_job
    owner to postgres;

create table if not exists public.quality_schedule_log
(
    id              varchar(50),
    status          varchar(1),
    execute_job_id  varchar(50),
    execute_rule_id varchar(50),
    execute_date    timestamp,
    execute_result  varchar(1000),
    execute_batch   varchar(50)
    );

alter table public.quality_schedule_log
    owner to postgres;

create table if not exists public.savepoint_backup
(
    id             serial,
    job_config_id  bigint  not null,
    savepoint_path varchar(2048),
    backup_time    timestamp,
    is_deleted     integer not null,
    create_time    timestamp,
    edit_time      timestamp,
    creator        varchar(32),
    editor         varchar(32)
    );

alter table public.savepoint_backup
    owner to postgres;

create table if not exists public.standard_contrast
(
    id             varchar(50),
    status         integer,
    create_by      varchar(50),
    create_time    timestamp,
    create_dept    varchar(50),
    update_by      varchar(50),
    update_time    timestamp,
    remark         varchar(1000),
    source_id      varchar(50),
    source_name    varchar(50),
    table_id       varchar(50),
    table_name     varchar(50),
    table_comment  varchar(50),
    column_id      varchar(50),
    column_name    varchar(50),
    column_comment varchar(50),
    gb_type_id     varchar(50),
    bind_gb_column varchar(50)
    );

alter table public.standard_contrast
    owner to postgres;

create table if not exists public.standard_contrast_dict
(
    id             varchar(50),
    status         integer,
    create_by      varchar(50),
    create_time    timestamp,
    create_dept    varchar(50),
    update_by      varchar(50),
    update_time    timestamp,
    remark         varchar(1000),
    contrast_id    varchar(50),
    col_code       varchar(50),
    col_name       varchar(50),
    contrast_gb_id varchar(50)
    );

alter table public.standard_contrast_dict
    owner to postgres;

create table if not exists public.standard_dict
(
    id          varchar(50),
    status      integer,
    create_by   varchar(50),
    create_time timestamp,
    create_dept varchar(50),
    update_by   varchar(50),
    update_time timestamp,
    remark      varchar(1000),
    type_id     varchar(255),
    gb_code     varchar(255),
    gb_name     varchar(255)
    );

alter table public.standard_dict
    owner to postgres;

create table if not exists public.standard_type
(
    id           varchar(50),
    status       varchar(50),
    create_by    varchar(50),
    create_time  timestamp,
    create_dept  varchar(50),
    update_by    varchar(50),
    update_time  timestamp,
    remark       varchar(1000),
    gb_type_code varchar(255),
    gb_type_name varchar(255)
    );

alter table public.standard_type
    owner to postgres;

create table if not exists public.sys_api_config
(
    id               serial,
    api_name         varchar(255),
    data_source_id   integer,
    data_source_name varchar(255),
    query_sql        text,
    parameter        varchar(255),
    publickey        text,
    privatekey       text,
    request_mode     varchar(255),
    token            varchar(255),
    ip_rights        varchar(1),
    ip_address       varchar(255),
    expiration_time  timestamp,
    api_state        varchar(1),
    create_by        varchar(255),
    create_time      timestamp,
    update_by        varchar(255),
    update_time      timestamp,
    remark           varchar(500)
    );

alter table public.sys_api_config
    owner to postgres;

create table if not exists public.sys_config
(
    config_id    serial,
    config_name  varchar(100),
    config_key   varchar(100),
    config_value varchar(500),
    config_type  varchar(1),
    create_by    varchar(64),
    create_time  timestamp,
    update_by    varchar(64),
    update_time  timestamp,
    remark       varchar(500)
    );

alter table public.sys_config
    owner to postgres;

create table if not exists public.sys_data_base
(
    id               serial,
    base_name        varchar(255),
    data_source_id   integer,
    data_source_code varchar(255),
    data_source_name varchar(255),
    colony_state     varchar(255),
    colony_name      varchar(255),
    create_by        varchar(255),
    create_time      timestamp,
    update_by        varchar(255),
    update_time      timestamp,
    remark           varchar(255)
    );

alter table public.sys_data_base
    owner to postgres;

create table if not exists public.sys_data_rule
(
    id               serial,
    data_source_id   integer,
    data_source_code varchar(255),
    data_source_name varchar(255),
    data_layer_code  varchar(255),
    data_layer_name  varchar(255),
    rule_type_code   varchar(255),
    rule_type_name   varchar(255),
    rule_text        varchar(255),
    create_by        varchar(255),
    create_time      timestamp,
    update_by        varchar(255),
    update_time      timestamp,
    remark           varchar(255)
    );

alter table public.sys_data_rule
    owner to postgres;

create table if not exists public.sys_dept
(
    dept_id     serial,
    parent_id   bigint,
    ancestors   varchar(50),
    dept_name   varchar(30),
    order_num   integer,
    leader      varchar(20),
    phone       varchar(11),
    email       varchar(50),
    status      varchar(1),
    del_flag    varchar(1),
    create_by   varchar(64),
    create_time timestamp,
    update_by   varchar(64),
    update_time timestamp
    );

alter table public.sys_dept
    owner to postgres;

create table if not exists public.sys_dict_data
(
    dict_code   serial,
    dict_sort   integer,
    dict_label  varchar(100),
    dict_value  varchar(100),
    dict_type   varchar(100),
    css_class   varchar(100),
    list_class  varchar(100),
    is_default  varchar(1),
    status      varchar(1),
    create_by   varchar(64),
    create_time timestamp,
    update_by   varchar(64),
    update_time timestamp,
    remark      varchar(500),
    show_value  varchar
    );

alter table public.sys_dict_data
    owner to postgres;

create table if not exists public.sys_dict_type
(
    dict_id     serial,
    dict_name   varchar(100),
    dict_type   varchar(100),
    status      varchar(1),
    create_by   varchar(64),
    create_time timestamp,
    update_by   varchar(64),
    update_time timestamp,
    remark      varchar(500)
    );

alter table public.sys_dict_type
    owner to postgres;

create table if not exists public.sys_job
(
    job_id          serial,
    job_name        varchar(64),
    job_group       varchar(64),
    invoke_target   varchar(500),
    cron_expression varchar(255),
    misfire_policy  varchar(20),
    concurrent      varchar(1),
    status          varchar(1),
    create_by       varchar(64),
    create_time     timestamp,
    update_by       varchar(64),
    update_time     timestamp,
    remark          varchar(500)
    );

alter table public.sys_job
    owner to postgres;

create table if not exists public.sys_job_log
(
    job_log_id     serial,
    job_name       varchar(64),
    job_group      varchar(64),
    invoke_target  varchar(500),
    job_message    varchar(500),
    status         varchar(1),
    exception_info varchar(2000),
    create_time    timestamp
    );

alter table public.sys_job_log
    owner to postgres;

create table if not exists public.sys_logininfor
(
    info_id        serial,
    user_name      varchar(50),
    ipaddr         varchar(128),
    login_location varchar(255),
    browser        varchar(50),
    os             varchar(50),
    status         varchar(1),
    msg            varchar(255),
    login_time     timestamp
    );

alter table public.sys_logininfor
    owner to postgres;

create table if not exists public.sys_menu_bak2
(
    menu_id     integer default nextval('sys_menu_menu_id_seq'::regclass) not null,
    menu_name   varchar(50),
    parent_id   bigint,
    order_num   integer,
    path        varchar(200),
    component   varchar(255),
    query       varchar(255),
    is_frame    integer,
    is_cache    integer,
    menu_type   varchar(1),
    visible     varchar(1),
    status      varchar(1),
    perms       varchar(100),
    icon        varchar(100),
    create_by   varchar(64),
    create_time timestamp,
    update_by   varchar(64),
    update_time timestamp,
    remark      varchar(500)
    );

alter table public.sys_menu_bak2
    owner to postgres;

create table if not exists public.sys_menu_bak
(
    menu_id     serial,
    menu_name   varchar(50),
    parent_id   bigint,
    order_num   integer,
    path        varchar(200),
    component   varchar(255),
    query       varchar(255),
    is_frame    integer,
    is_cache    integer,
    menu_type   varchar(1),
    visible     varchar(1),
    status      varchar(1),
    perms       varchar(100),
    icon        varchar(100),
    create_by   varchar(64),
    create_time timestamp,
    update_by   varchar(64),
    update_time timestamp,
    remark      varchar(500)
    );

alter table public.sys_menu_bak
    owner to postgres;

create table if not exists public.sys_notice
(
    notice_id      serial,
    notice_title   varchar(50),
    notice_type    varchar(1),
    notice_content bytea,
    status         varchar(1),
    create_by      varchar(64),
    create_time    timestamp,
    update_by      varchar(64),
    update_time    timestamp,
    remark         varchar(255)
    );

alter table public.sys_notice
    owner to postgres;

create table if not exists public.sys_oper_log
(
    oper_id        serial,
    title          varchar(50),
    business_type  integer,
    method         varchar(100),
    request_method varchar(10),
    operator_type  integer,
    oper_name      varchar(50),
    dept_name      varchar(50),
    oper_url       varchar(255),
    oper_ip        varchar(128),
    oper_location  varchar(255),
    oper_param     varchar(2000),
    json_result    varchar(2000),
    status         integer,
    error_msg      varchar(2000),
    oper_time      timestamp
    );

alter table public.sys_oper_log
    owner to postgres;

create table if not exists public.sys_post
(
    post_id     serial,
    post_code   varchar(64),
    post_name   varchar(50),
    post_sort   integer not null,
    status      varchar(1),
    create_by   varchar(64),
    create_time timestamp,
    update_by   varchar(64),
    update_time timestamp,
    remark      varchar(500)
    );

alter table public.sys_post
    owner to postgres;

create table if not exists public.sys_role
(
    role_id             serial,
    role_name           varchar(30),
    role_key            varchar(100),
    role_sort           integer not null,
    data_scope          varchar(1),
    menu_check_strictly integer,
    dept_check_strictly integer,
    status              varchar(1),
    del_flag            varchar(1),
    create_by           varchar(64),
    create_time         timestamp,
    update_by           varchar(64),
    update_time         timestamp,
    remark              varchar(500)
    );

alter table public.sys_role
    owner to postgres;

create table if not exists public.sys_role_dept
(
    role_id bigint not null,
    dept_id bigint not null
);

alter table public.sys_role_dept
    owner to postgres;

create table if not exists public.sys_role_menu
(
    role_id bigint not null,
    menu_id bigint not null
);

alter table public.sys_role_menu
    owner to postgres;

create table if not exists public.sys_servers
(
    id               serial,
    groupname        varchar(255),
    groupcode        varchar(255),
    serveraddress    varchar(255),
    osname           varchar(255),
    starttime        varchar(255),
    pid              varchar(255),
    cpucores         integer,
    cpuutilization   double precision,
    cpurate          double precision,
    jvminitialmemory double precision,
    jvmmaxmemory     double precision,
    jvmusedmemory    double precision,
    physicalmemory   double precision,
    surplusmemory    double precision,
    usedmemory       double precision,
    diskstatus       varchar(255),
    create_time      timestamp,
    create_by        varchar(255)
    );

alter table public.sys_servers
    owner to postgres;

create table if not exists public.sys_table
(
    id               serial,
    parent_id        bigint,
    parent_name      varchar(255),
    ancestors        varchar(255),
    name             varchar(255),
    type             varchar(255),
    lenth            integer,
    comment          varchar(255),
    createtablequery text,
    action_type      varchar(255),
    theme            varchar(255),
    level_code       varchar(255),
    create_by        varchar(255),
    create_time      timestamp,
    update_by        varchar(255),
    update_time      timestamp
    );

alter table public.sys_table
    owner to postgres;

create table if not exists public.sys_user
(
    user_id     serial,
    dept_id     bigint,
    user_name   varchar(30),
    nick_name   varchar(30),
    user_type   varchar(2),
    email       varchar(50),
    phonenumber varchar(11),
    sex         varchar(1),
    avatar      varchar(100),
    password    varchar(100),
    status      varchar(1),
    del_flag    varchar(1),
    login_ip    varchar(128),
    login_date  timestamp,
    create_by   varchar(64),
    create_time timestamp,
    update_by   varchar(64),
    update_time timestamp,
    remark      varchar(500)
    );

alter table public.sys_user
    owner to postgres;

create table if not exists public.sys_user_post
(
    user_id bigint not null,
    post_id bigint not null
);

alter table public.sys_user_post
    owner to postgres;

create table if not exists public.sys_user_role
(
    user_id bigint not null,
    role_id bigint not null
);

alter table public.sys_user_role
    owner to postgres;

create table if not exists public.system_config
(
    id          serial,
    key         varchar(128),
    val         varchar(512),
    type        varchar(12),
    is_deleted  integer   not null,
    create_time timestamp not null,
    edit_time   timestamp not null,
    creator     varchar(32),
    editor      varchar(32)
    );

alter table public.system_config
    owner to postgres;

create table if not exists public.upload_file
(
    id            serial,
    file_name     varchar(128),
    file_path     varchar(512),
    type          integer not null,
    is_deleted    integer not null,
    create_time   timestamp,
    edit_time     timestamp,
    creator       varchar(32),
    editor        varchar(32),
    url           varchar(255),
    resource_type varchar(255),
    resource_name varchar(255),
    resource_id   integer,
    remarks       varchar(255)
    );

alter table public.upload_file
    owner to postgres;

create table if not exists public."user"
(
    id          serial,
    username    varchar(100),
    name        varchar(100),
    password    varchar(255),
    status      integer not null,
    is_deleted  integer not null,
    create_time timestamp,
    edit_time   timestamp,
    creator     varchar(32),
    editor      varchar(32)
    );

alter table public."user"
    owner to postgres;

create table if not exists public.sys_menu
(
    menu_id     bigint not null
    constraint sys_menu_pk
    primary key,
    menu_name   varchar(50),
    parent_id   bigint,
    order_num   integer,
    path        varchar(200),
    component   varchar(255),
    query       varchar(255),
    is_frame    varchar(50),
    is_cache    varchar(50),
    menu_type   varchar(1),
    visible     varchar(1),
    status      varchar(1),
    perms       varchar(100),
    icon        varchar(100),
    create_by   varchar(64),
    create_time timestamp,
    update_by   varchar(64),
    update_time timestamp,
    remark      varchar(500)
    );

alter table public.sys_menu
    owner to postgres;

create table if not exists public.dp_portal_group
(
    group_id       varchar(50) not null
    primary key,
    parent_id      varchar(50)  default NULL::character varying,
    group_name     varchar(100) default NULL::character varying,
    describe       text,
    group_type     varchar(30)  default NULL::character varying,
    group_order    integer,
    create_time    timestamp,
    update_time    timestamp,
    create_user_id varchar(50)  default NULL::character varying
    );

comment on table public.dp_portal_group is '分组表';

comment on column public.dp_portal_group.group_id is '分组编号(PK)';

comment on column public.dp_portal_group.parent_id is '父级分组编号(PK)';

comment on column public.dp_portal_group.group_name is '分组名称(UK)';

comment on column public.dp_portal_group.describe is '分组描述';

comment on column public.dp_portal_group.group_type is '分组类型(UK)';

comment on column public.dp_portal_group.group_order is '分组排序';

comment on column public.dp_portal_group.create_time is '创建时间';

comment on column public.dp_portal_group.update_time is '修改时间';

comment on column public.dp_portal_group.create_user_id is '创建者';

alter table public.dp_portal_group
    owner to postgres;

create table if not exists public.dp_portal_task
(
    task_id          varchar(100)                not null
    primary key,
    task_name        varchar(100)  default NULL::character varying,
    description      varchar(1000) default NULL::character varying,
    task_type        varchar(20)   default NULL::character varying,
    task_status      varchar(1)    default NULL::character varying,
    create_time      timestamp,
    update_time      timestamp,
    create_user_id   varchar(50)   default NULL::character varying,
    create_user_name varchar(100)  default NULL::character varying,
    update_user_id   varchar(50)   default NULL::character varying,
    update_user_name varchar(100)  default NULL::character varying,
    group_id         varchar(50)   default '0'::character varying,
    group_name       varchar(100)  default NULL::character varying,
    deleted          boolean       default false not null
    );

comment on table public.dp_portal_task is '任务信息表';

comment on column public.dp_portal_task.task_id is '任务编号(PK)';

comment on column public.dp_portal_task.task_name is '任务名称';

comment on column public.dp_portal_task.description is '任务描述';

comment on column public.dp_portal_task.task_type is '任务类型';

comment on column public.dp_portal_task.task_status is '任务状态';

comment on column public.dp_portal_task.create_time is '创建时间';

comment on column public.dp_portal_task.update_time is '修改时间';

comment on column public.dp_portal_task.create_user_id is '创建者';

comment on column public.dp_portal_task.create_user_name is '创建者名称';

comment on column public.dp_portal_task.update_user_id is '最后修改者';

comment on column public.dp_portal_task.update_user_name is '最后修改者名称';

comment on column public.dp_portal_task.group_id is '分组编号(FK)';

comment on column public.dp_portal_task.group_name is '分组名称';

comment on column public.dp_portal_task.deleted is '软删除';

alter table public.dp_portal_task
    owner to postgres;

create table if not exists public.dp_portal_task_file
(
    task_file_id varchar(50)  not null
    primary key,
    task_id      varchar(100) not null,
    task_file    text,
    task_version varchar(10) default NULL::character varying,
    create_time  timestamp
    );

comment on table public.dp_portal_task_file is '任务附件信息表';

comment on column public.dp_portal_task_file.task_file_id is '任务附件信息编号(PK)';

comment on column public.dp_portal_task_file.task_id is '任务编号(PK)';

comment on column public.dp_portal_task_file.task_file is '任务文件内容';

comment on column public.dp_portal_task_file.task_version is '任务版本号';

comment on column public.dp_portal_task_file.create_time is '创建时间';

alter table public.dp_portal_task_file
    owner to postgres;

create table if not exists public.api_sync_config
(
    id                    bigint       not null
    primary key,
    name                  varchar(100) not null,
    url                   varchar(500) not null,
    method                varchar(10)  not null,
    headers_json          text,
    params_json           text,
    cron_expression       varchar(50),
    response_parse_script text,
    field_mapping_json    text,
    target_table          varchar(100),
    enabled               boolean   default true,
    pre_api_id            bigint,
    create_time           timestamp default now(),
    update_time           timestamp default now()
    );

alter table public.api_sync_config
    owner to postgres;

create table if not exists public.data_source_ftp
(
    id          varchar                not null
    constraint ftp_config_pkey
    primary key,
    title       varchar(100)           not null,
    protocol    varchar(10)            not null,
    host        varchar(255)           not null,
    port        integer     default 21 not null,
    username    varchar(100)           not null,
    password    text,
    charset     varchar(50) default 'UTF-8'::character varying,
    private_key text,
    create_time timestamp   default now(),
    update_time timestamp   default now(),
    remark      varchar,
    sys_id      varchar
    );

comment on column public.data_source_ftp.remark is '备注';

comment on column public.data_source_ftp.sys_id is '系统id';

alter table public.data_source_ftp
    owner to postgres;

create table if not exists public.dp_de_task_exec_history
(
    id          bigint not null
    primary key,
    task_id     varchar(100) default NULL::character varying,
    user_id     varchar(50)  default NULL::character varying,
    user_name   varchar(100) default NULL::character varying,
    exec_env    varchar(10)  default NULL::character varying,
    status      varchar(10)  default NULL::character varying,
    start_time  timestamp,
    end_time    timestamp,
    exec_second integer
    );

comment on table public.dp_de_task_exec_history is '数据探索-项目执行历史表';

comment on column public.dp_de_task_exec_history.id is '流水号';

comment on column public.dp_de_task_exec_history.task_id is '项目编号';

comment on column public.dp_de_task_exec_history.user_id is '执行用户编号';

comment on column public.dp_de_task_exec_history.user_name is '执行用户名';

comment on column public.dp_de_task_exec_history.exec_env is '执行环境（TSPT、DDPT）';

comment on column public.dp_de_task_exec_history.status is '执行状态';

comment on column public.dp_de_task_exec_history.start_time is '开始时间';

comment on column public.dp_de_task_exec_history.end_time is '结束时间';

comment on column public.dp_de_task_exec_history.exec_second is '执行时间秒';

alter table public.dp_de_task_exec_history
    owner to postgres;

create table if not exists public.dp_de_savemodel_history
(
    id          bigint not null
    primary key,
    model_name  varchar(100),
    task_id     varchar(100),
    step_name   varchar(200),
    status      varchar(1),
    start_time  timestamp,
    end_time    timestamp,
    exec_second integer,
    user_id     varchar(50),
    user_name   varchar(100),
    model_id    varchar(100)
    );

comment on table public.dp_de_savemodel_history is '数据探索-保存模型历史表';

comment on column public.dp_de_savemodel_history.id is 'ID';

comment on column public.dp_de_savemodel_history.model_name is '模型名称';

comment on column public.dp_de_savemodel_history.task_id is '任务ID';

comment on column public.dp_de_savemodel_history.step_name is '步骤名称';

comment on column public.dp_de_savemodel_history.status is '状态（1运行中、2成功、3终止）';

comment on column public.dp_de_savemodel_history.start_time is '开始时间';

comment on column public.dp_de_savemodel_history.end_time is '结束时间';

comment on column public.dp_de_savemodel_history.exec_second is '执行时间秒';

comment on column public.dp_de_savemodel_history.user_id is '用户编号';

comment on column public.dp_de_savemodel_history.user_name is '用户名称';

alter table public.dp_de_savemodel_history
    owner to postgres;

create table if not exists public.dp_portal_plugin_info
(
    plugin_id          varchar(50) not null
    primary key,
    plugin_name        varchar(100),
    plugin_describe    varchar(200),
    plugin_category    varchar(20),
    plugin_type        varchar(20),
    plugin_image       varchar(100),
    category_order     integer,
    plugin_order       integer,
    second_plugin_type varchar(50),
    plugin_filter      varchar(2),
    plugin_output      varchar(2)
    );

comment on table public.dp_portal_plugin_info is '插件信息表';

comment on column public.dp_portal_plugin_info.plugin_id is '插件id';

comment on column public.dp_portal_plugin_info.plugin_name is '插件名称';

comment on column public.dp_portal_plugin_info.plugin_describe is '插件描述';

comment on column public.dp_portal_plugin_info.plugin_category is '插件分类';

comment on column public.dp_portal_plugin_info.plugin_type is '插件类型';

comment on column public.dp_portal_plugin_info.plugin_image is '插件图片';

comment on column public.dp_portal_plugin_info.category_order is '分类排序';

comment on column public.dp_portal_plugin_info.plugin_order is '插件排序';

comment on column public.dp_portal_plugin_info.second_plugin_type is '插件父Id';

comment on column public.dp_portal_plugin_info.plugin_filter is '是否判断是否分发过滤';

comment on column public.dp_portal_plugin_info.plugin_output is '是否主输出';

alter table public.dp_portal_plugin_info
    owner to postgres;

create table if not exists public.idp_task_reference
(
    id             varchar(100) not null
    primary key,
    title          varchar(255) not null,
    code           varchar(100) not null
    unique,
    type           varchar(50),
    status         varchar(50),
    reference_task varchar(255),
    creator        varchar(100),
    create_time    timestamp default now(),
    updater        varchar(100),
    update_time    timestamp default now(),
    group_id       varchar,
    group_name     varchar,
    nodes          text,
    edges          text,
    deleted        boolean   default false,
    description    varchar(255),
    flow_file      text
    );

comment on table public.idp_task_reference is '任务流表';

comment on column public.idp_task_reference.deleted is '软删除';

comment on column public.idp_task_reference.description is '任务流描述';

comment on column public.idp_task_reference.flow_file is '执行流程数据';

alter table public.idp_task_reference
    owner to postgres;

create table if not exists public.idp_flow_node
(
    id          varchar(64) not null
    primary key,
    flow_id     varchar(64) not null,
    node_id     varchar(64) not null,
    name        varchar(255),
    type        varchar(32),
    task_type   varchar(64),
    params      jsonb,
    upstream    jsonb,
    downstream  jsonb,
    creator     varchar(64),
    create_time timestamp,
    updater     varchar(64),
    update_time timestamp
    );

alter table public.idp_flow_node
    owner to postgres;

create table if not exists public.metadata_index
(
    id          varchar(50)  not null
    primary key,
    source_id   varchar(50)  not null,
    table_id    varchar(50)  not null,
    index_name  varchar(100) not null,
    index_type  varchar(50)  not null,
    column_name varchar(100) not null
    );

comment on column public.metadata_index.id is '主键';

comment on column public.metadata_index.source_id is '所属数据源';

comment on column public.metadata_index.table_id is '所属数据表';

comment on column public.metadata_index.index_name is '索引名称';

comment on column public.metadata_index.index_type is '索引类型';

comment on column public.metadata_index.column_name is '索引字段名称';

alter table public.metadata_index
    owner to postgres;

create table if not exists public.metadata_column
(
    id               varchar(50),
    source_id        varchar(255),
    table_id         varchar(255),
    column_name      varchar(255),
    column_comment   text,
    column_key       varchar(255),
    column_nullable  varchar(255),
    column_position  integer,
    data_type        varchar(255),
    data_length      varchar(255),
    data_precision   varchar(255),
    data_scale       varchar(255),
    data_default     varchar(255),
    field_type       integer,
    auto_increment   boolean,
    source_data_id   varchar,
    source_table_id  varchar,
    source_column_id varchar
    );

comment on column public.metadata_column.field_type is '字段类型';

comment on column public.metadata_column.auto_increment is '字段是否自增';

comment on column public.metadata_column.source_data_id is '来源数据源id';

comment on column public.metadata_column.source_table_id is '来源表id';

comment on column public.metadata_column.source_column_id is '来源字段id';

alter table public.metadata_column
    owner to postgres;

create table if not exists public.metadata_source
(
    id          varchar(50),
    status      varchar(50),
    create_by   varchar(50),
    create_time timestamp(6),
    create_dept varchar(50),
    update_by   varchar(50),
    update_time timestamp(6),
    remark      varchar(1000),
    db_type     varchar(50),
    source_name varchar(50),
    is_sync     varchar(1),
    db_schema   varchar(1000),
    db_desc     varchar(50)
    );

comment on column public.metadata_source.db_desc is '数据源描述';

alter table public.metadata_source
    owner to postgres;

create table if not exists public.metadata_table
(
    id            varchar(50) not null,
    source_id     varchar(255),
    table_name    varchar(255),
    table_comment varchar(255),
    table_type    varchar
    );

comment on column public.metadata_table.table_type is '表类型';

alter table public.metadata_table
    owner to postgres;

create unique index if not exists metadata_table_id_idx
    on public.metadata_table (id);

create table if not exists public.t_indicator_template_bak2
(
    id                 integer,
    attribute_name     varchar(255),
    attribute_title    varchar,
    field_length       varchar,
    is_null_allowed    varchar,
    creator_id         integer,
    creator_name       varchar,
    create_date        timestamp(6),
    modifitor_id       varchar,
    modifitor          varchar,
    modify_date        timestamp(6),
    indicator_no       integer,
    attribute_category varchar
    );

alter table public.t_indicator_template_bak2
    owner to postgres;

create table if not exists public.data_source_sys
(
    id          varchar(50)  not null
    primary key,
    sys_title   varchar(255) not null,
    sys_code    varchar(255) not null,
    admin_user  varchar(255),
    admin_phone varchar(255),
    admin_email varchar(255),
    description text,
    create_by   varchar(64),
    create_time timestamp,
    update_by   varchar(64),
    update_time timestamp
    );

comment on column public.data_source_sys.sys_title is '系统标题';

comment on column public.data_source_sys.sys_code is '系统代号';

comment on column public.data_source_sys.admin_user is '管理员';

comment on column public.data_source_sys.admin_phone is '管理员号码';

comment on column public.data_source_sys.admin_email is '管理员邮箱';

comment on column public.data_source_sys.description is '描述';

comment on column public.data_source_sys.create_by is '创建人';

comment on column public.data_source_sys.create_time is '创建时间';

comment on column public.data_source_sys.update_by is '修改人';

comment on column public.data_source_sys.update_time is '修改时间';

alter table public.data_source_sys
    owner to postgres;

create table if not exists public.dp_portal_task_online
(
    id               varchar(36)             not null
    primary key,
    task_id          varchar(36)             not null,
    task_name        varchar(255),
    description      text,
    task_type        varchar(50),
    task_status      varchar(20),
    create_time      timestamp default CURRENT_TIMESTAMP,
    update_time      timestamp default CURRENT_TIMESTAMP,
    create_user_id   varchar(36),
    create_user_name varchar(100),
    update_user_id   varchar(36),
    update_user_name varchar(100),
    group_id         varchar(36),
    group_name       varchar(100),
    task_file        text,
    deleted          boolean   default false not null
    );

comment on table public.dp_portal_task_online is '已上线任务表';

comment on column public.dp_portal_task_online.id is '主键ID';

comment on column public.dp_portal_task_online.task_id is '任务ID';

comment on column public.dp_portal_task_online.task_name is '任务名称';

comment on column public.dp_portal_task_online.description is '任务描述';

comment on column public.dp_portal_task_online.task_type is '任务类型';

comment on column public.dp_portal_task_online.task_status is '任务状态';

comment on column public.dp_portal_task_online.create_time is '创建时间';

comment on column public.dp_portal_task_online.update_time is '更新时间';

comment on column public.dp_portal_task_online.create_user_id is '创建用户ID';

comment on column public.dp_portal_task_online.create_user_name is '创建用户名称';

comment on column public.dp_portal_task_online.update_user_id is '更新用户ID';

comment on column public.dp_portal_task_online.update_user_name is '更新用户名称';

comment on column public.dp_portal_task_online.group_id is '分组ID';

comment on column public.dp_portal_task_online.group_name is '分组名称';

comment on column public.dp_portal_task_online.task_file is '可执行文件';

comment on column public.dp_portal_task_online.deleted is '软删除';

alter table public.dp_portal_task_online
    owner to postgres;

create index if not exists idx_dp_portal_task_online_task_id
    on public.dp_portal_task_online (task_id);

create index if not exists idx_dp_portal_task_online_task_name
    on public.dp_portal_task_online (task_name);

create index if not exists idx_dp_portal_task_online_task_status
    on public.dp_portal_task_online (task_status);

create index if not exists idx_dp_portal_task_online_group_id
    on public.dp_portal_task_online (group_id);

create table if not exists public.data_source_hdfs
(
    id           varchar not null,
    title        varchar,
    description  varchar,
    environment  varchar,
    username     varchar,
    url          varchar,
    auth_type    varchar,
    extra_params varchar,
    sys_id       varchar,
    create_time  timestamp default now(),
    update_time  timestamp default now()
    );

comment on table public.data_source_hdfs is 'HDFS数据源';

comment on column public.data_source_hdfs.id is 'ID';

comment on column public.data_source_hdfs.title is '标题';

comment on column public.data_source_hdfs.description is '描述';

comment on column public.data_source_hdfs.environment is '连接环境';

comment on column public.data_source_hdfs.username is '用户名';

comment on column public.data_source_hdfs.url is '连接地址';

comment on column public.data_source_hdfs.auth_type is '认证方式（simple、kerberos）';

comment on column public.data_source_hdfs.extra_params is '扩展参数 JSON';

comment on column public.data_source_hdfs.sys_id is '系统id';

comment on column public.data_source_hdfs.create_time is '创建时间';

comment on column public.data_source_hdfs.update_time is '更新时间';

alter table public.data_source_hdfs
    owner to postgres;

create table if not exists public.idp_task_flow_online
(
    id             varchar(100) not null
    primary key,
    task_flow_id   varchar(100),
    group_id       varchar(100),
    group_name     varchar(100),
    title          varchar(200),
    code           varchar(100),
    type           varchar(50),
    status         varchar(50),
    reference_task text,
    creator        varchar(100),
    create_time    timestamp default CURRENT_TIMESTAMP,
    updater        varchar(100),
    description    text,
    flow_file      text,
    update_time    timestamp default CURRENT_TIMESTAMP,
    deleted        boolean   default false
    );

comment on table public.idp_task_flow_online is '已上线任务流表';

comment on column public.idp_task_flow_online.id is '主键ID';

comment on column public.idp_task_flow_online.task_flow_id is '源任务流ID';

comment on column public.idp_task_flow_online.group_id is '分组ID';

comment on column public.idp_task_flow_online.group_name is '分组名称';

comment on column public.idp_task_flow_online.title is '任务流标题';

comment on column public.idp_task_flow_online.code is '任务流代号';

comment on column public.idp_task_flow_online.type is '任务流类型';

comment on column public.idp_task_flow_online.status is '任务流状态';

comment on column public.idp_task_flow_online.reference_task is '引用的任务列表';

comment on column public.idp_task_flow_online.creator is '创建人';

comment on column public.idp_task_flow_online.create_time is '创建时间';

comment on column public.idp_task_flow_online.updater is '更新人';

comment on column public.idp_task_flow_online.description is '任务流描述';

comment on column public.idp_task_flow_online.flow_file is '任务流配置数据';

comment on column public.idp_task_flow_online.update_time is '更新时间';

comment on column public.idp_task_flow_online.deleted is '是否删除（逻辑删除标记）';

alter table public.idp_task_flow_online
    owner to postgres;

create index if not exists idx_task_flow_online_task_flow_id
    on public.idp_task_flow_online (task_flow_id);

create index if not exists idx_task_flow_online_group_id
    on public.idp_task_flow_online (group_id);

create index if not exists idx_task_flow_online_title
    on public.idp_task_flow_online (title);

create index if not exists idx_task_flow_online_status
    on public.idp_task_flow_online (status);

create index if not exists idx_task_flow_online_creator
    on public.idp_task_flow_online (creator);

create table if not exists public.idp_task_flow_schedule
(
    id                  varchar(100) not null
    primary key,
    task_flow_id        varchar(100) not null,
    task_flow_name      varchar(200),
    group_id            varchar(100) default '0'::character varying,
    group_name          varchar(200) default NULL::character varying,
    schedule_name       varchar(200) not null,
    cron_expression     varchar(100) not null,
    enabled             boolean      default false,
    retry_on_failure    boolean      default false,
    max_retry_times     integer      default 0,
    retry_interval      integer      default 0,
    description         text,
    creator             varchar(100),
    create_time         timestamp    default CURRENT_TIMESTAMP,
    updater             varchar(100),
    update_time         timestamp    default CURRENT_TIMESTAMP,
    last_execution_time timestamp,
    next_execution_time timestamp,
    status              integer      default 0
    );

comment on table public.idp_task_flow_schedule is '任务流调度配置表';

comment on column public.idp_task_flow_schedule.id is '主键ID';

comment on column public.idp_task_flow_schedule.task_flow_id is '任务流ID';

comment on column public.idp_task_flow_schedule.task_flow_name is '任务流名称';

comment on column public.idp_task_flow_schedule.group_id is '分组ID';

comment on column public.idp_task_flow_schedule.group_name is '分组名称';

comment on column public.idp_task_flow_schedule.schedule_name is '调度名称';

comment on column public.idp_task_flow_schedule.cron_expression is 'Cron表达式';

comment on column public.idp_task_flow_schedule.enabled is '是否启用';

comment on column public.idp_task_flow_schedule.retry_on_failure is '失败是否重试';

comment on column public.idp_task_flow_schedule.max_retry_times is '最大重试次数';

comment on column public.idp_task_flow_schedule.retry_interval is '重试间隔(秒)';

comment on column public.idp_task_flow_schedule.description is '描述';

comment on column public.idp_task_flow_schedule.creator is '创建人';

comment on column public.idp_task_flow_schedule.create_time is '创建时间';

comment on column public.idp_task_flow_schedule.updater is '更新人';

comment on column public.idp_task_flow_schedule.update_time is '更新时间';

comment on column public.idp_task_flow_schedule.last_execution_time is '上次执行时间';

comment on column public.idp_task_flow_schedule.next_execution_time is '下次执行时间';

comment on column public.idp_task_flow_schedule.status is '状态：0-未执行，1-执行中，2-执行成功，3-执行失败';

alter table public.idp_task_flow_schedule
    owner to postgres;

create index if not exists idx_task_flow_schedule_task_flow_id
    on public.idp_task_flow_schedule (task_flow_id);

create index if not exists idx_task_flow_schedule_group_id
    on public.idp_task_flow_schedule (group_id);

create index if not exists idx_task_flow_schedule_enabled
    on public.idp_task_flow_schedule (enabled);

create index if not exists idx_task_flow_schedule_next_execution_time
    on public.idp_task_flow_schedule (next_execution_time);

create index if not exists idx_task_flow_schedule_status
    on public.idp_task_flow_schedule (status);

create table if not exists public.qrtz_job_details
(
    sched_name        varchar(120) not null,
    job_name          varchar(200) not null,
    job_group         varchar(200) not null,
    description       varchar(250),
    job_class_name    varchar(250) not null,
    is_durable        boolean      not null,
    is_nonconcurrent  boolean      not null,
    is_update_data    boolean      not null,
    requests_recovery boolean      not null,
    job_data          bytea,
    primary key (sched_name, job_name, job_group)
    );

comment on table public.qrtz_job_details is '任务详细信息表';

comment on column public.qrtz_job_details.sched_name is '调度名称';

comment on column public.qrtz_job_details.job_name is '任务名称';

comment on column public.qrtz_job_details.job_group is '任务组名';

comment on column public.qrtz_job_details.description is '相关介绍';

comment on column public.qrtz_job_details.job_class_name is '执行任务类名称';

comment on column public.qrtz_job_details.is_durable is '是否持久化';

comment on column public.qrtz_job_details.is_nonconcurrent is '是否并发';

comment on column public.qrtz_job_details.is_update_data is '是否更新数据';

comment on column public.qrtz_job_details.requests_recovery is '是否要求恢复数据';

comment on column public.qrtz_job_details.job_data is '存放持久化job对象';

alter table public.qrtz_job_details
    owner to postgres;

create index if not exists idx_qrtz_j_req_recovery
    on public.qrtz_job_details (sched_name, requests_recovery);

create index if not exists idx_qrtz_j_grp
    on public.qrtz_job_details (sched_name, job_group);

create table if not exists public.qrtz_triggers
(
    sched_name     varchar(120) not null,
    trigger_name   varchar(200) not null,
    trigger_group  varchar(200) not null,
    job_name       varchar(200) not null,
    job_group      varchar(200) not null,
    description    varchar(250),
    next_fire_time bigint,
    prev_fire_time bigint,
    priority       integer,
    trigger_state  varchar(16)  not null,
    trigger_type   varchar(8)   not null,
    start_time     bigint       not null,
    end_time       bigint,
    calendar_name  varchar(200),
    misfire_instr  smallint,
    job_data       bytea,
    primary key (sched_name, trigger_name, trigger_group),
    foreign key (sched_name, job_name, job_group) references public.qrtz_job_details
    );

comment on table public.qrtz_triggers is '触发器详细信息表';

comment on column public.qrtz_triggers.sched_name is '调度名称';

comment on column public.qrtz_triggers.trigger_name is '触发器名称';

comment on column public.qrtz_triggers.trigger_group is '触发器组名';

comment on column public.qrtz_triggers.job_name is '任务名称';

comment on column public.qrtz_triggers.job_group is '任务组名';

comment on column public.qrtz_triggers.description is '相关介绍';

comment on column public.qrtz_triggers.next_fire_time is '下一次触发时间';

comment on column public.qrtz_triggers.prev_fire_time is '上一次触发时间';

comment on column public.qrtz_triggers.priority is '优先级';

comment on column public.qrtz_triggers.trigger_state is '触发器状态';

comment on column public.qrtz_triggers.trigger_type is '触发器类型';

comment on column public.qrtz_triggers.start_time is '开始时间';

comment on column public.qrtz_triggers.end_time is '结束时间';

comment on column public.qrtz_triggers.calendar_name is '日历名称';

comment on column public.qrtz_triggers.misfire_instr is '补偿执行的策略';

comment on column public.qrtz_triggers.job_data is '存放持久化job对象';

alter table public.qrtz_triggers
    owner to postgres;

create index if not exists idx_qrtz_t_j
    on public.qrtz_triggers (sched_name, job_name, job_group);

create index if not exists idx_qrtz_t_jg
    on public.qrtz_triggers (sched_name, job_group);

create index if not exists idx_qrtz_t_c
    on public.qrtz_triggers (sched_name, calendar_name);

create index if not exists idx_qrtz_t_g
    on public.qrtz_triggers (sched_name, trigger_group);

create index if not exists idx_qrtz_t_state
    on public.qrtz_triggers (sched_name, trigger_state);

create index if not exists idx_qrtz_t_n_state
    on public.qrtz_triggers (sched_name, trigger_name, trigger_group, trigger_state);

create index if not exists idx_qrtz_t_n_g_state
    on public.qrtz_triggers (sched_name, trigger_group, trigger_state);

create index if not exists idx_qrtz_t_next_fire_time
    on public.qrtz_triggers (sched_name, next_fire_time);

create index if not exists idx_qrtz_t_nft_st
    on public.qrtz_triggers (sched_name, trigger_state, next_fire_time);

create index if not exists idx_qrtz_t_nft_misfire
    on public.qrtz_triggers (sched_name, misfire_instr, next_fire_time);

create index if not exists idx_qrtz_t_nft_st_misfire
    on public.qrtz_triggers (sched_name, misfire_instr, next_fire_time, trigger_state);

create index if not exists idx_qrtz_t_nft_st_misfire_grp
    on public.qrtz_triggers (sched_name, misfire_instr, next_fire_time, trigger_group, trigger_state);

create table if not exists public.qrtz_simple_triggers
(
    sched_name      varchar(120) not null,
    trigger_name    varchar(200) not null,
    trigger_group   varchar(200) not null,
    repeat_count    bigint       not null,
    repeat_interval bigint       not null,
    times_triggered bigint       not null,
    primary key (sched_name, trigger_name, trigger_group),
    foreign key (sched_name, trigger_name, trigger_group) references public.qrtz_triggers
    );

comment on table public.qrtz_simple_triggers is '简单触发器的信息表';

comment on column public.qrtz_simple_triggers.sched_name is '调度名称';

comment on column public.qrtz_simple_triggers.trigger_name is '触发器名称';

comment on column public.qrtz_simple_triggers.trigger_group is '触发器组名';

comment on column public.qrtz_simple_triggers.repeat_count is '重复的次数统计';

comment on column public.qrtz_simple_triggers.repeat_interval is '重复的间隔时间';

comment on column public.qrtz_simple_triggers.times_triggered is '已经触发的次数';

alter table public.qrtz_simple_triggers
    owner to postgres;

create table if not exists public.qrtz_cron_triggers
(
    sched_name      varchar(120) not null,
    trigger_name    varchar(200) not null,
    trigger_group   varchar(200) not null,
    cron_expression varchar(120) not null,
    time_zone_id    varchar(80),
    primary key (sched_name, trigger_name, trigger_group),
    foreign key (sched_name, trigger_name, trigger_group) references public.qrtz_triggers
    );

comment on table public.qrtz_cron_triggers is 'Cron类型的触发器表';

comment on column public.qrtz_cron_triggers.sched_name is '调度名称';

comment on column public.qrtz_cron_triggers.trigger_name is '触发器名称';

comment on column public.qrtz_cron_triggers.trigger_group is '触发器组名';

comment on column public.qrtz_cron_triggers.cron_expression is 'cron表达式';

comment on column public.qrtz_cron_triggers.time_zone_id is '时区';

alter table public.qrtz_cron_triggers
    owner to postgres;

create table if not exists public.qrtz_simprop_triggers
(
    sched_name    varchar(120) not null,
    trigger_name  varchar(200) not null,
    trigger_group varchar(200) not null,
    str_prop_1    varchar(512),
    str_prop_2    varchar(512),
    str_prop_3    varchar(512),
    int_prop_1    integer,
    int_prop_2    integer,
    long_prop_1   bigint,
    long_prop_2   bigint,
    dec_prop_1    numeric(13, 4),
    dec_prop_2    numeric(13, 4),
    bool_prop_1   boolean,
    bool_prop_2   boolean,
    primary key (sched_name, trigger_name, trigger_group),
    constraint qrtz_simprop_triggers_sched_name_trigger_name_trigger_grou_fkey
    foreign key (sched_name, trigger_name, trigger_group) references public.qrtz_triggers
    );

comment on table public.qrtz_simprop_triggers is '同步机制的行锁表';

comment on column public.qrtz_simprop_triggers.sched_name is '调度名称';

comment on column public.qrtz_simprop_triggers.trigger_name is '触发器名称';

comment on column public.qrtz_simprop_triggers.trigger_group is '触发器组名';

comment on column public.qrtz_simprop_triggers.str_prop_1 is 'String类型的trigger的第一个参数';

comment on column public.qrtz_simprop_triggers.str_prop_2 is 'String类型的trigger的第二个参数';

comment on column public.qrtz_simprop_triggers.str_prop_3 is 'String类型的trigger的第三个参数';

comment on column public.qrtz_simprop_triggers.int_prop_1 is 'int类型的trigger的第一个参数';

comment on column public.qrtz_simprop_triggers.int_prop_2 is 'int类型的trigger的第二个参数';

comment on column public.qrtz_simprop_triggers.long_prop_1 is 'long类型的trigger的第一个参数';

comment on column public.qrtz_simprop_triggers.long_prop_2 is 'long类型的trigger的第二个参数';

comment on column public.qrtz_simprop_triggers.dec_prop_1 is 'decimal类型的trigger的第一个参数';

comment on column public.qrtz_simprop_triggers.dec_prop_2 is 'decimal类型的trigger的第二个参数';

comment on column public.qrtz_simprop_triggers.bool_prop_1 is 'Boolean类型的trigger的第一个参数';

comment on column public.qrtz_simprop_triggers.bool_prop_2 is 'Boolean类型的trigger的第二个参数';

alter table public.qrtz_simprop_triggers
    owner to postgres;

create table if not exists public.qrtz_blob_triggers
(
    sched_name    varchar(120) not null,
    trigger_name  varchar(200) not null,
    trigger_group varchar(200) not null,
    blob_data     bytea,
    primary key (sched_name, trigger_name, trigger_group),
    foreign key (sched_name, trigger_name, trigger_group) references public.qrtz_triggers
    );

comment on table public.qrtz_blob_triggers is 'Blob类型的触发器表';

comment on column public.qrtz_blob_triggers.sched_name is '调度名称';

comment on column public.qrtz_blob_triggers.trigger_name is '触发器名称';

comment on column public.qrtz_blob_triggers.trigger_group is '触发器组名';

comment on column public.qrtz_blob_triggers.blob_data is '存放持久化Trigger对象';

alter table public.qrtz_blob_triggers
    owner to postgres;

create table if not exists public.qrtz_calendars
(
    sched_name    varchar(120) not null,
    calendar_name varchar(200) not null,
    calendar      bytea        not null,
    primary key (sched_name, calendar_name)
    );

comment on table public.qrtz_calendars is '日历信息表';

comment on column public.qrtz_calendars.sched_name is '调度名称';

comment on column public.qrtz_calendars.calendar_name is '日历名称';

comment on column public.qrtz_calendars.calendar is '存放持久化calendar对象';

alter table public.qrtz_calendars
    owner to postgres;

create table if not exists public.qrtz_paused_trigger_grps
(
    sched_name    varchar(120) not null,
    trigger_group varchar(200) not null,
    primary key (sched_name, trigger_group)
    );

comment on table public.qrtz_paused_trigger_grps is '暂停的触发器表';

comment on column public.qrtz_paused_trigger_grps.sched_name is '调度名称';

comment on column public.qrtz_paused_trigger_grps.trigger_group is '触发器组名';

alter table public.qrtz_paused_trigger_grps
    owner to postgres;

create table if not exists public.qrtz_fired_triggers
(
    sched_name        varchar(120) not null,
    entry_id          varchar(95)  not null,
    trigger_name      varchar(200) not null,
    trigger_group     varchar(200) not null,
    instance_name     varchar(200) not null,
    fired_time        bigint       not null,
    sched_time        bigint       not null,
    priority          integer      not null,
    state             varchar(16)  not null,
    job_name          varchar(200),
    job_group         varchar(200),
    is_nonconcurrent  boolean,
    requests_recovery boolean,
    primary key (sched_name, entry_id)
    );

comment on table public.qrtz_fired_triggers is '已触发的触发器表';

comment on column public.qrtz_fired_triggers.sched_name is '调度名称';

comment on column public.qrtz_fired_triggers.entry_id is '调度器实例id';

comment on column public.qrtz_fired_triggers.trigger_name is '触发器名称';

comment on column public.qrtz_fired_triggers.trigger_group is '触发器组名';

comment on column public.qrtz_fired_triggers.instance_name is '调度器实例名';

comment on column public.qrtz_fired_triggers.fired_time is '触发时间';

comment on column public.qrtz_fired_triggers.sched_time is '定时器制定的时间';

comment on column public.qrtz_fired_triggers.priority is '优先级';

comment on column public.qrtz_fired_triggers.state is '状态';

comment on column public.qrtz_fired_triggers.job_name is '任务名称';

comment on column public.qrtz_fired_triggers.job_group is '任务组名';

comment on column public.qrtz_fired_triggers.is_nonconcurrent is '是否并发';

comment on column public.qrtz_fired_triggers.requests_recovery is '是否要求恢复数据';

alter table public.qrtz_fired_triggers
    owner to postgres;

create index if not exists idx_qrtz_ft_trig_inst_name
    on public.qrtz_fired_triggers (sched_name, instance_name);

create index if not exists idx_qrtz_ft_inst_job_req_rcvry
    on public.qrtz_fired_triggers (sched_name, instance_name, requests_recovery);

create index if not exists idx_qrtz_ft_j_g
    on public.qrtz_fired_triggers (sched_name, job_name, job_group);

create index if not exists idx_qrtz_ft_jg
    on public.qrtz_fired_triggers (sched_name, job_group);

create index if not exists idx_qrtz_ft_t_g
    on public.qrtz_fired_triggers (sched_name, trigger_name, trigger_group);

create index if not exists idx_qrtz_ft_tg
    on public.qrtz_fired_triggers (sched_name, trigger_group);

create table if not exists public.qrtz_scheduler_state
(
    sched_name        varchar(120) not null,
    instance_name     varchar(200) not null,
    last_checkin_time bigint       not null,
    checkin_interval  bigint       not null,
    primary key (sched_name, instance_name)
    );

comment on table public.qrtz_scheduler_state is '调度器状态表';

comment on column public.qrtz_scheduler_state.sched_name is '调度名称';

comment on column public.qrtz_scheduler_state.instance_name is '实例名称';

comment on column public.qrtz_scheduler_state.last_checkin_time is '上次检查时间';

comment on column public.qrtz_scheduler_state.checkin_interval is '检查间隔时间';

alter table public.qrtz_scheduler_state
    owner to postgres;

create table if not exists public.qrtz_locks
(
    sched_name varchar(120) not null,
    lock_name  varchar(40)  not null,
    primary key (sched_name, lock_name)
    );

comment on table public.qrtz_locks is '存储的悲观锁信息表';

comment on column public.qrtz_locks.sched_name is '调度名称';

comment on column public.qrtz_locks.lock_name is '悲观锁名称';

alter table public.qrtz_locks
    owner to postgres;

create table if not exists public.t_col_attr_union
(
    id           bigint not null
    constraint pk_t_col_attr_union_id
    primary key,
    column_type  integer,
    to_dname     varchar,
    data_cycle   varchar,
    agg_func     varchar,
    title        varchar,
    descrip      varchar,
    t_group      varchar,
    expression   varchar,
    creator_name varchar,
    creator_id   varchar,
    create_date  timestamp,
    modifier     varchar,
    modifier_id  varchar,
    modify_date  timestamp,
    column_id    integer
);

comment on table public.t_col_attr_union is '字段指标属性合并表 字段指标属性合并表';

comment on column public.t_col_attr_union.id is '编号';

comment on column public.t_col_attr_union.column_type is '字段类型 1=指标列 2=数据期列 3=维度列';

comment on column public.t_col_attr_union.to_dname is '对应的维表 对应的维表';

comment on column public.t_col_attr_union.data_cycle is '数据期类型';

comment on column public.t_col_attr_union.agg_func is '聚合函数';

comment on column public.t_col_attr_union.title is '标题';

comment on column public.t_col_attr_union.descrip is '描述';

comment on column public.t_col_attr_union.t_group is '分组';

comment on column public.t_col_attr_union.expression is '公式';

comment on column public.t_col_attr_union.creator_name is '创建人';

comment on column public.t_col_attr_union.creator_id is '创建人编号';

comment on column public.t_col_attr_union.create_date is '创建时间 创建时间';

comment on column public.t_col_attr_union.modifier is '修改人';

comment on column public.t_col_attr_union.modifier_id is '修改人编号';

comment on column public.t_col_attr_union.modify_date is '修改日期';

comment on column public.t_col_attr_union.column_id is '列编号';

alter table public.t_col_attr_union
    owner to postgres;

create table if not exists public.t_dim_attribute_setting
(
    id                  bigint  not null
    constraint pk_t_dim_aing_id6830
    primary key
    constraint t_dim_attribute_setting_id_unique
    unique,
    attribute_name      varchar,
    attribute_column    varchar,
    text_column         varchar,
    creator_id          varchar not null,
    creator_name        varchar,
    create_date         timestamp default now(),
    modifier            varchar,
    modifier_id         varchar,
    modify_date         varchar,
    dim_no              bigint,
    attribute_column_id varchar(255),
    text_column_id      varchar(255)
    );

comment on table public.t_dim_attribute_setting is '维度属性设置表 维度属性设置表';

comment on column public.t_dim_attribute_setting.id is '编号';

comment on column public.t_dim_attribute_setting.attribute_name is '属性名称';

comment on column public.t_dim_attribute_setting.attribute_column is '属性列名';

comment on column public.t_dim_attribute_setting.text_column is '文字列名';

comment on column public.t_dim_attribute_setting.creator_id is '创建人编号 当前用户ID';

comment on column public.t_dim_attribute_setting.creator_name is '创建人姓名';

comment on column public.t_dim_attribute_setting.create_date is '创建日期 默认为当前时间';

comment on column public.t_dim_attribute_setting.modifier is '修改人';

comment on column public.t_dim_attribute_setting.modifier_id is '修改人编号';

comment on column public.t_dim_attribute_setting.modify_date is '修改日期 修改日期';

comment on column public.t_dim_attribute_setting.dim_no is '维表编号';

comment on column public.t_dim_attribute_setting.attribute_column_id is '属性列字段id';

comment on column public.t_dim_attribute_setting.text_column_id is '文字列字段id';

alter table public.t_dim_attribute_setting
    owner to postgres;

create index if not exists idx_t_dim_aing_creator_id9d70
    on public.t_dim_attribute_setting (creator_id);

create table if not exists public.t_dim_base_info
(
    id                   bigint  not null
    constraint pk_t_dim_base_info_id
    primary key
    constraint t_dim_base_info_id_unique
    unique,
    dim_name             varchar,
    dim_type             integer,
    database_pool        varchar,
    table_name           varchar,
    cache_to             varchar,
    database_table_name  varchar,
    enable_time          integer,
    edit_context         varchar,
    creator_id           varchar not null,
    create_date          timestamp default now(),
    creator_name         varchar,
    modifier             varchar,
    modifier_id          varchar,
    modify_date          timestamp,
    dim_group_id         bigint,
    tree_type            varchar,
    tree_type_split      varchar,
    tree_value           varchar,
    id_field             varchar,
    text_field           varchar,
    mark                 varchar,
    table_id             varchar(50),
    source_id            varchar(50),
    split_info           varchar(255),
    split_name           varchar(255),
    drill_path           varchar(255),
    id_field_column_id   integer,
    text_field_column_id integer
    );

comment on table public.t_dim_base_info is '维度基础信息表 维度基础信息表';

comment on column public.t_dim_base_info.id is '编号';

comment on column public.t_dim_base_info.dim_name is '名称';

comment on column public.t_dim_base_info.dim_type is '类型 维度类型';

comment on column public.t_dim_base_info.database_pool is '数据库连接池';

comment on column public.t_dim_base_info.table_name is '表名';

comment on column public.t_dim_base_info.cache_to is '缓存到内存 缓存到内存';

comment on column public.t_dim_base_info.database_table_name is '数据库表名称';

comment on column public.t_dim_base_info.enable_time is '有效时间';

comment on column public.t_dim_base_info.edit_context is '编辑内容 编辑内容';

comment on column public.t_dim_base_info.creator_id is '创建人编号 当前用户ID';

comment on column public.t_dim_base_info.create_date is '创建日期 默认为当前时间';

comment on column public.t_dim_base_info.creator_name is '创建人姓名';

comment on column public.t_dim_base_info.modifier is '修改人';

comment on column public.t_dim_base_info.modifier_id is '修改人编号';

comment on column public.t_dim_base_info.modify_date is '修改日期';

comment on column public.t_dim_base_info.dim_group_id is '维度分组id';

comment on column public.t_dim_base_info.tree_type is '树结构类型（0无层次结构 1分段结构 2钻取结构）';

comment on column public.t_dim_base_info.tree_type_split is '树类型分割';

comment on column public.t_dim_base_info.tree_value is '树类型值';

comment on column public.t_dim_base_info.id_field is 'ID字段';

comment on column public.t_dim_base_info.text_field is '文字字段';

comment on column public.t_dim_base_info.mark is '描述';

comment on column public.t_dim_base_info.source_id is '连接池id';

comment on column public.t_dim_base_info.split_info is '分段信息';

comment on column public.t_dim_base_info.split_name is '分段名';

comment on column public.t_dim_base_info.drill_path is '钻取路径';

alter table public.t_dim_base_info
    owner to postgres;

create index if not exists idx_t_dim_bnfo_creator_id98c8
    on public.t_dim_base_info (creator_id);

create table if not exists public.t_dim_column_setting
(
    id             bigint  not null
    constraint pk_t_dim_cing_idca68
    primary key,
    column_name    varchar,
    column_comment varchar not null,
    length         integer,
    decimal_digits integer,
    column_type    varchar,
    enable_pk      boolean,
    enable_text    varchar,
    creator_id     varchar,
    creator_name   varchar,
    create_date    timestamp default now(),
    modifier       varchar,
    modifier_id    varchar,
    modify_date    timestamp,
    dim_no         bigint
    );

comment on table public.t_dim_column_setting is '维度字段设置表 维度字段设置表';

comment on column public.t_dim_column_setting.id is '编号';

comment on column public.t_dim_column_setting.column_name is '字段名称';

comment on column public.t_dim_column_setting.column_comment is '字段描述';

comment on column public.t_dim_column_setting.length is '字段长度';

comment on column public.t_dim_column_setting.decimal_digits is '小数位数';

comment on column public.t_dim_column_setting.column_type is '字段类型';

comment on column public.t_dim_column_setting.enable_pk is '是否ID主键字段';

comment on column public.t_dim_column_setting.enable_text is '是否文字类型字段';

comment on column public.t_dim_column_setting.creator_id is '创建人编号 当前用户ID';

comment on column public.t_dim_column_setting.creator_name is '创建人姓名';

comment on column public.t_dim_column_setting.create_date is '创建日期 默认为当前时间';

comment on column public.t_dim_column_setting.modifier is '修改人';

comment on column public.t_dim_column_setting.modifier_id is '修改人编号';

comment on column public.t_dim_column_setting.modify_date is '修改日期';

comment on column public.t_dim_column_setting.dim_no is '维度表编号';

alter table public.t_dim_column_setting
    owner to postgres;

create table if not exists public.t_dim_drill_path
(
    id           bigint  not null
    constraint pk_t_dim_drill_path_id
    primary key
    constraint t_dim_drill_path_id_unique
    unique,
    t_path       varchar,
    creator_id   varchar not null,
    creator_name varchar,
    create_date  timestamp default now(),
    modifier     varchar,
    modifier_id  varchar,
    modify_date  timestamp,
    dim_no       bigint
    );

comment on table public.t_dim_drill_path is '维度钻取路径表 维度钻取路径表';

comment on column public.t_dim_drill_path.id is '路径编号';

comment on column public.t_dim_drill_path.t_path is '路径';

comment on column public.t_dim_drill_path.creator_id is '创建人编号 当前用户ID';

comment on column public.t_dim_drill_path.creator_name is '创建人姓名';

comment on column public.t_dim_drill_path.create_date is '创建日期 默认为当前时间';

comment on column public.t_dim_drill_path.modifier is '修改人';

comment on column public.t_dim_drill_path.modifier_id is '修改人编号';

comment on column public.t_dim_drill_path.modify_date is '修改日期';

comment on column public.t_dim_drill_path.dim_no is '维度编号';

alter table public.t_dim_drill_path
    owner to postgres;

create table if not exists public.t_dim_tree_setting
(
    id                bigint  not null
    constraint pk_t_dim_tree_setting_id
    primary key,
    enable_level      varchar,
    split_column_info varchar,
    drill_path        varchar,
    split_column_name varchar,
    creator_id        varchar not null,
    creator_name      varchar,
    create_date       timestamp default now(),
    modifier          varchar,
    modifier_id       varchar,
    modify_date       timestamp,
    dim_no            bigint
    );

comment on table public.t_dim_tree_setting is '维度树形设置表 维度树形设置表';

comment on column public.t_dim_tree_setting.id is '编号';

comment on column public.t_dim_tree_setting.enable_level is '是否有无层次结构';

comment on column public.t_dim_tree_setting.split_column_info is '分段信息';

comment on column public.t_dim_tree_setting.drill_path is '钻取路径';

comment on column public.t_dim_tree_setting.split_column_name is '分段名';

comment on column public.t_dim_tree_setting.creator_id is '创建人编号 当前用户ID';

comment on column public.t_dim_tree_setting.creator_name is '创建人姓名';

comment on column public.t_dim_tree_setting.create_date is '创建日期 默认为当前时间';

comment on column public.t_dim_tree_setting.modifier is '修改人';

comment on column public.t_dim_tree_setting.modifier_id is '修改人编号';

comment on column public.t_dim_tree_setting.modify_date is '修改日期';

comment on column public.t_dim_tree_setting.dim_no is '维度编号';

alter table public.t_dim_tree_setting
    owner to postgres;

create table if not exists public.t_indicator_connection
(
    id                     bigint not null
    primary key,
    connection_id          varchar,
    source_id              varchar,
    left_table_id          varchar,
    right_table_id         varchar,
    connection_type        varchar,
    left_table_coordinate  varchar,
    right_table_coordinate varchar,
    left_table_columns     varchar,
    right_table_columns    varchar,
    connection_columns     varchar,
    creator_name           varchar,
    creator_id             varchar,
    create_date            timestamp,
    modifier               varchar,
    modifier_id            varchar,
    modify_date            timestamp
);

comment on table public.t_indicator_connection is '指标关联-关联关系表';

comment on column public.t_indicator_connection.id is '主键';

comment on column public.t_indicator_connection.connection_id is '关联关系ID';

comment on column public.t_indicator_connection.source_id is '数据源ID';

comment on column public.t_indicator_connection.left_table_id is '左表ID';

comment on column public.t_indicator_connection.right_table_id is '右表ID';

comment on column public.t_indicator_connection.connection_type is '关联类型';

comment on column public.t_indicator_connection.left_table_coordinate is '左表坐标';

comment on column public.t_indicator_connection.right_table_coordinate is '右表坐标';

comment on column public.t_indicator_connection.left_table_columns is '左表字段';

comment on column public.t_indicator_connection.right_table_columns is '右表字段';

comment on column public.t_indicator_connection.connection_columns is '关联字段';

comment on column public.t_indicator_connection.creator_name is '创建人';

comment on column public.t_indicator_connection.creator_id is '创建人ID';

comment on column public.t_indicator_connection.create_date is '创建时间';

comment on column public.t_indicator_connection.modifier is '修改人';

comment on column public.t_indicator_connection.modifier_id is '修改人ID';

comment on column public.t_indicator_connection.modify_date is '修改时间';

alter table public.t_indicator_connection
    owner to postgres;

create index if not exists t_indicator_connection__connection_id_index
    on public.t_indicator_connection (connection_id);

comment on index public.t_indicator_connection__connection_id_index is '关联信息id索引';

create table if not exists public.t_indicator_info
(
    id                   bigint not null
    constraint pk_t_indicator_info_id
    primary key,
    title                varchar,
    ind_scope_id         varchar,
    source_id            varchar,
    ind_scope_title      varchar,
    mark                 varchar,
    ind_type             integer,
    ind_group            varchar,
    data_level           varchar,
    express              varchar,
    ind_length           integer,
    decimal_digits       integer,
    business_caliber     varchar,
    creator_id           varchar,
    creator_name         varchar,
    modifier             varchar,
    modify_date          timestamp,
    modifier_id          varchar,
    num_indic_func       varchar,
    character_indic_func varchar,
    indicator_lib_no     bigint,
    time_limit           varchar,
    business_limit       varchar,
    agge_method          varchar,
    analy_dim            varchar,
    tt_exp               varchar,
    field_name           varchar,
    alias                varchar,
    connection_id        varchar,
    field_type           varchar,
    inner_domain_table   varchar,
    data_date_type       varchar,
    table_id             varchar,
    col_nullable         varchar,
    related_indicator_id bigint,
    create_date          timestamp,
    batch_no             varchar
);

comment on table public.t_indicator_info is '指标库详情信息表 指标库详情信息表';

comment on column public.t_indicator_info.id is '编号';

comment on column public.t_indicator_info.title is '标题';

comment on column public.t_indicator_info.ind_scope_id is '指标域id';

comment on column public.t_indicator_info.source_id is '资源id';

comment on column public.t_indicator_info.ind_scope_title is '指标域标题';

comment on column public.t_indicator_info.mark is '描述';

comment on column public.t_indicator_info.ind_type is '指标类型 1=派生指标2=原子指标3=复合指标';

comment on column public.t_indicator_info.ind_group is '分组 分组';

comment on column public.t_indicator_info.data_level is '数据类型';

comment on column public.t_indicator_info.express is '公式';

comment on column public.t_indicator_info.ind_length is '长度';

comment on column public.t_indicator_info.decimal_digits is '小数位数';

comment on column public.t_indicator_info.business_caliber is '业务口径';

comment on column public.t_indicator_info.creator_id is '创建人编号';

comment on column public.t_indicator_info.creator_name is '创建人姓名';

comment on column public.t_indicator_info.modifier is '修改人姓名';

comment on column public.t_indicator_info.modify_date is '修改日期';

comment on column public.t_indicator_info.modifier_id is '修改人编号';

comment on column public.t_indicator_info.num_indic_func is '数值型指标聚合函数';

comment on column public.t_indicator_info.character_indic_func is '字符型指标聚合函数 字符型指标聚合函数';

comment on column public.t_indicator_info.indicator_lib_no is '指标库id';

comment on column public.t_indicator_info.time_limit is '时间限定';

comment on column public.t_indicator_info.business_limit is '业务限定';

comment on column public.t_indicator_info.agge_method is '聚合方式';

comment on column public.t_indicator_info.analy_dim is '分析维度';

comment on column public.t_indicator_info.tt_exp is '表达式';

comment on column public.t_indicator_info.field_name is '字段名称';

comment on column public.t_indicator_info.alias is '别名';

comment on column public.t_indicator_info.connection_id is '关联关系id';

comment on column public.t_indicator_info.field_type is '字段类型  指标列  数据期列  维度列';

comment on column public.t_indicator_info.inner_domain_table is '维表';

comment on column public.t_indicator_info.data_date_type is '数据期类型';

comment on column public.t_indicator_info.table_id is '表id';

comment on column public.t_indicator_info.col_nullable is '是否为空';

comment on column public.t_indicator_info.related_indicator_id is '关联指标id';

comment on column public.t_indicator_info.create_date is '创建时间';

comment on column public.t_indicator_info.batch_no is '批次号';

alter table public.t_indicator_info
    owner to postgres;

create index if not exists t_indicator_info__connection_id_index
    on public.t_indicator_info (connection_id);

comment on index public.t_indicator_info__connection_id_index is '关联信息id索引';

create table if not exists public.t_indicator_manage
(
    id                      bigint  not null
    constraint pk_t_indicator_manage_id
    primary key,
    pid                     bigint,
    title_name              varchar,
    con_pool                varchar,
    creator_id              varchar not null,
    creator_name            varchar,
    create_date             timestamp default now(),
    data_level              integer,
    order_no                integer,
    modifier                varchar,
    modify_date             timestamp,
    mark                    varchar,
    num_indic_func          varchar,
    character_indic_func    varchar,
    approval_process_enable boolean,
    enable_version          varchar,
    version_manage_enable   boolean,
    source_id               varchar,
    table_data_ids          varchar,
    modifier_id             varchar
    );

comment on table public.t_indicator_manage is '指标管理列表 指标管理列表';

comment on column public.t_indicator_manage.id is '编号';

comment on column public.t_indicator_manage.pid is '父编号';

comment on column public.t_indicator_manage.title_name is '标题名称';

comment on column public.t_indicator_manage.con_pool is '存储连接池';

comment on column public.t_indicator_manage.creator_id is '创建人编号 当前用户ID';

comment on column public.t_indicator_manage.creator_name is '创建人姓名';

comment on column public.t_indicator_manage.create_date is '创建日期 默认为当前时间';

comment on column public.t_indicator_manage.data_level is '数据级别 选项：0=维度 1=维度分组 2=指标域 3=指标库 4=指标分组5=指标6=未知';

comment on column public.t_indicator_manage.order_no is '排序号';

comment on column public.t_indicator_manage.modifier is '修改人姓名';

comment on column public.t_indicator_manage.modify_date is '修改日期';

comment on column public.t_indicator_manage.mark is '描述';

comment on column public.t_indicator_manage.num_indic_func is '数值型指标聚合函数';

comment on column public.t_indicator_manage.character_indic_func is '字符型指标聚合函数';

comment on column public.t_indicator_manage.approval_process_enable is '启动审批流程';

comment on column public.t_indicator_manage.enable_version is '启用版本描述';

comment on column public.t_indicator_manage.version_manage_enable is '版本管理';

comment on column public.t_indicator_manage.source_id is '资源ID';

comment on column public.t_indicator_manage.table_data_ids is '模板id集合';

comment on column public.t_indicator_manage.modifier_id is '修改人id';

alter table public.t_indicator_manage
    owner to postgres;

create trigger set_modify_date
    before insert or update
                         on public.t_indicator_manage
                         for each row
                         execute procedure public.update_modify_date();

create table if not exists public.t_indicator_manage_table
(
    id             varchar not null
    constraint t_indicator_manage_table_pk
    primary key,
    method_name    varchar,
    method_title   varchar,
    method_type    varchar,
    relation_table varchar,
    length         integer,
    allownull      integer,
    pid            bigint,
    create_date    timestamp,
    modify_date    timestamp,
    creator_id     varchar,
    creator_name   varchar,
    modifier       varchar,
    modifier_id    varchar
);

comment on column public.t_indicator_manage_table.pid is '指标库id';

alter table public.t_indicator_manage_table
    owner to postgres;

create table if not exists public.t_indicator_template
(
    id                 bigint  not null
    constraint pk_t_indicate_id4b1f
    primary key
    constraint t_indicator_template_id_unique
    unique,
    attribute_name     varchar,
    attribute_title    varchar,
    field_length       varchar,
    is_null_allowed    varchar,
    creator_id         varchar not null,
    creator_name       varchar,
    create_date        timestamp default now(),
    modifier_id        varchar,
    modifier           varchar,
    modify_date        timestamp,
    indicator_no       bigint,
    attribute_category varchar
    );

comment on table public.t_indicator_template is '指标模板信息表 指标模板信息表';

comment on column public.t_indicator_template.id is '编号';

comment on column public.t_indicator_template.attribute_name is '属性名称';

comment on column public.t_indicator_template.attribute_title is '属性标题';

comment on column public.t_indicator_template.field_length is '字段长度';

comment on column public.t_indicator_template.is_null_allowed is '允许为空';

comment on column public.t_indicator_template.creator_id is '创建人编号 当前用户ID';

comment on column public.t_indicator_template.creator_name is '创建人姓名';

comment on column public.t_indicator_template.create_date is '创建日期 默认为当前时间';

comment on column public.t_indicator_template.modifier_id is '修改人编号';

comment on column public.t_indicator_template.modifier is '修改人';

comment on column public.t_indicator_template.modify_date is '修改日期';

comment on column public.t_indicator_template.indicator_no is '指标库编号';

comment on column public.t_indicator_template.attribute_category is '属性类别';

alter table public.t_indicator_template
    owner to postgres;

create trigger set_modify_date
    before insert or update
                         on public.t_indicator_template
                         for each row
                         execute procedure public.update_modify_date();

create table if not exists public.t_title_info_pg
(
    id              bigint,
    file_no         varchar(50),
    original_title  varchar(1000),
    modify_title    varchar(1000),
    is_del          boolean,
    c_crt_usr       varchar(255),
    dt_crt_tm       timestamp,
    c_upd_usr       varchar(255),
    dt_upd_tm       timestamp,
    record_id       bigint,
    title_page      integer,
    material_type   varchar(100),
    extract_conf_id bigint,
    end_page        integer,
    need_confirm    boolean
    );

alter table public.t_title_info_pg
    owner to postgres;

create table if not exists public.dp_task_execution_log
(
    id            varchar(50)  not null
    primary key,
    task_id       varchar(100) not null,
    task_name     varchar(200),
    executor_id   varchar(100),
    user_id       varchar(50),
    user_name     varchar(100),
    start_time    timestamp,
    end_time      timestamp,
    status        varchar(20),
    exec_env      varchar(50),
    log_level     varchar(20),
    log_content   text,
    error_message text,
    exec_second   integer,
    create_time   timestamp default CURRENT_TIMESTAMP,
    update_time   timestamp default CURRENT_TIMESTAMP
    );

comment on table public.dp_task_execution_log is '任务执行日志表';

comment on column public.dp_task_execution_log.id is '主键ID';

comment on column public.dp_task_execution_log.task_id is '任务ID';

comment on column public.dp_task_execution_log.task_name is '任务名称';

comment on column public.dp_task_execution_log.executor_id is '执行器ID';

comment on column public.dp_task_execution_log.user_id is '执行用户ID';

comment on column public.dp_task_execution_log.user_name is '执行用户名';

comment on column public.dp_task_execution_log.start_time is '开始时间';

comment on column public.dp_task_execution_log.end_time is '结束时间';

comment on column public.dp_task_execution_log.status is '执行状态';

comment on column public.dp_task_execution_log.exec_env is '执行环境';

comment on column public.dp_task_execution_log.log_level is '日志级别';

comment on column public.dp_task_execution_log.log_content is '执行日志内容';

comment on column public.dp_task_execution_log.error_message is '错误信息';

comment on column public.dp_task_execution_log.exec_second is '执行时间(秒)';

comment on column public.dp_task_execution_log.create_time is '创建时间';

comment on column public.dp_task_execution_log.update_time is '更新时间';

alter table public.dp_task_execution_log
    owner to postgres;

create index if not exists idx_dp_task_execution_log_task_id
    on public.dp_task_execution_log (task_id);

create index if not exists idx_dp_task_execution_log_executor_id
    on public.dp_task_execution_log (executor_id);

create index if not exists idx_dp_task_execution_log_status
    on public.dp_task_execution_log (status);

create index if not exists idx_dp_task_execution_log_start_time
    on public.dp_task_execution_log (start_time);

create table if not exists public.asdqwdqwd
(
    sad smallint
);

comment on column public.asdqwdqwd.sad is 'asdasd';

alter table public.asdqwdqwd
    owner to postgres;

create table if not exists public.data_set
(
    id                  varchar(50) not null
    primary key,
    parent_id           varchar(50),
    name                varchar(255),
    meta_source_id      varchar(255),
    data_source_id      varchar(255),
    meta_table_id       varchar(255),
    meta_table_name     varchar(255),
    meta_table_describe varchar(255),
    data_hierarchy      varchar(255),
    old_data_hierarchy  varchar(255),
    project_id          varchar(50),
    data_set_type       varchar(255),
    x                   varchar(100),
    y                   varchar(100),
    type                varchar(255),
    create_by           varchar(64),
    create_time         timestamp,
    update_by           varchar(64),
    update_time         timestamp
    );

comment on table public.data_set is '数仓集市数据集表';

comment on column public.data_set.id is '数据集ID';

comment on column public.data_set.parent_id is '父文件夹ID';

comment on column public.data_set.name is '数据集名称';

comment on column public.data_set.meta_source_id is '数仓数据源id，与下面的tableId不一定关联';

comment on column public.data_set.data_source_id is '元数据源Id';

comment on column public.data_set.meta_table_id is '元数据表ID';

comment on column public.data_set.meta_table_name is '元数据表名';

comment on column public.data_set.meta_table_describe is '元数据描述';

comment on column public.data_set.data_hierarchy is '数据层';

comment on column public.data_set.old_data_hierarchy is '旧数据层';

comment on column public.data_set.project_id is '项目ID';

comment on column public.data_set.data_set_type is '数据集类型';

comment on column public.data_set.x is 'x坐标';

comment on column public.data_set.y is 'y坐标';

comment on column public.data_set.type is '类型：Folder：数据集 RealTable：数据表';

comment on column public.data_set.create_by is '创建人';

comment on column public.data_set.create_time is '创建时间';

comment on column public.data_set.update_by is '修改人';

comment on column public.data_set.update_time is '修改时间';

alter table public.data_set
    owner to postgres;

create table if not exists public.table_relation
(
    id                 varchar(50)  not null
    primary key,
    data_set_id        varchar(50)  not null,
    target_data_set_id varchar(50)  not null,
    relation_name      varchar(255) not null,
    entity_relation    varchar(50)  not null,
    column_relation    varchar(50)  not null,
    create_by          varchar(64),
    create_time        timestamp,
    update_by          varchar(64),
    update_time        timestamp
    );

comment on column public.table_relation.data_set_id is '数据集ID';

comment on column public.table_relation.target_data_set_id is '目标数据集ID';

comment on column public.table_relation.relation_name is '关系名称';

comment on column public.table_relation.entity_relation is '字段关联关系；1：一对一 2：一对多';

comment on column public.table_relation.column_relation is '字段关系';

comment on column public.table_relation.create_by is '创建人';

comment on column public.table_relation.create_time is '创建时间';

comment on column public.table_relation.update_by is '修改人';

comment on column public.table_relation.update_time is '修改时间';

alter table public.table_relation
    owner to postgres;

create table if not exists public.asset_attribute_set
(
    id             varchar(36) not null
    primary key,
    catalogue_id   varchar(255),
    cate_gory_id   varchar(255),
    is_release     varchar(1),
    is_arrive      varchar(1),
    label_data_ids text,
    mount_type     varchar(1),
    mount_info     text,
    is_del         varchar(1) default '0'::character varying,
    create_by      varchar(255),
    create_time    timestamp,
    update_by      varchar(255),
    update_time    timestamp
    );

comment on column public.asset_attribute_set.id is '主键ID';

comment on column public.asset_attribute_set.catalogue_id is '目录ID';

comment on column public.asset_attribute_set.cate_gory_id is '类目ID';

comment on column public.asset_attribute_set.is_release is '发布状态;0：未发布，1：发布   未发布不能挂接';

comment on column public.asset_attribute_set.is_arrive is '上架状态;0：未上架，1：上架，2：下架';

comment on column public.asset_attribute_set.label_data_ids is '标签数据集ID列表';

comment on column public.asset_attribute_set.mount_type is '挂接类型；0：数据库表，1：文件';

comment on column public.asset_attribute_set.mount_info is '挂接信息';

comment on column public.asset_attribute_set.is_del is '逻辑删除标识;0:未删除,1:已删除';

comment on column public.asset_attribute_set.create_by is '创建人';

comment on column public.asset_attribute_set.create_time is '创建时间';

comment on column public.asset_attribute_set.update_by is '更新人';

comment on column public.asset_attribute_set.update_time is '更新时间';

alter table public.asset_attribute_set
    owner to postgres;

create table if not exists public.label_set
(
    id          varchar(36) not null
    primary key,
    name        varchar(255),
    is_del      varchar(1) default '0'::character varying,
    create_by   varchar(255),
    create_time timestamp,
    update_by   varchar(255),
    update_time timestamp
    );

comment on column public.label_set.id is '主键ID';

comment on column public.label_set.name is '标签集名称';

comment on column public.label_set.is_del is '逻辑删除标识;0:未删除,1:已删除';

comment on column public.label_set.create_by is '创建人';

comment on column public.label_set.create_time is '创建时间';

comment on column public.label_set.update_by is '更新人';

comment on column public.label_set.update_time is '更新时间';

alter table public.label_set
    owner to postgres;

create table if not exists public.asset_code
(
    id           varchar(36) not null
    primary key,
    catalogue_id varchar(255),
    level        integer,
    type         integer,
    rule         integer,
    code_digit   integer,
    left_range   varchar(255),
    right_range  varchar(255),
    is_del       varchar(1) default '0'::character varying,
    create_by    varchar(255),
    create_time  timestamp,
    update_by    varchar(255),
    update_time  timestamp
    );

comment on column public.asset_code.id is '主键ID';

comment on column public.asset_code.catalogue_id is '目录ID';

comment on column public.asset_code.level is '层级';

comment on column public.asset_code.type is '类型；1：目录规则，2：分割，3：资产规则';

comment on column public.asset_code.rule is '规则；1：流水号，2：大写字母，3：小写字母，4：字母数字组合';

comment on column public.asset_code.code_digit is '编码位数';

comment on column public.asset_code.left_range is '左边范围';

comment on column public.asset_code.right_range is '右边范围';

comment on column public.asset_code.is_del is '逻辑删除标识;0:未删除,1:已删除';

comment on column public.asset_code.create_by is '创建人';

comment on column public.asset_code.create_time is '创建时间';

comment on column public.asset_code.update_by is '更新人';

comment on column public.asset_code.update_time is '更新时间';

alter table public.asset_code
    owner to postgres;

create table if not exists public.asset_gate_way
(
    id                         varchar(36) not null
    primary key,
    catalogue_id               varchar(255),
    asset_gate_way_name        varchar(255),
    asset_attribute_ids        text,
    description                text,
    display_asset_attribute_id text,
    is_del                     varchar(1) default '0'::character varying,
    create_by                  varchar(255),
    create_time                timestamp,
    update_by                  varchar(255),
    update_time                timestamp
    );

comment on column public.asset_gate_way.id is '主键ID';

comment on column public.asset_gate_way.catalogue_id is '目录ID';

comment on column public.asset_gate_way.asset_gate_way_name is '资产门户名称';

comment on column public.asset_gate_way.asset_attribute_ids is '资产属性分类ID列表';

comment on column public.asset_gate_way.description is '描述';

comment on column public.asset_gate_way.display_asset_attribute_id is '显示资产属性ID列表';

comment on column public.asset_gate_way.is_del is '逻辑删除标识;0:未删除,1:已删除';

comment on column public.asset_gate_way.create_by is '创建人';

comment on column public.asset_gate_way.create_time is '创建时间';

comment on column public.asset_gate_way.update_by is '更新人';

comment on column public.asset_gate_way.update_time is '更新时间';

alter table public.asset_gate_way
    owner to postgres;

create table if not exists public.attribute_data
(
    id                      varchar(36) not null
    primary key,
    attribute_code          varchar(255),
    attribute_name          varchar(255),
    attribute_set_name      varchar(255),
    attribute_set_id        varchar(255),
    data_type               varchar(255),
    data_length             integer,
    data_precision          integer,
    column_nullable         varchar(1),
    description             text,
    related_dimension_table varchar(255),
    data_default            varchar(255),
    is_built_in             integer,
    is_del                  varchar(1) default '0'::character varying,
    create_by               varchar(255),
    create_time             timestamp,
    update_by               varchar(255),
    update_time             timestamp
    );

comment on column public.attribute_data.id is '主键ID';

comment on column public.attribute_data.attribute_code is '属性代码';

comment on column public.attribute_data.attribute_name is '属性名称';

comment on column public.attribute_data.attribute_set_name is '属性类别名称';

comment on column public.attribute_data.attribute_set_id is '属性类别ID';

comment on column public.attribute_data.data_type is '数据类型';

comment on column public.attribute_data.data_length is '数据长度';

comment on column public.attribute_data.data_precision is '数据精度';

comment on column public.attribute_data.column_nullable is '是否允许为空';

comment on column public.attribute_data.description is '描述';

comment on column public.attribute_data.related_dimension_table is '关联维表';

comment on column public.attribute_data.data_default is '默认值';

comment on column public.attribute_data.is_built_in is '是否内置';

comment on column public.attribute_data.is_del is '逻辑删除标识;0:未删除,1:已删除';

comment on column public.attribute_data.create_by is '创建人';

comment on column public.attribute_data.create_time is '创建时间';

comment on column public.attribute_data.update_by is '更新人';

comment on column public.attribute_data.update_time is '更新时间';

alter table public.attribute_data
    owner to postgres;

create table if not exists public.attribute_set
(
    id                 varchar(36) not null
    primary key,
    type               integer,
    number             integer,
    attribute_set_name varchar(255),
    parent_id          varchar(36),
    is_del             varchar(1) default '0'::character varying,
    create_by          varchar(255),
    create_time        timestamp,
    update_by          varchar(255),
    update_time        timestamp
    );

comment on column public.attribute_set.id is '主键ID';

comment on column public.attribute_set.type is '类型；0：目录属性，1：信息项属性';

comment on column public.attribute_set.number is '序号';

comment on column public.attribute_set.attribute_set_name is '属性集的名称';

comment on column public.attribute_set.parent_id is '父级ID';

comment on column public.attribute_set.is_del is '逻辑删除标识;0:未删除,1:已删除';

comment on column public.attribute_set.create_by is '创建人';

comment on column public.attribute_set.create_time is '创建时间';

comment on column public.attribute_set.update_by is '更新人';

comment on column public.attribute_set.update_time is '更新时间';

alter table public.attribute_set
    owner to postgres;

create table if not exists public.catalogue_attribute
(
    id                varchar(36) not null
    primary key,
    catalogue_id      varchar(255),
    attribute_set_id  varchar(255),
    attribute_data_id varchar(255),
    number            integer,
    is_disabled       varchar(1),
    is_editable       varchar(1),
    is_portal_display varchar(1),
    is_del            varchar(1) default '0'::character varying,
    create_by         varchar(255),
    create_time       timestamp,
    update_by         varchar(255),
    update_time       timestamp
    );

comment on column public.catalogue_attribute.id is '主键ID';

comment on column public.catalogue_attribute.catalogue_id is '目录ID';

comment on column public.catalogue_attribute.attribute_set_id is '属性集ID';

comment on column public.catalogue_attribute.attribute_data_id is '属性ID';

comment on column public.catalogue_attribute.number is '序号';

comment on column public.catalogue_attribute.is_disabled is '是否禁用；0：否；1：是  禁用时在资产编目时不展示，内置属性不可禁用';

comment on column public.catalogue_attribute.is_editable is '是否可编辑；0：否；1：是';

comment on column public.catalogue_attribute.is_portal_display is '门户显示；0：否；1：是';

comment on column public.catalogue_attribute.is_del is '逻辑删除标识;0:未删除,1:已删除';

comment on column public.catalogue_attribute.create_by is '创建人';

comment on column public.catalogue_attribute.create_time is '创建时间';

comment on column public.catalogue_attribute.update_by is '更新人';

comment on column public.catalogue_attribute.update_time is '更新时间';

alter table public.catalogue_attribute
    owner to postgres;

create table if not exists public.catalogue
(
    id                 varchar(36) not null
    primary key,
    number             integer,
    data_property_name varchar(255),
    cate_gory_name     varchar(255),
    cate_gory_id       varchar(255),
    asset_code_id      varchar(255),
    description        text,
    edit               varchar(1),
    openness           varchar(1),
    is_del             varchar(1) default '0'::character varying,
    create_by          varchar(255),
    create_time        timestamp,
    update_by          varchar(255),
    update_time        timestamp
    );

comment on column public.catalogue.id is '主键ID';

comment on column public.catalogue.number is '序号';

comment on column public.catalogue.data_property_name is '数据资产名称';

comment on column public.catalogue.cate_gory_name is '类目名称';

comment on column public.catalogue.cate_gory_id is '类目ID';

comment on column public.catalogue.asset_code_id is '资产编码ID';

comment on column public.catalogue.description is '描述';

comment on column public.catalogue.edit is '在编；0：未编；1：在编   在编才可资产编目';

comment on column public.catalogue.openness is '开放；0：未开放；1：开放   开放才可创建资产门户';

comment on column public.catalogue.is_del is '逻辑删除标识;0:未删除,1:已删除';

comment on column public.catalogue.create_by is '创建人';

comment on column public.catalogue.create_time is '创建时间';

comment on column public.catalogue.update_by is '更新人';

comment on column public.catalogue.update_time is '更新时间';

alter table public.catalogue
    owner to postgres;

create table if not exists public.cate_gory
(
    id             varchar(36) not null
    primary key,
    parent_id      varchar(36),
    level          integer,
    catalogue_id   varchar(255),
    cate_gory_name varchar(255),
    asset_code     varchar(255),
    description    text,
    is_del         varchar(1) default '0'::character varying,
    create_by      varchar(255),
    create_time    timestamp,
    update_by      varchar(255),
    update_time    timestamp
    );

comment on column public.cate_gory.id is '主键ID';

comment on column public.cate_gory.parent_id is '父级ID';

comment on column public.cate_gory.level is '层级';

comment on column public.cate_gory.catalogue_id is '目录ID';

comment on column public.cate_gory.cate_gory_name is '类目名称';

comment on column public.cate_gory.asset_code is '资产编码';

comment on column public.cate_gory.description is '描述';

comment on column public.cate_gory.is_del is '逻辑删除标识;0:未删除,1:已删除';

comment on column public.cate_gory.create_by is '创建人';

comment on column public.cate_gory.create_time is '创建时间';

comment on column public.cate_gory.update_by is '更新人';

comment on column public.cate_gory.update_time is '更新时间';

alter table public.cate_gory
    owner to postgres;

create table if not exists public.asset_attribute
(
    id                     varchar(36) not null
    primary key,
    asset_attribute_set_id varchar(255),
    catalogue_attr_id      varchar(255),
    attribute_name         varchar(255),
    attribute_code         varchar(255),
    attribute_value        text,
    create_by              varchar(255),
    create_time            timestamp,
    update_by              varchar(255),
    update_time            timestamp
    );

comment on column public.asset_attribute.id is '主键ID';

comment on column public.asset_attribute.asset_attribute_set_id is '资产ID';

comment on column public.asset_attribute.catalogue_attr_id is '编目属性ID';

comment on column public.asset_attribute.attribute_name is '属性名称';

comment on column public.asset_attribute.attribute_code is '属性代码';

comment on column public.asset_attribute.attribute_value is '属性值列表';

comment on column public.asset_attribute.create_by is '创建人';

comment on column public.asset_attribute.create_time is '创建时间';

comment on column public.asset_attribute.update_by is '更新人';

comment on column public.asset_attribute.update_time is '更新时间';

alter table public.asset_attribute
    owner to postgres;

create table if not exists public.label_data
(
    id           varchar(36) not null
    primary key,
    label_set_id varchar(255),
    name         varchar(255),
    description  text,
    is_del       varchar(1) default '0'::character varying,
    create_by    varchar(255),
    create_time  timestamp,
    update_by    varchar(255),
    update_time  timestamp
    );

comment on column public.label_data.id is '主键ID';

comment on column public.label_data.label_set_id is '标签集ID';

comment on column public.label_data.name is '名称';

comment on column public.label_data.description is '描述';

comment on column public.label_data.is_del is '逻辑删除标识;0:未删除,1:已删除';

comment on column public.label_data.create_by is '创建人';

comment on column public.label_data.create_time is '创建时间';

comment on column public.label_data.update_by is '更新人';

comment on column public.label_data.update_time is '更新时间';

alter table public.label_data
    owner to postgres;

create table if not exists public.xyz
(
    id             text,
    lawsuit_amount text
);

comment on column public.xyz.id is '主键';

comment on column public.xyz.lawsuit_amount is '诉讼金额';

alter table public.xyz
    owner to postgres;

create table if not exists public.api_sync_config_new
(
    id                     bigint       not null,
    api_id                 varchar(100),
    sys_id                 varchar(100),
    name                   varchar(100) not null,
    sys_name               varchar(100),
    org_name               varchar(100),
    api_description        text,
    service_category       varchar(100),
    service_subcategory    varchar(100),
    api_version            varchar(100),
    url                    varchar(500) not null,
    invocation_method      varchar(100),
    method                 varchar(10)  not null,
    data_format            varchar(10)  not null,
    pre_api_id             bigint,
    headers_json           text,
    params_json            text,
    response_parse_script  text,
    authentication_method  varchar(100),
    access_control         varchar(100),
    sensitive_data_flag    boolean   default true,
    encryption_requirement varchar(100),
    api_account            varchar(100),
    api_password           varchar(100),
    call_frequency         varchar(100),
    avg_response_time      varchar(100),
    error_rate             varchar(100),
    timeout_threshold      varchar(100),
    monitoring_config      text,
    api_owner              varchar(100),
    api_contact            varchar(100),
    documentation_url      varchar(100),
    enabled                boolean   default true,
    last_update_time       timestamp default now(),
    remarks                text,
    field_mapping_json     text,
    target_table           varchar(100),
    cron_expression        varchar(50),
    create_time            timestamp default now(),
    update_time            timestamp default now()
    );

comment on column public.api_sync_config_new.api_id is '接口编号';

comment on column public.api_sync_config_new.sys_id is '系统id';

comment on column public.api_sync_config_new.name is '接口名称';

comment on column public.api_sync_config_new.sys_name is '所属系统名称';

comment on column public.api_sync_config_new.org_name is '负责公司';

comment on column public.api_sync_config_new.api_description is '接口功能描述';

comment on column public.api_sync_config_new.service_category is '服务大类';

comment on column public.api_sync_config_new.service_subcategory is '服务小类';

comment on column public.api_sync_config_new.api_version is '接口版本';

comment on column public.api_sync_config_new.url is '接口地址';

comment on column public.api_sync_config_new.invocation_method is '调用方式';

comment on column public.api_sync_config_new.method is '请求方式';

comment on column public.api_sync_config_new.data_format is '数据格式';

comment on column public.api_sync_config_new.pre_api_id is '前缀API（id）';

comment on column public.api_sync_config_new.headers_json is '请求头json';

comment on column public.api_sync_config_new.params_json is '请求参数json';

comment on column public.api_sync_config_new.response_parse_script is '响应解析脚本';

comment on column public.api_sync_config_new.authentication_method is '认证方式';

comment on column public.api_sync_config_new.access_control is '权限控制';

comment on column public.api_sync_config_new.sensitive_data_flag is '敏感数据';

comment on column public.api_sync_config_new.encryption_requirement is '加密要求';

comment on column public.api_sync_config_new.api_account is 'api账号';

comment on column public.api_sync_config_new.api_password is 'api密码';

comment on column public.api_sync_config_new.call_frequency is '调用频率';

comment on column public.api_sync_config_new.avg_response_time is '平均响应时间';

comment on column public.api_sync_config_new.error_rate is '错误率（近30天）';

comment on column public.api_sync_config_new.timeout_threshold is '超时阈值';

comment on column public.api_sync_config_new.monitoring_config is '告警条件';

comment on column public.api_sync_config_new.api_owner is '接口负责人';

comment on column public.api_sync_config_new.api_contact is '联系方式';

comment on column public.api_sync_config_new.documentation_url is '文档连接地址';

comment on column public.api_sync_config_new.enabled is '接口状态';

comment on column public.api_sync_config_new.last_update_time is '接口最后更新时间';

comment on column public.api_sync_config_new.remarks is '备注';

alter table public.api_sync_config_new
    owner to postgres;

create table if not exists public.t_model_base
(
    id              bigint                 not null
    constraint pk_t_model_base_id
    primary key,
    title           varchar,
    source_id       varchar,
    model_uuid      varchar,
    code_no         varchar,
    model_type      integer,
    t_name          varchar,
    table_id        varchar,
    extend_property varchar,
    is_enable       integer      default 0 not null,
    creator_id      integer                not null,
    creator_name    varchar,
    create_date     timestamp(6) default now(),
    modiftor        varchar,
    modiftor_id     integer,
    modify_date     timestamp(6) default now(),
    model_lib_no    bigint,
    model_name      varchar(255),
    remark          varchar(500),
    field_sort_num  integer
    );

comment on table public.t_model_base is '模型主表 模型主表';

comment on column public.t_model_base.id is '编号';

comment on column public.t_model_base.title is '标题';

comment on column public.t_model_base.source_id is '数据源id';

comment on column public.t_model_base.model_uuid is '模型id';

comment on column public.t_model_base.code_no is '代号';

comment on column public.t_model_base.model_type is '类型 3=模型分组4=物理模型';

comment on column public.t_model_base.t_name is '数据库表';

comment on column public.t_model_base.table_id is '数据库表ID';

comment on column public.t_model_base.extend_property is '扩展属性 扩展属性';

comment on column public.t_model_base.is_enable is '是否有效 0=有效1=无效';

comment on column public.t_model_base.creator_id is '创建人编号 当前用户ID';

comment on column public.t_model_base.creator_name is '创建人姓名';

comment on column public.t_model_base.create_date is '创建日期 默认为当前时间';

comment on column public.t_model_base.modiftor is '修改人';

comment on column public.t_model_base.modiftor_id is '修改人编号 修改人编号';

comment on column public.t_model_base.modify_date is '修改时间 修改时间';

comment on column public.t_model_base.model_lib_no is '所属模型库ID';

comment on column public.t_model_base.model_name is '模型名称';

comment on column public.t_model_base.remark is '描述';

comment on column public.t_model_base.field_sort_num is '排序号';

alter table public.t_model_base
    owner to postgres;

create index if not exists idx_t_modelase_creator_id0ac8
    on public.t_model_base (creator_id);

create table if not exists public.t_model_column
(
    id             bigint                 not null
    constraint pk_t_model_column_id
    primary key,
    t_name         varchar,
    alias_name     varchar,
    title          varchar(255),
    is_dim         integer,
    relation_dim   varchar,
    create_id      integer                not null,
    is_enable      integer      default 0 not null,
    creator_name   varchar,
    create_date    timestamp(6) default now(),
    data_level     varchar(50),
    t_length       integer,
    decimal_digits integer,
    is_null        integer,
    is_unique      integer,
    remark         varchar(500),
    model_no       bigint,
    modiftor       varchar,
    modiftor_id    integer,
    modify_date    timestamp(6),
    field_sort_num integer
    );

comment on table public.t_model_column is '模型字段表 模型字段表';

comment on column public.t_model_column.id is '编号';

comment on column public.t_model_column.t_name is '名称 名称';

comment on column public.t_model_column.alias_name is '别名 别名';

comment on column public.t_model_column.title is '标题';

comment on column public.t_model_column.is_dim is '是否维度字段 0=非维度字段1=维度字段';

comment on column public.t_model_column.relation_dim is '关联维度表';

comment on column public.t_model_column.create_id is '创建人编号 当前用户ID';

comment on column public.t_model_column.is_enable is '是否有效 0=有效1=无效';

comment on column public.t_model_column.creator_name is '创建人姓名';

comment on column public.t_model_column.create_date is '创建日期 默认为当前时间';

comment on column public.t_model_column.data_level is '数据类型 1=字符类型  2=数值类型  3=时间类型  4=布尔类型';

comment on column public.t_model_column.t_length is '长度 长度';

comment on column public.t_model_column.decimal_digits is '小数位数 小数位数';

comment on column public.t_model_column.is_null is '是否可为空 0=不可为空  1=可为空';

comment on column public.t_model_column.is_unique is '是否唯一 0=不唯一  1=唯一';

comment on column public.t_model_column.remark is '描述 描述';

comment on column public.t_model_column.model_no is '模型编号 模型编号';

comment on column public.t_model_column.modiftor is '修改人，记录最后一次修改该模型库信息的用户';

comment on column public.t_model_column.modiftor_id is '修改人编号，记录最后一次修改该模型库信息的用户的编号';

comment on column public.t_model_column.modify_date is '修改日期，记录该模型库信息最后一次被修改的时间';

comment on column public.t_model_column.field_sort_num is '字段属性排序';

alter table public.t_model_column
    owner to postgres;

create index if not exists idx_t_modelumn_create_id3e5c
    on public.t_model_column (create_id);

create table if not exists public.t_model_fk_info
(
    id             bigint  not null
    constraint pk_t_modelkey_id7c2a
    primary key,
    f_name         varchar not null,
    f_col          varchar(255),
    relation_table varchar,
    relation_col   varchar,
    is_enable      integer      default 0,
    model_no       bigint,
    creator_id     integer not null,
    creator_name   varchar,
    create_date    timestamp(6) default now(),
    modiftor_id    integer,
    modiftor       varchar,
    modify_date    timestamp(6) default now()
    );

comment on table public.t_model_fk_info is '模型外键表 模型外键表';

comment on column public.t_model_fk_info.id is '编号';

comment on column public.t_model_fk_info.f_name is '外键名称';

comment on column public.t_model_fk_info.f_col is '外键列名';

comment on column public.t_model_fk_info.relation_table is '关联表名';

comment on column public.t_model_fk_info.relation_col is '关联列名';

comment on column public.t_model_fk_info.is_enable is '是否有效 0=有效1=无效';

comment on column public.t_model_fk_info.model_no is '模型编号';

comment on column public.t_model_fk_info.creator_id is '创建人编号 当前用户ID';

comment on column public.t_model_fk_info.creator_name is '创建人姓名';

comment on column public.t_model_fk_info.create_date is '创建日期 默认为当前时间';

comment on column public.t_model_fk_info.modiftor_id is '修改人编号';

comment on column public.t_model_fk_info.modiftor is '修改人';

comment on column public.t_model_fk_info.modify_date is '修改日期 默认为当前时间';

alter table public.t_model_fk_info
    owner to postgres;

create index if not exists idx_t_modelkey_creator_id2724
    on public.t_model_fk_info (creator_id);

create table if not exists public.t_model_index
(
    id           bigint                 not null
    constraint pk_t_model_index_id
    primary key,
    title        varchar,
    t_type       varchar                not null,
    t_col        varchar(255),
    is_enable    integer      default 0 not null,
    model_no     bigint,
    creator_id   integer                not null,
    creator_name varchar,
    create_date  timestamp(6) default now(),
    modiftor     varchar,
    modiftor_id  integer,
    modify_date  timestamp(6)
    );

comment on table public.t_model_index is '模型索引表 模型索引表';

comment on column public.t_model_index.id is '编号';

comment on column public.t_model_index.title is '名称';

comment on column public.t_model_index.t_type is '类型';

comment on column public.t_model_index.t_col is '列';

comment on column public.t_model_index.is_enable is '是否有效 0=有效1=无效';

comment on column public.t_model_index.model_no is '模型编号 模型编号';

comment on column public.t_model_index.creator_id is '创建人编号 当前用户ID';

comment on column public.t_model_index.creator_name is '创建人姓名 创建人姓名';

comment on column public.t_model_index.create_date is '创建日期 默认为当前时间';

comment on column public.t_model_index.modiftor is '修改人，记录最后一次修改该模型库信息的用户';

comment on column public.t_model_index.modiftor_id is '修改人编号，记录最后一次修改该模型库信息的用户的编号';

comment on column public.t_model_index.modify_date is '修改日期，记录该模型库信息最后一次被修改的时间';

alter table public.t_model_index
    owner to postgres;

create index if not exists idx_t_modeldex_creator_id1e65
    on public.t_model_index (creator_id);

create table if not exists public.t_model_lib_info
(
    id               bigint                 not null
    constraint pk_t_model_lib_info_id
    primary key,
    pid              bigint,
    model_type       integer,
    model_lib_name   varchar                not null,
    source_id        varchar(255),
    database_pool_id integer,
    mark             varchar(500),
    is_enable        integer      default 0 not null,
    creator_id       integer                not null,
    creator_name     varchar,
    create_date      timestamp(6) default now(),
    modify_date      timestamp(6),
    modiftor         varchar,
    modiftor_id      integer
    );

comment on table public.t_model_lib_info is '模型库信息';

comment on column public.t_model_lib_info.id is '编号';

comment on column public.t_model_lib_info.pid is '父编号 父编号';

comment on column public.t_model_lib_info.model_type is '节点类型 1=模型管理，2=模型库，3=模型分组';

comment on column public.t_model_lib_info.model_lib_name is '模型库标题';

comment on column public.t_model_lib_info.source_id is '资源ID 资源ID';

comment on column public.t_model_lib_info.database_pool_id is '数据存储连接池ID 数据存储连接池ID';

comment on column public.t_model_lib_info.mark is '描述';

comment on column public.t_model_lib_info.is_enable is '是否有效 0=有效1=无效';

comment on column public.t_model_lib_info.creator_id is '创建人编号 当前用户ID';

comment on column public.t_model_lib_info.creator_name is '创建人姓名';

comment on column public.t_model_lib_info.create_date is '创建日期 默认为当前时间';

comment on column public.t_model_lib_info.modify_date is '修改日期';

comment on column public.t_model_lib_info.modiftor is '修改人';

comment on column public.t_model_lib_info.modiftor_id is '修改人编号 修改人编号';

alter table public.t_model_lib_info
    owner to postgres;

create index if not exists idx_t_modelnfo_creator_ide5df
    on public.t_model_lib_info (creator_id);

create table if not exists public.t_model_mapping_table
(
    id               bigint                 not null
    constraint pk_t_modelble_ida825
    primary key,
    code_no          varchar,
    model_titile     varchar(50)            not null,
    table_view_name  varchar(100),
    filter_condition varchar(255),
    model_lib_no     bigint
    constraint fk_t_modelble_model_l_no1e6f
    references public.t_model_lib_info,
    is_enable        integer      default 0 not null,
    creator_id       integer                not null,
    creator_name     varchar(20),
    create_date      timestamp(6) default now(),
    modiftor_id      integer,
    modiftor         varchar,
    modify_date      timestamp(6) default now()
    );

comment on table public.t_model_mapping_table is '模型数据映射表 模型数据映射表';

comment on column public.t_model_mapping_table.id is '编号';

comment on column public.t_model_mapping_table.code_no is '代号';

comment on column public.t_model_mapping_table.model_titile is '模型标题';

comment on column public.t_model_mapping_table.table_view_name is '表或者视图';

comment on column public.t_model_mapping_table.filter_condition is '过滤条件';

comment on column public.t_model_mapping_table.model_lib_no is '模型库';

comment on column public.t_model_mapping_table.is_enable is '是否有效 是否有效，0=有效1=无效';

comment on column public.t_model_mapping_table.creator_id is '创建人编号 当前用户ID';

comment on column public.t_model_mapping_table.creator_name is '创建人姓名';

comment on column public.t_model_mapping_table.create_date is '创建日期 默认为当前时间';

comment on column public.t_model_mapping_table.modiftor_id is '修改人编号';

comment on column public.t_model_mapping_table.modiftor is '修改人';

comment on column public.t_model_mapping_table.modify_date is '修改日期 默认为当前时间';

alter table public.t_model_mapping_table
    owner to postgres;

create index if not exists idx_t_modelble_model_l_no1e6f
    on public.t_model_mapping_table (model_lib_no);

create table if not exists public.t_model_pk_info
(
    id           bigint  not null
    constraint pk_t_model_pk_key_id7c2a
    primary key,
    p_column     varchar,
    is_enable    integer      default 0,
    model_no     bigint,
    creator_id   integer not null,
    creator_name varchar,
    create_date  timestamp(6) default now(),
    modiftor_id  integer,
    modiftor     varchar,
    modify_date  timestamp(6) default now()
    );

comment on table public.t_model_pk_info is '模型主键表';

comment on column public.t_model_pk_info.id is '编号';

comment on column public.t_model_pk_info.p_column is '主键列';

comment on column public.t_model_pk_info.is_enable is '是否有效 0=有效1=无效';

comment on column public.t_model_pk_info.model_no is '模型编号';

comment on column public.t_model_pk_info.creator_id is '创建人编号 当前用户ID';

comment on column public.t_model_pk_info.creator_name is '创建人姓名';

comment on column public.t_model_pk_info.create_date is '创建日期 默认为当前时间';

comment on column public.t_model_pk_info.modiftor_id is '修改人编号';

comment on column public.t_model_pk_info.modiftor is '修改人';

comment on column public.t_model_pk_info.modify_date is '修改日期 默认为当前时间';

alter table public.t_model_pk_info
    owner to postgres;

create table if not exists public.t_model_relation_info
(
    id                bigint                 not null
    constraint pk_t_model_relation_id0f72
    primary key,
    model_relation_id bigint                 not null,
    model_id          bigint,
    is_enable         integer      default 0 not null,
    creator_id        integer                not null,
    creator_name      varchar,
    create_date       timestamp(6) default now(),
    modiftor          varchar,
    modiftor_id       integer,
    modify_date       timestamp(6) default now()
    );

comment on table public.t_model_relation_info is '模型关联关系记录表';

comment on column public.t_model_relation_info.id is '编号';

comment on column public.t_model_relation_info.model_relation_id is '关联ID';

comment on column public.t_model_relation_info.model_id is '模型ID';

comment on column public.t_model_relation_info.is_enable is '是否有效 0=有效1=无效';

comment on column public.t_model_relation_info.creator_id is '创建人编号 当前用户ID';

comment on column public.t_model_relation_info.creator_name is '创建人姓名';

comment on column public.t_model_relation_info.create_date is '创建日期 默认为当前时间';

comment on column public.t_model_relation_info.modiftor is '修改人';

comment on column public.t_model_relation_info.modiftor_id is '修改人编号';

comment on column public.t_model_relation_info.modify_date is '修改时间 修改时间';

alter table public.t_model_relation_info
    owner to postgres;

create table if not exists public.t_model_table_relation
(
    id           bigint                 not null
    constraint pk_t_modelion_id0f72
    primary key,
    t_name       varchar                not null,
    t_def        varchar(500),
    union_method integer                not null,
    t_exp        varchar(500),
    model_lib_no bigint,
    is_enable    integer      default 0 not null,
    creator_id   integer                not null,
    creator_name varchar,
    create_date  timestamp(6) default now(),
    modiftor     varchar,
    modiftor_id  integer,
    modify_date  timestamp(6) default now()
    );

comment on table public.t_model_table_relation is '模型表关联关系记录表 模型表关联关系记录表';

comment on column public.t_model_table_relation.id is '编号';

comment on column public.t_model_table_relation.t_name is '名称';

comment on column public.t_model_table_relation.t_def is '表定义';

comment on column public.t_model_table_relation.union_method is '连接方式';

comment on column public.t_model_table_relation.t_exp is '表达式';

comment on column public.t_model_table_relation.model_lib_no is '所属模型库编号 所属模型库编号';

comment on column public.t_model_table_relation.is_enable is '是否有效 0=有效1=无效';

comment on column public.t_model_table_relation.creator_id is '创建人编号 当前用户ID';

comment on column public.t_model_table_relation.creator_name is '创建人姓名';

comment on column public.t_model_table_relation.create_date is '创建日期 默认为当前时间';

comment on column public.t_model_table_relation.modiftor is '修改人';

comment on column public.t_model_table_relation.modiftor_id is '修改人编号';

comment on column public.t_model_table_relation.modify_date is '修改时间 修改时间';

alter table public.t_model_table_relation
    owner to postgres;

create table if not exists public.t_indicator_dim_relation
(
    id           bigint not null
    primary key,
    indicator_no bigint,
    dim_no       bigint
);

comment on column public.t_indicator_dim_relation.id is '主键ID';

comment on column public.t_indicator_dim_relation.indicator_no is '指标编号';

comment on column public.t_indicator_dim_relation.dim_no is '维度编号';

alter table public.t_indicator_dim_relation
    owner to postgres;

create table if not exists public.idp_task_flow_execution_log
(
    id                  varchar(100) not null
    primary key,
    schedule_id         varchar(100),
    schedule_history_id varchar(100),
    node_id             varchar(255),
    node_type           varchar(255),
    node_name           varchar(255),
    exec_status         varchar(50),
    exec_time           bigint,
    exec_result         text,
    exec_log            text,
    exec_order          integer
    );

comment on table public.idp_task_flow_execution_log is '任务流执行日志';

alter table public.idp_task_flow_execution_log
    owner to postgres;

create index if not exists idx_schedule_id
    on public.idp_task_flow_execution_log (schedule_id);

create index if not exists idx_schedule_history_id
    on public.idp_task_flow_execution_log (schedule_history_id);

create index if not exists idx_node_id
    on public.idp_task_flow_execution_log (node_id);

create index if not exists idx_exec_status
    on public.idp_task_flow_execution_log (exec_status);

create table if not exists public.idp_task_flow_schedule_history
(
    id             varchar(36) not null
    primary key,
    schedule_id    varchar(255),
    task_flow_id   varchar(255),
    task_flow_name varchar(255),
    start_time     timestamp,
    end_time       timestamp,
    status         integer,
    result         text,
    error_message  text,
    retry_count    integer,
    executor       varchar(255),
    create_time    timestamp
    );

comment on table public.idp_task_flow_schedule_history is '任务流调度历史记录';

alter table public.idp_task_flow_schedule_history
    owner to postgres;

create index if not exists idx_schedule_id_history
    on public.idp_task_flow_schedule_history (schedule_id);

create index if not exists idx_task_flow_id
    on public.idp_task_flow_schedule_history (task_flow_id);

create index if not exists idx_status
    on public.idp_task_flow_schedule_history (status);

create index if not exists idx_create_time
    on public.idp_task_flow_schedule_history (create_time);

