package com.dib.model;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication(scanBasePackages = {"com.dib.model",  "com.dib.metadata", "com.dib.common",  "com.dib.async","com.dib.framework","com.dib.bigdata.service.impl"})
@MapperScan({"com.dib.model.mapper","com.dib.metadata.mapper"})
@EnableTransactionManagement
public class IdpModelManageApplication
{
    public static void main(String[] args) {
        SpringApplication.run(IdpModelManageApplication.class, args);
        System.out.println("      _   _   _     \n" +
                "     | | (_) | |    \n" +
                "   __| |  _  | |__  \n" +
                "  / _` | | | | '_ \\ \n" +
                " | (_| | | | | |_) |\n" +
                "  \\__,_| |_| |_.__/ \n" +
                "                    \n" +
                "                    ");
    }

}
