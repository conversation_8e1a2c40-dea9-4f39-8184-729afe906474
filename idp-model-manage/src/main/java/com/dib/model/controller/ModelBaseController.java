package com.dib.model.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.common.core.domain.entity.ReturnT;
import com.dib.common.utils.poi.ExcelUtil;
import com.dib.metadata.vo.MetadataColumnVo;
import com.dib.metadata.vo.SqlConsoleVo;
import com.dib.model.service.TModelBaseService;
import com.dib.model.vo.*;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

/**
 * 模型详情管理
 **/
@RestController
@RequestMapping("/model-base")
public class ModelBaseController {

    // 自动注入 TModelBaseService，用于处理 TModelBase 相关的业务逻辑
    @Autowired
    private TModelBaseService modelBaseService;

    /**
     * 创建模型详情信息记录的接口
     *
     * @param modelBase 要创建的 TModelBase 实体对象，通过请求体传入
     * @return
     */
    @PostMapping("/create")
    public ReturnT<TModelBaseInfoVo> create(@RequestBody  @Validated TModelBaseAddVo modelBase) {
        try {
            TModelBaseInfoVo createdModelBase = modelBaseService.create(modelBase);
            return ReturnT.success(createdModelBase);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnT.fail("创建模型详情信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据模型库或者模型分组ID获取模型详情列表信息接口
     *
     * @return ResponseEntity<List < TModelBaseVo>> 返回一个包含所有 TModelBase 实体对象列表的响应对象，
     * 以及 HTTP 状态码 HttpStatus.OK（200，表示请求成功）
     */
    @GetMapping("/list/{modelLibNo}")
    public ReturnT<List<TModelBaseVo>> getModelBaseList(@PathVariable Long modelLibNo) {
        try {
            List<TModelBaseVo> modelBases = modelBaseService.getModelBaseList(modelLibNo);
            return ReturnT.success(modelBases);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("根据模型库或者模型分组ID获取模型详情列表信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据 ID 获取模型详情信息记录的接口
     *
     * @param id 要获取的 TModelBase 记录的 ID，通过路径参数传入
     * @return ResponseEntity<TModelBase> 如果找到对应的 TModelBase 记录，返回包含该记录的响应对象，
     * 以及 HTTP 状态码 HttpStatus.OK（200）；如果未找到，返回一个空的响应对象，
     * 以及 HTTP 状态码 HttpStatus.NOT_FOUND（404）
     */
    @GetMapping("/{id}")
    public ReturnT<TModelBaseInfoVo> getModelBaseInfo(@PathVariable Long id) {
        try {
            TModelBaseInfoVo modelBase = modelBaseService.getModelBaseInfo(id);
            return ReturnT.success(modelBase);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("根据 ID 获取模型详情信息记录失败: " + e.getMessage());
        }
    }

    /**
     * @Author: lengdongxue
     * @Date: 2025/5/30 12:02
     * @param vo
     * @Return: com.dib.common.core.domain.entity.ReturnT<com.dib.model.domain.TModelBase>
     * @Description: 克隆模型
     **/
    @PostMapping("/copy")
    public ReturnT<TModelBaseInfoVo> copy(@RequestBody @Valid ModelIdReqVo vo) {
        try {
            TModelBaseInfoVo modelBase = modelBaseService.copy(vo);
            return ReturnT.success(modelBase);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("克隆模型失败: " + e.getMessage());
        }
    }

    /**
     * 更新模型详情信息记录的接口
     *
     * @param modelBase 包含更新后数据的 TModelBase 实体对象，通过请求体传入
     * @return ResponseEntity<TModelBase> 如果更新成功，返回包含更新后的 TModelBase 实体的响应对象，
     * 以及 HTTP 状态码 HttpStatus.OK（200）；如果未找到要更新的记录（更新失败），
     * 返回一个空的响应对象，以及 HTTP 状态码 HttpStatus.NOT_FOUND（404）
     */
    @PutMapping
    public ReturnT<TModelBaseInfoVo> update(@RequestBody TModelBaseInfoVo modelBase) {
        try {
            TModelBaseInfoVo updatedModelBase = modelBaseService.update(modelBase);
            return ReturnT.success(updatedModelBase);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("更新模型详情信息记录失败: " + e.getMessage());
        }
    }

    /**
     * 删除模型详情信息记录的接口
     *
     * @param id 要删除的 TModelBase 记录的 ID，通过路径参数传入
     * @return ResponseEntity<Void> 如果删除成功，返回一个空的响应对象，
     * 以及 HTTP 状态码 HttpStatus.NO_CONTENT（204，表示删除成功且无内容返回）；
     * 如果未找到要删除的记录（删除失败），返回一个空的响应对象，
     * 以及 HTTP 状态码 HttpStatus.NOT_FOUND（404）
     */
    @DeleteMapping("/{id}")
    public ReturnT<Boolean> delete(@PathVariable Long id) {
        try {
            boolean result = modelBaseService.delete(id);
            return ReturnT.success(result);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("删除模型详情信息记录失败: " + e.getMessage());
        }
    }


    /**
     * 导出模型信息
     *
     * @param modelLibNo 模型库编号
     * @param response
     */
    @PostMapping("/exportModelList")
    public void exportDetailByCard(HttpServletResponse response, @RequestParam(value = "modelLibNo") String modelLibNo) {

        try {
            if (modelLibNo == null || modelLibNo.trim().isEmpty()) {
                return;
            }
            Long modelLibNoLong = Long.parseLong(modelLibNo);
            List<TModelBaseVo> modelBasesList = modelBaseService.getModelBaseList(modelLibNoLong);
            ExcelUtil<TModelBaseVo> util = new ExcelUtil<TModelBaseVo>(TModelBaseVo.class);
            util.exportExcel(response, modelBasesList, "模型信息");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入模型信息
     *
     * @param file     导入文件
     * @param fileType 1:忽略 2:覆盖 3:新建
     * @return
     */
    @PostMapping("/importModelList")
    public ReturnT<Boolean> importModelList(@RequestParam("file") MultipartFile file, Integer fileType) {
        try {
            if (file == null || file.isEmpty()) {
                return ReturnT.fail("导入模型信息失败: 文件为空");
            }

            if (!file.getOriginalFilename().endsWith(".xlsx")) {
                return ReturnT.fail("文件类型不匹配,请重试！！！");
            }
            ExcelUtil<TModelBaseVo> util = new ExcelUtil<>(TModelBaseVo.class);
            List<TModelBaseVo> modelBasesList = util.importExcel(file.getInputStream());
            return ReturnT.success(modelBaseService.importModelList(modelBasesList));
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnT.fail("导入模型信息失败: " + e.getMessage());
        }
    }


    /**
     * 查询模型字段信息
     *
     * @param selectColumnVo
     * @return
     */
    @PostMapping("/selectColumnInfo")
    public ReturnT<List<MetadataColumnVo>> selectColumnInfo(@RequestBody SelectColumnVo selectColumnVo) {
        try {
            List<MetadataColumnVo> columnList = modelBaseService.queryColList(selectColumnVo);
            return ReturnT.success(columnList);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnT.fail("查询模型字段信息失败: " + e.getMessage());
        }
    }


    /**
     * 查询所有模型详细列表信息
     *
     * @return
     */
    @GetMapping("/selectAllModelInfo")
    public ReturnT<List<TModelBaseInfoVo>> selectAllModelDetailList() {
        try {
            List<TModelBaseInfoVo> tModelBases = modelBaseService.queryAllModelList();
            return ReturnT.success(tModelBases);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnT.fail("查询所有模型详细信息失败: " + e.getMessage());
        }
    }

    /**
     * 查询指定模型下的指标和维度列表信息
     *
     * @param modelNo 模型编号
     * @return
     */
    @GetMapping("/selectIndictorDimByModelId/{modelNo}")
    public ReturnT<IndictorDimVo> selectIndictorDimByModelId(@PathVariable String modelNo) {
        try {
            IndictorDimVo indictorDimVo = modelBaseService.queryIndicAndDimList(modelNo);
            return ReturnT.success(indictorDimVo);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnT.fail("查询指定模型下的指标和维度列表信息失败: " + e.getMessage());
        }
    }

    /**
     * 查询模型实例化表后的数据
     *
     * @param vo
     * @return
     */
    @PostMapping("/selectModelData")
    public ReturnT<List<SqlConsoleVo>> selectModelData(@RequestBody SelectModelDataVo vo) {
        try {
            List<SqlConsoleVo> sqlConsoleVos = modelBaseService.queryModelData(vo.getIndictorAttrs(), vo.getModelNo());
            return ReturnT.success(sqlConsoleVos);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnT.fail("模型实例化表未实例化,请确认模型表是否已创建！！！");
        }
    }

    /**
     * 重新同步数据库表结构
     *
     * @param modelNo 模型编号
     * @return
     */
    @GetMapping("/tableStructSync/{modelNo}")
    public ReturnT<Boolean> tableStructSync(@PathVariable String modelNo) {
        try {
            Boolean isSyncFinsh = modelBaseService.tableModelStructSync(modelNo);
            return ReturnT.success(isSyncFinsh);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnT.fail("模型实例化表不存在，请确认模型表已经被实例化！！！ ");
        }
    }

    /**
     * 锁定数据库表
     *
     * @param modelNo 模型编号
     * @param isLock  1为锁表，0为解锁表
     * @return
     */
    @PostMapping("/lockTable")
    public ReturnT<SqlConsoleVo> lockTableByModelNo(@RequestParam("modelNo") String modelNo, @RequestParam("isLock") String isLock) {
        try {
            SqlConsoleVo sqlConsoleVos = modelBaseService.lockTableByModelNo(modelNo, isLock);
            return ReturnT.success(sqlConsoleVos);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnT.fail("锁定或释放报错,具体报错: " + e.getMessage());
        }
    }

    /**
     * 查询模型管理模块中所有的表包含（数据源id,表id,字段列表）
     */
    @PostMapping("/modelTableByModelNo")
    public AjaxResult modelTableByModelNo() {
        try {
            return AjaxResult.success(modelBaseService.modelAllTabByModelNo());
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("查询模型管理模块中所有的表包含报错,具体报错: " + e.getMessage());
        }
    }


    /**
     * 查询所有主外键信息
     *
     * @param modelNo 模型编号
     * @return 主外键信息
     */
    @GetMapping("/queryAllKey/{modelNo}")
    public ReturnT<TModelQueryKeyVo> queryAllKey(@PathVariable Long modelNo) {
        try {
            return ReturnT.success(modelBaseService.queryModelKeyInfo(modelNo));
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("查询所有主外键信息失败: " + e.getMessage());
        }
    }

}
