package com.dib.model.controller;

import com.dib.common.core.domain.entity.ReturnT;
import com.dib.model.domain.TModelLibInfo;
import com.dib.model.service.TModelLibInfoService;
import com.dib.model.vo.TModelLibInfoVo;
import com.dib.model.vo.TModelLibUpdateInfoVo;
import com.dib.model.vo.TreeNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模型库管理接口
 */
@RestController
@Slf4j
@RequestMapping("/model-lib")
public class ModelLibController {

    @Autowired
    private TModelLibInfoService tModelManageService;

    // 注入 TModelLibInfoService，用于处理与模型库信息相关的业务逻辑
    @Autowired
    private TModelLibInfoService modelLibInfoService;

    /**
     * 获取模型库树形结构接口
     *
     * @return 返回一个包含模型库树形结构信息的 ReturnT 对象，
     * 其中泛型为 List<TreeNode>，表示模型库树形结构的节点列表。
     */
    @GetMapping("/tree")
    public ReturnT<List<TreeNode>> getModelTreeNodeInfo() {
        try {
            List<TreeNode> treeNodes = tModelManageService.buildNavigationTree();
            return ReturnT.success(treeNodes);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("获取模型库树形结构信息失败: " + e.getMessage());
        }

    }

    /**
     * 创建模型库信息记录接口
     *
     * @param modelLibInfoVo 包含要创建的模型库信息的VO对象，通过请求体传递。
     * @return 返回一个包含创建后的模型库信息VO对象的ReturnT对象
     */
    @PostMapping("/create")
    public ReturnT<TModelLibInfoVo> create(@RequestBody TModelLibInfoVo modelLibInfoVo) {
        try {
            // 调用service层的createFromVo方法，将VO转换为实体并保存，然后将保存后的实体转换回VO
            TModelLibInfoVo createdModelLibInfoVo = modelLibInfoService.create(modelLibInfoVo);
            return ReturnT.success(createdModelLibInfoVo);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("创建模型库信息记录失败: " + e.getMessage());
        }

    }

    /**
     * 获取所有模型库信息记录接口
     *
     * @return 返回一个 ResponseEntity 对象，包含所有模型库信息记录的列表，
     * 以及 HTTP 状态码 HttpStatus.OK（200，表示请求成功）。
     */
    @GetMapping
    public ReturnT<List<TModelLibInfoVo>> getModelLibList() {
        try {
            List<TModelLibInfoVo> modelLibInfos = modelLibInfoService.getModelLibInfoList();
            return ReturnT.success(modelLibInfos);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("获取模型库信息记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据 ID 获取模型库信息记录接口
     *
     * @param id 要获取的模型库信息记录的 ID，通过路径参数传递。
     * @return 如果找到对应的模型库信息记录，返回包含该记录的 ResponseEntity 对象，
     * 以及 HTTP 状态码 HttpStatus.OK（200）；
     * 如果未找到，返回一个空的 ResponseEntity 对象，
     * 以及 HTTP 状态码 HttpStatus.NOT_FOUND（404）。
     */
    @GetMapping("/{id}")
    public ReturnT<TModelLibInfoVo> getModelLibInfo(@PathVariable Long id) {
        try {
            TModelLibInfo modelLibInfo = modelLibInfoService.getModelLibInfo(id);
            TModelLibInfoVo modelLibInfoVo = modelLibInfoService.convertToVo(modelLibInfo);
            return ReturnT.success(modelLibInfoVo);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("根据ID获取模型库信息记录失败: " + e.getMessage());
        }

    }

    /**
     * 更新模型库-基本属性信息记录接口
     *
     * @param modelLibInfoVo 包含要更新的模型库信息的实体对象，通过请求体传递。
     * @return 如果更新成功，返回包含更新后的模型库信息记录的 ResponseEntity 对象，
     * 以及 HTTP 状态码 HttpStatus.OK（200）；
     * 如果未找到要更新的记录（更新失败），返回一个空的 ResponseEntity 对象，
     * 以及 HTTP 状态码 HttpStatus.NOT_FOUND（404）。
     */
    @PostMapping("/update")
    public ReturnT<TModelLibInfoVo> update(@RequestBody TModelLibUpdateInfoVo modelLibInfoVo) {
        try {
            // 更新实体（内部会调用convertToEntity方法）
            TModelLibInfoVo updatedVo = modelLibInfoService.updateById(modelLibInfoVo);
            if (updatedVo == null) {
                return ReturnT.fail("未找到ID为" + updatedVo.getId() + "的模型库信息记录");
            }
            return ReturnT.success(updatedVo);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnT.fail("更新模型库信息记录信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除模型库信息记录接口
     *
     * @param id 要删除的模型库信息记录的 ID，通过路径参数传递。
     * @return 如果删除成功，返回一个空的 ResponseEntity 对象，
     * 以及 HTTP 状态码 HttpStatus.NO_CONTENT（204，表示删除成功且无内容返回）；
     * 如果未找到要删除的记录（删除失败），返回一个空的 ResponseEntity 对象，
     * 以及 HTTP 状态码 HttpStatus.NOT_FOUND（404）。
     */
    @DeleteMapping("/delete/{id}")
    public ReturnT<Boolean> delete(@PathVariable Long id) {
        try {
            Boolean result = modelLibInfoService.delete(id);
            return ReturnT.success(result);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("删除模型库信息记录失败: " + e.getMessage());
        }

    }
}
