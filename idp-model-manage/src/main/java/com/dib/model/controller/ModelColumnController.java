package com.dib.model.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.model.domain.TModelColumn;
import com.dib.model.mapstruct.ModelColumnConvertor;
import com.dib.model.service.TModelBaseService;
import com.dib.model.service.TModelColumnService;
import com.dib.model.utils.SnowflakeUtils;
import com.dib.model.vo.BatchSaveColumnVo;
import com.dib.model.vo.TModelColumnVo;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/column")
public class ModelColumnController {

    @Autowired
    private TModelColumnService tModelColumnService;

    @Autowired
    private TModelBaseService modelBaseService;

    @PostMapping("/batchSaveColumnInfo")
    public AjaxResult batchSaveColumnInfo(@RequestBody @Valid BatchSaveColumnVo vo) {
        tModelColumnService.saveBatchColInfo(vo.getColumnVos());
        if(vo.isSyncToTable()){
            modelBaseService.tableModelStructSync(vo.getColumnVos().get(0).getModelNo().toString());
        }
        return AjaxResult.success();
    }

    @GetMapping("/getColumnByModelNo/{modelNo}")
    public AjaxResult getColumnByModelNo(@PathVariable("modelNo") Long modelNo) {
        List<TModelColumnVo> list = tModelColumnService.getColumnByModelNo(modelNo);
        return AjaxResult.success(list);
    }

    @PostMapping("/createColumn")
    public AjaxResult createColumn(@RequestBody TModelColumnVo columnVo) {
        TModelColumn column = ModelColumnConvertor.INSTANT.toEntity(columnVo);
        column.setId(SnowflakeUtils.nextId());
        tModelColumnService.save(column);
        return AjaxResult.success();
    }

    @PutMapping("/updateColumn")
    public AjaxResult updateColumn(@RequestBody TModelColumnVo columnVo) {
        TModelColumn column = ModelColumnConvertor.INSTANT.toEntity(columnVo);
        column.setId(columnVo.getId());
        tModelColumnService.updateById(column);
        return AjaxResult.success();
    }

    @DeleteMapping("/deleteColumn/{id}")
    public AjaxResult deleteColumn(@PathVariable("id") Long id) {
        tModelColumnService.delete(id);
        return AjaxResult.success();
    }
}
