package com.dib.model.controller;

import com.dib.common.core.domain.entity.ReturnT;
import com.dib.model.service.TModelPrimaryKeyService;
import com.dib.model.vo.TModelAddPkVo;
import com.dib.model.vo.TModelPkInfoVo;
import com.dib.model.vo.TModelQueryKeyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模型主键管理接口
 */
@RestController
@Slf4j
@RequestMapping("/modelPrimary")
public class ModelPrimaryKeyController {

    @Autowired
    private TModelPrimaryKeyService tModelPrimaryKeyService;


    /**
     * 新增主键信息
     *
     * @param tModelAddPkVo 主键信息VO
     * @return 创建的主键信息
     */
    @PostMapping("/createPk")
    public ReturnT<TModelAddPkVo> addPrimaryInfo(@RequestBody TModelAddPkVo tModelAddPkVo) {
        try {
            TModelAddPkVo modelPkVos = tModelPrimaryKeyService.createModelPkInfo(tModelAddPkVo);
            return ReturnT.success(modelPkVos);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("新增主键信息失败: " + e.getMessage());
        }
    }

    /**
     * 查询主键信息
     *
     * @param modelNo 查询条件VO
     * @return 主键信息列表
     */
    @GetMapping("/queryAllKey/{modelNo}")
    public ReturnT<List<TModelPkInfoVo>> queryAllKey(@PathVariable String modelNo) {
        try {
            List<TModelPkInfoVo> queryKeyVo = tModelPrimaryKeyService.queryModelPkInfoByModelNo(modelNo);
            return ReturnT.success(queryKeyVo);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("查询主键信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除主键信息
     * 
     * @param id 主键ID
     * @return 删除结果
     */
    @GetMapping("/deletePk/{id}")
    public ReturnT<Boolean> deletePrimaryInfo(@PathVariable Long id) {
        try {
            Boolean isDelete = tModelPrimaryKeyService.deleteModelPkInfo(id);
            return ReturnT.success(isDelete);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("删除主键信息失败: " + e.getMessage());
        }
    }
}