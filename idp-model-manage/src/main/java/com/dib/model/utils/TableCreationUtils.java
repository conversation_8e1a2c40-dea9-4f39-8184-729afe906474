package com.dib.model.utils;

import com.dib.common.database.constants.DbType;
import com.dib.metadata.dto.MetadataColumnMarketDto;
import com.dib.metadata.service.MetadataCreateTableService;
import com.dib.metadata.service.MetadataSourceService;
import com.dib.model.domain.TModelBase;
import com.dib.metadata.dto.MetadataCreateSqlDto;
import com.dib.metadata.entity.MetadataSourceEntity;
import com.dib.metadata.enums.SqlTypeEnum;

import com.dib.model.enums.CreateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class TableCreationUtils {

    private final MetadataSourceService metadataSourceService;
    private final MetadataCreateTableService metadataCreateTableService;
    public TableCreationUtils(MetadataSourceService metadataSourceService,
                              MetadataCreateTableService metadataCreateTableService) {
        this.metadataSourceService = metadataSourceService;
        this.metadataCreateTableService = metadataCreateTableService;
    }

    private static String DORIS_SOURCE_ID = null;

    private static MetadataSourceEntity DORIS_SOURCE = null;


    public String getDorisSourceId(){
        if(null == DORIS_SOURCE_ID){
            MetadataSourceEntity dorisSource = metadataSourceService.lambdaQuery().eq(MetadataSourceEntity::getDbType, DbType.DORIS.getDb()).last("limit 1").one();
            if(null == dorisSource){
                throw new ModelException("未找到doris数据源");
            }
            DORIS_SOURCE_ID = dorisSource.getId();
            DORIS_SOURCE = dorisSource;
        }
        return DORIS_SOURCE_ID;
    }

    public MetadataSourceEntity getDorisSource(){
        if(null == DORIS_SOURCE_ID){
            MetadataSourceEntity dorisSource = metadataSourceService.lambdaQuery().eq(MetadataSourceEntity::getDbType, DbType.DORIS.getDb()).last("limit 1").one();
            if(null == dorisSource){
                throw new ModelException("未找到doris数据源");
            }
            DORIS_SOURCE_ID = dorisSource.getId();
            DORIS_SOURCE = dorisSource;
        }
        return DORIS_SOURCE;
    }
    /**
     * 创建表并返回表ID
     *
     * @param modelBase 模型基础信息
     * @param metadataColumnEntities 元数据列信息
     * @return 表ID，如果是SQL创建类型则返回null
     */
    public String createTableAndGetId(TModelBase modelBase, List<MetadataColumnMarketDto> metadataColumnEntities) {
        if (CreateTypeEnum.CREATE_TYPE_SQL.getCode().equals(modelBase.getCreateType())) {
            return null;
        }

        MetadataCreateSqlDto metadataCreateSqlDto = new MetadataCreateSqlDto();
        metadataCreateSqlDto.setTargetTableName(modelBase.getTName());
        
        MetadataSourceEntity metadataSourceEntity = getDorisSource();
        
        metadataCreateSqlDto.setTargetId(metadataSourceEntity.getId());
        metadataCreateSqlDto.setTargetSchemaName(metadataSourceEntity.getDbSchema().getSid());
        metadataCreateSqlDto.setMetadataColumnMarketDtoList(metadataColumnEntities);
        metadataCreateSqlDto.setSourceTableRemarks(modelBase.getRemark());
        metadataCreateSqlDto.setSqlType(SqlTypeEnum.CREATE_TABLE.getCode());
        
        String sqlCreatTable = metadataCreateTableService.createSql(metadataCreateSqlDto);
        metadataCreateSqlDto.setTableSql(sqlCreatTable);
        log.debug("sqlCreateTable: {}", sqlCreatTable);
        
        return metadataCreateTableService.runSql(metadataCreateSqlDto);
    }
} 