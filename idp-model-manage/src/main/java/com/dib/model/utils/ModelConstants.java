package com.dib.model.utils;

/**
 * 模型管理业务常量类
 */
public class ModelConstants {
    
    /**
     * 默认分页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 20;
    
    /**
     * 最大分页大小
     */
    public static final int MAX_PAGE_SIZE = 1000;
    
    /**
     * 有效状态
     */
    public static final int ENABLE_STATUS = 0;
    
    /**
     * 无效状态
     */
    public static final int DISABLE_STATUS = 1;
    
    /**
     * 锁定状态
     */
    public static final String LOCK_STATUS = "1";
    
    /**
     * 解锁状态
     */
    public static final String UNLOCK_STATUS = "0";
    
    /**
     * SQL查询超时时间（秒）
     */
    public static final int SQL_TIMEOUT = 30;

    /**
     * 克隆名字后缀
     */
    public static final String CLONE_NAME_SUFFIX = "_副本";

    /**
     * 克隆代号前缀
     */
    public static final String CLONE_CODE_PREFIX = "COPY_OF_";
}