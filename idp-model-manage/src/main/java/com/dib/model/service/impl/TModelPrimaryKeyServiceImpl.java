package com.dib.model.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.model.domain.TModelFkInfo;
import com.dib.model.domain.TModelPkInfo;
import com.dib.model.mapper.TModelPkInfoMapper;
import com.dib.model.mapstruct.ModelPrimaryKeyConvertor;
import com.dib.model.service.TModelForeignKeyService;
import com.dib.model.service.TModelPrimaryKeyService;
import com.dib.model.utils.SnowflakeUtils;
import com.dib.model.vo.TModelAddPkVo;
import com.dib.model.vo.TModelPkInfoVo;
import com.dib.model.vo.TModelQueryKeyVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模型主键服务实现类
 */
@Service
public class TModelPrimaryKeyServiceImpl extends ServiceImpl<TModelPkInfoMapper, TModelPkInfo> implements TModelPrimaryKeyService {

    @Override
    public TModelAddPkVo createModelPkInfo(TModelAddPkVo tModelAddPkVo) {
        // 将VO转换为实体
        TModelPkInfo tModelPkInfo = ModelPrimaryKeyConvertor.INSTANT.toEntity(tModelAddPkVo);
        tModelPkInfo.setId(SnowflakeUtils.nextId());
        // 保存实体
        baseMapper.insert(tModelPkInfo);
        return tModelAddPkVo;
    }


    @Override
    public Boolean deleteModelPkInfo(Long id) {
        return removeById(id); // 返回true表示删除成功，false表示失败
    }

    @Override
    public List<TModelPkInfoVo> queryModelPkInfoByModelNo(String modelNo) {
        QueryWrapper<TModelPkInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("model_no", modelNo);
        List<TModelPkInfo> tModelPkInfoList = baseMapper.selectList(queryWrapper);
        List<TModelPkInfoVo> tModelPkInfoVoList = ModelPrimaryKeyConvertor.INSTANT.toVO(tModelPkInfoList);
        return tModelPkInfoVoList;
    }

}