package com.dib.model.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.model.domain.TModelMappingTable;
import com.dib.model.mapper.TModelMappingTableMapper;
import com.dib.model.mapstruct.ModelMappingTableConvertor;
import com.dib.model.service.TModelMappingTableService;
import com.dib.model.utils.SnowflakeUtils;
import com.dib.model.vo.TModelMappingTableVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 模型数据映射表的 Service 实现类，实现 Service 接口中定义的业务逻辑方法
 */
@Service
public class TModelMappingTableServiceImpl extends ServiceImpl<TModelMappingTableMapper, TModelMappingTable> implements TModelMappingTableService {

    private static final int BATCH_SIZE = 500;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TModelMappingTableVo create(TModelMappingTableVo modelMappingTable) {
        TModelMappingTable entity = convertToEntity(modelMappingTable);
        entity.setId(SnowflakeUtils.nextId());
        save(entity);
        modelMappingTable.setId(entity.getId());
        return modelMappingTable;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<TModelMappingTableVo> batchCreate(List<TModelMappingTableVo> modelMappingTables) {
        if (CollectionUtils.isEmpty(modelMappingTables)) {
            return new ArrayList<>();
        }

        List<TModelMappingTable> entities = convertToEntity(modelMappingTables);
        entities.forEach(o -> o.setId(SnowflakeUtils.nextId()));
        saveBatch(entities, BATCH_SIZE);

        // 更新ID回VO
        for (int i = 0; i < modelMappingTables.size(); i++) {
            modelMappingTables.get(i).setId(entities.get(i).getId());
        }

        return modelMappingTables;
    }

    @Override
    public List<TModelMappingTable> getModelMappingTable(Long modelLibNo) {
        return this.lambdaQuery()
            .eq(TModelMappingTable::getModelLibNo, modelLibNo)
            .list();
    }
    
    @Override
    public List<TModelMappingTable> getModelMappingTable(String modelLibNo) {
        return this.lambdaQuery()
            .eq(TModelMappingTable::getModelLibNo, modelLibNo)
            .list();
    }
    
    @Override
    public List<TModelMappingTableVo> getModelMappingTableVoList(String modelLibNo) {
        List<TModelMappingTable> list = getModelMappingTable(modelLibNo);
        return convertToVo(list);
    }

    @Override
    public TModelMappingTable getModelMappingTableById(Long id) {
        return getById(id);
    }
    
    @Override
    public TModelMappingTableVo getModelMappingTableVoById(Long id) {
        TModelMappingTable entity = getModelMappingTableById(id);
        return convertToVo(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TModelMappingTable update(TModelMappingTable modelMappingTable) {
        updateById(modelMappingTable);
        return modelMappingTable;
    }
    
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<TModelMappingTable> batchUpdate(List<TModelMappingTable> modelMappingTables) {
        if (CollectionUtils.isEmpty(modelMappingTables)) {
            return new ArrayList<>();
        }
        updateBatchById(modelMappingTables, BATCH_SIZE);
        return modelMappingTables;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TModelMappingTableVo updateAndReturnVo(TModelMappingTableVo modelMappingTableVo) {
        TModelMappingTable entity = convertToEntity(modelMappingTableVo);
        TModelMappingTable updatedEntity = update(entity);
        return convertToVo(updatedEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        return removeById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteBatch(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        return removeByIds(ids);
    }
    
    @Override
    public TModelMappingTableVo convertToVo(TModelMappingTable entity) {
        if (entity == null) {
            return null;
        }
        return ModelMappingTableConvertor.INSTANT.toVO(entity);
    }
    
    @Override
    public List<TModelMappingTableVo> convertToVo(List<TModelMappingTable> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        return ModelMappingTableConvertor.INSTANT.toVO(entityList);
    }
    
    @Override
    public TModelMappingTable convertToEntity(TModelMappingTableVo vo) {
        if (vo == null) {
            return null;
        }
        return ModelMappingTableConvertor.INSTANT.toEntity(vo);
    }
    
    @Override
    public List<TModelMappingTable> convertToEntity(List<TModelMappingTableVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return new ArrayList<>();
        }
        return ModelMappingTableConvertor.INSTANT.toEntity(voList);
    }
}
