package com.dib.model.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.model.domain.TModelColumn;
import com.dib.model.mapper.TModelColumnMapper;
import com.dib.model.mapstruct.ModelColumnConvertor;
import com.dib.model.service.TModelColumnService;
import com.dib.model.utils.SnowflakeUtils;
import com.dib.model.vo.TModelColumnVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TModelColumnServiceImpl extends ServiceImpl<TModelColumnMapper, TModelColumn> implements TModelColumnService {

    @Override
    public List<TModelColumnVo> getColumnByModelNo(Long modelNo) {
        List<TModelColumn> tModelColumnList = baseMapper.selectByModelNo(modelNo);
        return ModelColumnConvertor.INSTANT.toVO(tModelColumnList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteColumnByModelNo(Long modelNo) {
        return baseMapper.deleteByModelNo(modelNo) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TModelColumnVo saveColumnFromVo(TModelColumnVo columnVo) {
        if (columnVo == null) {
            return null;
        }
        TModelColumn entity = ModelColumnConvertor.INSTANT.toEntity(columnVo);
        this.save(entity);
        columnVo.setId(entity.getId());
        return columnVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean delete(Long id) {
        return removeById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveBatchColInfo(List<TModelColumnVo> columnVo) {
        if (CollectionUtils.isNotEmpty(columnVo)) {
            //批量删除模型列信息
            deleteColumnByModelNo(columnVo.get(0).getModelNo().longValue());
            List<TModelColumn> tModelColumnList = ModelColumnConvertor.INSTANT.toEntityList(columnVo);
            tModelColumnList.forEach(o -> o.setId(SnowflakeUtils.nextId()));
            saveBatch(tModelColumnList);
            return true;
        }
        return false;
    }

}
