package com.dib.model.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dib.model.domain.TModelPkInfo;
import com.dib.model.vo.TModelAddPkVo;
import com.dib.model.vo.TModelPkInfoVo;
import com.dib.model.vo.TModelQueryKeyVo;

import java.util.List;

/**
 * 模型主键服务接口
 */
public interface TModelPrimaryKeyService extends IService<TModelPkInfo> {

    /**
     * 创建模型主键信息
     * @param tModelAddPkVo 主键信息VO
     * @return 创建后的主键信息VO
     */
    TModelAddPkVo createModelPkInfo(TModelAddPkVo tModelAddPkVo);

    /**
     * 删除模型主键信息
     * @param id 主键ID
     * @return 删除结果
     */
    Boolean deleteModelPkInfo(Long id);

    List<TModelPkInfoVo> queryModelPkInfoByModelNo(String id);

}