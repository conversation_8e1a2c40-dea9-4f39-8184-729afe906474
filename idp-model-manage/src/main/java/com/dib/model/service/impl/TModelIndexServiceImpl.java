package com.dib.model.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.model.domain.TModelIndex;
import com.dib.model.mapper.TModelIndexMapper;
import com.dib.model.mapstruct.ModelIndexConvertor;
import com.dib.model.service.TModelIndexService;
import com.dib.model.utils.SnowflakeUtils;
import com.dib.model.vo.TModelIndexVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TModelIndexServiceImpl extends ServiceImpl<TModelIndexMapper, TModelIndex> implements TModelIndexService {

    @Override
    public List<TModelIndexVo> getIndexByModelNo(Long modelNo) {
        List<TModelIndex> tModelIndexList = baseMapper.selectByModelNo(modelNo);
        return ModelIndexConvertor.INSTANT.toVO(tModelIndexList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TModelIndex saveIndexFromVo(TModelIndexVo indexVo) {
        TModelIndex index = ModelIndexConvertor.INSTANT.toEntity(indexVo);
        index.setId(SnowflakeUtils.nextId());
        save(index);
        return index;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean delete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }


}
