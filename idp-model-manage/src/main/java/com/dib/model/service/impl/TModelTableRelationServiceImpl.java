package com.dib.model.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.common.utils.bean.BeanUtils;
import com.dib.model.domain.TModelBase;
import com.dib.model.domain.TModelRelationInfo;
import com.dib.model.domain.TModelTableRelation;
import com.dib.model.mapper.TModelRelationInfoMapper;
import com.dib.model.mapper.TModelTableRelationMapper;
import com.dib.model.mapstruct.ModelTableRelationConvertor;
import com.dib.model.service.TModelBaseService;
import com.dib.model.service.TModelRelationInfoService;
import com.dib.model.service.TModelTableRelationService;
import com.dib.model.utils.SnowflakeUtils;
import com.dib.model.vo.TModelTableRelationVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class TModelTableRelationServiceImpl extends ServiceImpl<TModelTableRelationMapper, TModelTableRelation> implements TModelTableRelationService {
    
    private static final int BATCH_SIZE = 500;
    
    @Autowired
    private TModelRelationInfoService tModelRelationInfoService;

    @Autowired
    private TModelBaseService modelBaseService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TModelTableRelationVo create(TModelTableRelationVo relation) {
        // 1. 保存主表数据
        TModelTableRelation tModelTableRelation = ModelTableRelationConvertor.INSTANT.toEntity(relation);
        tModelTableRelation.setId(SnowflakeUtils.nextId());
        save(tModelTableRelation);
        relation.setId(tModelTableRelation.getId());

        // 2. 处理关联模型
        List<String> ids = relation.getSelectedModelIds();
        if(CollectionUtils.isNotEmpty(ids)) {
            // 2.1 验证模型是否存在且来自同一数据源
            validateModels(ids);
            
            // 2.2 批量保存关联信息
            List<TModelRelationInfo> relationInfos = ids.stream()
                .map(id -> {
                    TModelRelationInfo info = new TModelRelationInfo();
                    info.setId(SnowflakeUtils.nextId());
                    info.setModelRelationId(tModelTableRelation.getId());
                    info.setModelId(Long.parseLong(id));
                    return info;
                })
                .collect(Collectors.toList());

            tModelRelationInfoService.saveBatch(relationInfos, BATCH_SIZE);
        }
        
        return relation;
    }

    private void validateModels(List<String> ids) {
        List<TModelBase> modelBaseList = modelBaseService.lambdaQuery()
            .in(TModelBase::getId, ids)
            .list();
            
        if(CollectionUtils.isEmpty(modelBaseList)) {
            throw new RuntimeException("表不存在！");
        }
        
        Set<String> sourceIds = modelBaseList.stream()
            .map(TModelBase::getSourceId)
            .collect(Collectors.toSet());
            
        if(CollectionUtils.isEmpty(sourceIds) || sourceIds.size() > 1) {
            throw new RuntimeException("选择的模型来自于不同的数据源无法进行数据连接！");
        }
    }

    @Override
    public List<TModelTableRelation> getAll() {
        return list();
    }

    @Override
    public TModelTableRelation getModelTableRelationById(Long id) {
        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(TModelTableRelationVo relation) {
        // 1. 更新主表
        TModelTableRelation relations = ModelTableRelationConvertor.INSTANT.toEntity(relation);
        boolean updated = updateById(relations);
        if (!updated) {
            return false;
        }

        // 2. 更新关联信息
        List<String> selectedModelIds = relation.getSelectedModelIds();
        if (CollectionUtils.isNotEmpty(selectedModelIds)) {
            // 2.1 删除原有关联
            tModelRelationInfoService.remove(
                new QueryWrapper<TModelRelationInfo>()
                    .lambda()
                    .eq(TModelRelationInfo::getModelRelationId, relations.getId())
            );
            
            // 2.2 批量插入新关联
            List<TModelRelationInfo> relationInfos = selectedModelIds.stream()
                .map(id -> {
                    TModelRelationInfo info = new TModelRelationInfo();
                    info.setModelId(Long.parseLong(id));
                    info.setModelRelationId(relations.getId());
                    return info;
                })
                .collect(Collectors.toList());
                
            return tModelRelationInfoService.saveBatch(relationInfos, BATCH_SIZE);
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatchRela(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        
        // 1. 删除关联信息
        tModelRelationInfoService.remove(
            new QueryWrapper<TModelRelationInfo>()
                .lambda()
                .in(TModelRelationInfo::getModelRelationId, ids)
        );
        
        // 2. 删除主表数据
        return removeByIds(ids);
    }

    @Override
    public List<TModelTableRelationVo> getModelTableRelaList(Long modelLibNo) {
        // 1. 获取主表数据
        List<TModelTableRelation> list = this.lambdaQuery()
            .eq(TModelTableRelation::getModelLibNo, modelLibNo)
            .list();
            
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        // 2. 获取所有关联ID
        List<Long> relationIds = list.stream()
            .map(TModelTableRelation::getId)
            .collect(Collectors.toList());
            
        // 3. 批量查询关联信息
        Map<Long, List<String>> relationModelMap = tModelRelationInfoService.list(
            new QueryWrapper<TModelRelationInfo>()
                .lambda()
                .in(TModelRelationInfo::getModelRelationId, relationIds)
        ).stream().collect(
            Collectors.groupingBy(
                TModelRelationInfo::getModelRelationId,
                Collectors.mapping(
                    info -> Objects.toString(info.getModelId()),
                    Collectors.toList()
                )
            )
        );

        // 4. 组装VO
        return list.stream()
            .map(relation -> {
                TModelTableRelationVo vo = new TModelTableRelationVo();
                BeanUtils.copyBeanProp(vo, relation);
                vo.setSelectedModelIds(relationModelMap.getOrDefault(relation.getId(), new ArrayList<>()));
                return vo;
            })
            .collect(Collectors.toList());
    }
    
    @Override
    public TModelTableRelationVo getModelTableRelationVoById(Long id) {
        TModelTableRelation relation = getModelTableRelationById(id);
        if (relation == null) {
            return null;
        }
        
        TModelTableRelationVo vo = ModelTableRelationConvertor.INSTANT.toVO(relation);
        
        // 获取关联信息
        List<TModelRelationInfo> relationInfos = tModelRelationInfoService.list(
            new QueryWrapper<TModelRelationInfo>()
                .lambda()
                .eq(TModelRelationInfo::getModelRelationId, id)
        );
        
        if (CollectionUtils.isNotEmpty(relationInfos)) {
            List<String> modelIds = relationInfos.stream()
                .map(item -> Objects.toString(item.getModelId()))
                .collect(Collectors.toList());
            vo.setSelectedModelIds(modelIds);
        }
        
        return vo;
    }
}
