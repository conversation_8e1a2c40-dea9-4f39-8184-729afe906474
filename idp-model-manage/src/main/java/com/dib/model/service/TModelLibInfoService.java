package com.dib.model.service;

import com.dib.model.domain.TModelLibInfo;
import com.dib.model.vo.TModelLibInfoVo;
import com.dib.model.vo.TModelLibUpdateInfoVo;
import com.dib.model.vo.TreeNode;

import java.util.List;

public interface TModelLibInfoService {

    List<TreeNode> buildNavigationTree();

    TModelLibInfoVo create(TModelLibInfoVo modelLibInfoVo);

    List<TModelLibInfo> getAll();

    TModelLibInfo getModelLibInfo(Long id);

    TModelLibInfo update(TModelLibInfoVo modelLibInfo);

    boolean delete(Long id);

    List<TModelLibInfoVo> getModelLibInfoList();

    TModelLibInfoVo updateById(TModelLibUpdateInfoVo modelLibInfoVo);
    
    /**
     * 将实体转换为VO
     * @param entity 实体对象
     * @return VO对象
     */
    TModelLibInfoVo convertToVo(TModelLibInfo entity);
    
    /**
     * 将实体列表转换为VO列表
     * @param entityList 实体对象列表
     * @return VO对象列表
     */
    List<TModelLibInfoVo> convertToVo(List<TModelLibInfo> entityList);
    
    /**
     * 将VO对象转换为实体
     * @param vo VO对象
     * @return 实体对象
     */
    TModelLibInfo convertToEntity(TModelLibInfoVo vo);
    
    /**
     * 将VO对象列表转换为实体列表
     * @param voList VO对象列表
     * @return 实体对象列表
     */
    List<TModelLibInfo> convertToEntity(List<TModelLibInfoVo> voList);
    
    /**
     * 将更新VO对象转换为实体
     * @param vo 更新VO对象
     * @return 实体对象
     */
    TModelLibInfo convertToEntity(TModelLibUpdateInfoVo vo);
}
