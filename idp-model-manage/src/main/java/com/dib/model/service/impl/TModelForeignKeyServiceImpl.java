package com.dib.model.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.model.domain.TModelFkInfo;
import com.dib.model.mapper.TModelFkInfoMapper;
import com.dib.model.mapstruct.ModelForeignKeyConvertor;
import com.dib.model.service.TModelForeignKeyService;
import com.dib.model.utils.SnowflakeUtils;
import com.dib.model.vo.TModelAddFkVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 模型外键服务实现类
 */
@Service
public class TModelForeignKeyServiceImpl extends ServiceImpl<TModelFkInfoMapper, TModelFkInfo> implements TModelForeignKeyService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TModelAddFkVo createModelFkInfo(TModelAddFkVo tModelAddFkVo) {
        // 将VO转换为实体
        TModelFkInfo tModelFkInfo = ModelForeignKeyConvertor.INSTANT.toEntity(tModelAddFkVo);
        tModelFkInfo.setId(SnowflakeUtils.nextId());
        // 保存实体
        save(tModelFkInfo);
        tModelAddFkVo.setId(tModelFkInfo.getId());
        return tModelAddFkVo;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteBatchModelFkInfo(List<Long> ids) {
        int result = baseMapper.deleteBatchIds(ids);
        return result > 0; // 返回true表示删除成功，false表示失败
    }

    @Override
    public List<TModelFkInfo> getModelFkInfoByModelNo(Long modelNo) {
        return this.lambdaQuery().eq(TModelFkInfo::getModelNo, modelNo).list();
    }
}