package com.dib.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("t_model_base")
@Data
public class TModelBase extends BaseComEntity {

    /**
     * 编号，作为该表的主键，用于唯一标识每
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 模型名称，用于标识模型的名称，通常为模型的标题
     */
    @TableField("model_name")
    private String modelName;

    /**
     * 标题，用于描述模型的标题信息
     */
    @TableField("title")
    private String title;

    /**
     * 资源 ID，用于关联与该模型相关的资源，通常为唯一标识资源的字符串
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 模型 ID，用于唯一标识该模型，在系统中对模型进行区分和管理
     */
    @TableField("model_uuid")
    private String modelUuid;

    /**
     * 代号，模型的一个特定编码，用于简洁地表示模型
     */
    @TableField("code_no")
    private String codeNo;

    /**
     * 类型，用于标识模型的类型，方便对不同类型的模型进行分类管理 类型 3=模型分组4=物理模型
     */
    @TableField("model_type")
    private Integer modelType;

    /**
     * 数据库表，记录该模型所关联的数据库表名称
     */
    @TableField("t_name")
    private String tName;

    /**
     * 扩展属性，用于存储与模型相关的扩展信息，可根据业务需求进行自定义
     */
    @TableField("extend_property")
    private String extendProperty;

    /**
     * 表ID，用于标识模型所关联的数据库表，以方便后续操作
     */
    @TableField("table_id")
    private String tableId;

    /**
     * 备注，用于记录模型的备注信息，以方便后续维护和说明
     */
    @TableField("remark")
    private String remark;

    /**
     * 所属模型库或者模型分组
     */
    @TableField("model_lib_no")
    private Long modelLibNo;

    /**
     * 字段排序序号
     */
    @TableField("field_sort_num")
    private Integer fieldSortNum;

    /**
     * 是否锁定
     */
    @TableField("is_lock")
    private Integer isLock;

    /**
     * 创建方式 1-基于集市创建 2-基于指标创建 3-基于数据库表创建 4-基于sql创建
     */
    @TableField("create_type")
    private Integer createType;

    /**
     * 基于sql创建时的sql
     */
    @TableField("model_sql")
    private String modelSql;

    @TableField("copy_count")
    private Integer copyCount;



    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }


    public void setSourceId(String sourceId) {
        this.sourceId = sourceId == null ? null : sourceId.trim();
    }

    public void setModelUuid(String modelUuid) {
        this.modelUuid = modelUuid == null ? null : modelUuid.trim();
    }


    public void setCodeNo(String codeNo) {
        this.codeNo = codeNo == null ? null : codeNo.trim();
    }

    public void settName(String tName) {
        this.tName = tName == null ? null : tName.trim();
    }


    public void setExtendProperty(String extendProperty) {
        this.extendProperty = extendProperty == null ? null : extendProperty.trim();
    }

}