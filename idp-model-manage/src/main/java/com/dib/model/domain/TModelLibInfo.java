package com.dib.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**表示模型库信息**/
@TableName("t_model_lib_info")
@Data
public class TModelLibInfo extends BaseComEntity {

    /**
     * 编号，作为该表的主键，用于唯一标识每条模型库信息记录，通过序列自动生成
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 父编号，用于表示模型库之间的层级关系
     */
    @TableField("pid")
    private Long pid;

    /**
     * 节点类型，取值具有特定含义：
     * 0 代表模型管理；
     * 1 代表模型库；
     * 2 代表模型分组
     */
    @TableField("model_type")
    private Integer modelType;

    /**
     * 模型库标题，用于描述模型库的名称，在数据库表中该字段不允许为空
     */
    @TableField("model_lib_name")
    private String modelLibName;

    /**
     * 资源 ID，用于关联与该模型库相关的资源
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 数据存储连接池 ID，用于指定该模型库所使用的数据存储连接池
     */
    @TableField("database_pool_id")
    private Integer databasePoolId;

    /**
     * 描述信息，用于对模型库进行简要说明
     */
    @TableField("mark")
    private String mark;

    @TableField("data_base_id")
    private Long dataBaseId;

    // 使用Lombok的@Data注解自动生成getter和setter方法

}


