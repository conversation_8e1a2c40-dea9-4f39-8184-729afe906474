package com.dib.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 模型数据外鍵实体类，对应数据库中的 t_model_fk_info 表
 */
@TableName("t_model_fk_info")
@Data
public class TModelFkInfo extends BaseComEntity{
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 外键名称，不能为空
     */
    @TableField("f_name")
    private String fName;

    /**
     * 外键列名
     */
    @TableField("f_col")
    private String fCol;

    /**
     * 关联表名
     */
    @TableField("relation_table")
    private String relationTable;

    /**
     * 关联列名
     */
    @TableField("relation_col")
    private String relationCol;

    /**
     * 模型编号
     */
    @TableField("model_no")
    private Long modelNo;
    // 使用Lombok的@Data注解自动生成getter和setter方法
    // 注意：原setter方法中包含字符串trim逻辑，使用Lombok后需要注意这一点

}