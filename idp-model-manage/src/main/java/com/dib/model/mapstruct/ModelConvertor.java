package com.dib.model.mapstruct;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.dib.model.domain.TModelBase;
import com.dib.model.utils.SnowflakeUtils;
import com.dib.model.vo.TModelBaseAddVo;
import com.dib.model.vo.TModelBaseInfoVo;
import com.dib.model.vo.TModelBaseVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 模型实体映射接口
 * 用于TModelBase实体与DTO/VO对象之间的转换
 */
@Mapper(componentModel = "spring")
public interface ModelConvertor {

    ModelConvertor INSTANT = Mappers.getMapper(ModelConvertor.class);

    /**
     * 将实体转换为VO对象
     * @param entity 实体对象
     * @return VO对象
     */
    TModelBaseVo toVO(TModelBase entity);
    
    /**
     * 将实体列表转换为VO对象列表
     * @param entityList 实体对象列表
     * @return VO对象列表
     */
    List<TModelBaseVo> toVO(List<TModelBase> entityList);

    /**
     * 将DTO对象转换为实体
     * @param dto DTO对象
     * @return 实体对象
     */
    @Mappings({
        @Mapping(target = "tableId", source = "tableIds", qualifiedByName = "tableIdsToTableId"),
    })
    TModelBase toEntity(TModelBaseAddVo dto);
    
    /**
     * 将DTO对象列表转换为实体列表
     * @param dtoList DTO对象列表
     * @return 实体列表
     */
    List<TModelBase> toEntity(List<TModelBaseAddVo> dtoList);

    /**
     * 将TModelBaseVo对象转换为实体
     * 用于导入模型列表时的转换
     * @param vo TModelBaseVo对象
     * @return 实体对象
     */
    TModelBase fromVO(TModelBaseVo vo);
    
    /**
     * 将TModelBaseVo列表转换为实体列表
     * @param voList TModelBaseVo对象列表
     * @return 实体对象列表
     */
    List<TModelBase> fromVO(List<TModelBaseVo> voList);
    
    /**
     * 将tableIds列表转换为逗号分隔的字符串
     */
    @Named("tableIdsToTableId")
    default String tableIdsToTableId(List<String> tableIds) {
        if (CollectionUtils.isNotEmpty(tableIds)) {
            return tableIds.stream().collect(Collectors.joining(","));
        }
        return null;
    }
    
    /**
     * 将实体转换为TModelBaseInfoVo对象
     * @param entity 实体对象
     * @return TModelBaseInfoVo对象
     */
    TModelBaseInfoVo toInfoVO(TModelBase entity);
    
    /**
     * 将TModelBaseInfoVo对象转换为实体
     * @param vo TModelBaseInfoVo对象
     * @return 实体对象
     */
    TModelBase fromInfoVO(TModelBaseInfoVo vo);
    
    /**
     * 将TModelBaseInfoVo列表转换为实体列表
     * @param voList TModelBaseInfoVo对象列表
     * @return 实体对象列表
     */
    List<TModelBase> fromInfoVO(List<TModelBaseInfoVo> voList);
    
    /**
     * 将实体列表转换为TModelBaseInfoVo列表
     * @param entityList 实体对象列表
     * @return TModelBaseInfoVo对象列表
     */
    List<TModelBaseInfoVo> toInfoVO(List<TModelBase> entityList);
}