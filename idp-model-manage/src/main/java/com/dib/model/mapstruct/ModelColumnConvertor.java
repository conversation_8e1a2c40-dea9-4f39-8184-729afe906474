package com.dib.model.mapstruct;

import com.dib.model.domain.TModelColumn;
import com.dib.model.vo.TModelColumnVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 模型列实体映射接口
 * 用于TModelColumn实体与VO对象之间的转换
 */
@Mapper(componentModel = "spring")
public interface ModelColumnConvertor {

    ModelColumnConvertor INSTANT = Mappers.getMapper(ModelColumnConvertor.class);

    /**
     * 将实体转换为VO对象
     * @param entity 实体对象
     * @return VO对象
     */
    TModelColumnVo toVO(TModelColumn entity);

    /**
     * 将实体列表转换为VO对象列表
     * @param entityList 实体对象列表
     * @return VO对象列表
     */
    List<TModelColumnVo> toVO(List<TModelColumn> entityList);

    /**
     * 将VO对象转换为实体
     * @param vo VO对象
     * @return 实体对象
     */
    TModelColumn toEntity(TModelColumnVo vo);

    /**
     * 将VO对象列表转换为实体列表
     * @param voList VO对象列表
     * @return 实体列表
     */
    List<TModelColumn> toEntityList(List<TModelColumnVo> voList);

    /**
     * 将VO对象列表转换为实体列表
     * @param voList VO对象列表
     * @return 实体列表
     */
    List<TModelColumn> toEntity(List<TModelColumnVo> voList);
}