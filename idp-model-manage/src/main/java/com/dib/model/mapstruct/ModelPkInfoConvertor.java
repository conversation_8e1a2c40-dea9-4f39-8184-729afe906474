package com.dib.model.mapstruct;

import com.dib.model.domain.TModelPkInfo;
import com.dib.model.vo.TModelPkInfoVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 模型主键信息实体映射接口
 * 用于TModelPkInfo实体与VO对象之间的转换
 */
@Mapper(componentModel = "spring")
public interface ModelPkInfoConvertor {

    ModelPkInfoConvertor INSTANT = Mappers.getMapper(ModelPkInfoConvertor.class);

    /**
     * 将实体转换为VO对象
     * @param entity 实体对象
     * @return VO对象
     */
    TModelPkInfoVo toVO(TModelPkInfo entity);

    /**
     * 将实体列表转换为VO对象列表
     * @param entityList 实体对象列表
     * @return VO对象列表
     */
    List<TModelPkInfoVo> toVO(List<TModelPkInfo> entityList);

    /**
     * 将VO对象转换为实体
     * @param vo VO对象
     * @return 实体对象
     */
    TModelPkInfo toEntity(TModelPkInfoVo vo);

    /**
     * 将VO对象列表转换为实体列表
     * @param voList VO对象列表
     * @return 实体列表
     */
    List<TModelPkInfo> toEntity(List<TModelPkInfoVo> voList);
}