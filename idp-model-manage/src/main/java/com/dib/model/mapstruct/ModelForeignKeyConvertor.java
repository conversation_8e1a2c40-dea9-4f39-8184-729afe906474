package com.dib.model.mapstruct;

import com.dib.model.domain.TModelFkInfo;
import com.dib.model.vo.TModelAddFkVo;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: lengdongxue
 * @Date: 2025/6/1 11:49
 * @Description:
 **/
@Mapper
public interface ModelForeignKeyConvertor {

    ModelForeignKeyConvertor INSTANT = Mappers.getMapper(ModelForeignKeyConvertor.class);


    TModelFkInfo toEntity(TModelAddFkVo vo);
}
