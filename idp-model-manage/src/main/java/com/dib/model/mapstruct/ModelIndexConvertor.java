package com.dib.model.mapstruct;

import com.dib.model.domain.TModelIndex;
import com.dib.model.vo.TModelIndexVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 模型索引实体映射接口
 * 用于TModelIndex实体与TModelIndexVo之间的转换
 */
@Mapper(componentModel = "spring")
public interface ModelIndexConvertor {

    ModelIndexConvertor INSTANT = Mappers.getMapper(ModelIndexConvertor.class);

    /**
     * 将实体转换为VO对象
     * @param entity 实体对象
     * @return VO对象
     */
    TModelIndexVo toVO(TModelIndex entity);

    /**
     * 将实体列表转换为VO对象列表
     * @param entityList 实体对象列表
     * @return VO对象列表
     */
    List<TModelIndexVo> toVO(List<TModelIndex> entityList);

    /**
     * 将VO对象转换为实体
     * @param vo VO对象
     * @return 实体对象
     */
    TModelIndex toEntity(TModelIndexVo vo);

    /**
     * 将VO对象列表转换为实体列表
     * @param voList VO对象列表
     * @return 实体列表
     */
    List<TModelIndex> toEntity(List<TModelIndexVo> voList);
}