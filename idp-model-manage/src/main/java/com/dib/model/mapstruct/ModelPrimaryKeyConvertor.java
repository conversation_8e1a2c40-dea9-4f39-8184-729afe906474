package com.dib.model.mapstruct;

import com.dib.model.domain.TModelFkInfo;
import com.dib.model.domain.TModelPkInfo;
import com.dib.model.vo.TModelAddFkVo;
import com.dib.model.vo.TModelAddPkVo;
import com.dib.model.vo.TModelPkInfoVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: lengdongxue
 * @Date: 2025/6/1 11:49
 * @Description:
 **/
@Mapper
public interface ModelPrimaryKeyConvertor {

    ModelPrimaryKeyConvertor INSTANT = Mappers.getMapper(ModelPrimaryKeyConvertor.class);


    TModelPkInfo toEntity(TModelAddPkVo vo);

    TModelPkInfoVo toVO(TModelPkInfo entity);

    List<TModelPkInfoVo> toVO(List<TModelPkInfo> entityList);
}
