package com.dib.model.vo;

import com.dib.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class TModelBaseVo {

    /**
     * 编号
     */
    @Excel(name = "模型编号")
    private Long id;

    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 资源 ID
     */
    @Excel(name = "资源ID")
    private String sourceId;

    /**
     * 模型 ID
     */
    @Excel(name = "模型ID")
    private String modelUuid;

    /**
     * 代号
     */
    @Excel(name = "代号")
    private String codeNo;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private Integer modelType;

    /**
     * 数据库表
     */
    @Excel(name = "数据库表")
    @JsonProperty("tName")
    private String tName;

    /**
     * 扩展属性
     */
    @Excel(name = "扩展属性")
    private String extendProperty;

    /**
     * 扩展属性
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 所属模型库或者模型分组
     */
    @Excel(name = "所属模型库")
    private Long modelLibNo;

    /**
     * 最后修改时间
     */
    @Excel(name = "最后修改时间")
    private Date modifyDate;

    /**
     * 字段排序
     */
    private Integer fieldSortNum;

    /**
     * 是否锁表
     */
    private Integer isLock;

    private Integer createType;

}
