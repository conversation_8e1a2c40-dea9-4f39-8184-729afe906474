package com.dib.model.vo;

import com.dib.model.domain.TModelIndex;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TModelIndexVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 序号，对应实体类中的编号,创建更新都不用传
     */
    private Long id;
    /**
     * 名称
     */
    private String title;
    /**
     * 类型
     */
    @JsonProperty("tType")
    private String tType;
    /**
     * 列
     */
    @JsonProperty("tCol")
    private String tCol;

    // 使用Lombok的@Data注解自动生成getter和setter方法
    /**
     * 模型编号
     */
    private Long modelNo;




}
