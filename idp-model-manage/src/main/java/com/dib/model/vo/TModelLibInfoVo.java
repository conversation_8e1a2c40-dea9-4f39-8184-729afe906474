package com.dib.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TModelLibInfoVo {

    /**
     * 序号
     */
    private Long id;

    /**
     * 模型库标题
     */
    private String modelLibName;

    /**
     * 模型数
     */
    private Integer modelCount;

    /**
     * 资源 ID
     */
    private String sourceId;

    /**
     * 数据存储连接池 ID
     */
    private Integer databasePoolId;

    /**
     * 描述信息
     */
    private String mark;

    /**
     * 最后修改时间
     */
    private Date modifyDate;

    /**
     * 数据库 ID
     */
    private Long dataBaseId;

    private Integer modelType;

    private Integer pid;


}
