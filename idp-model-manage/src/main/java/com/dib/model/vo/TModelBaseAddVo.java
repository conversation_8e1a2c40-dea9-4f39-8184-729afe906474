package com.dib.model.vo;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.dib.common.utils.uuid.UUID;
import com.dib.model.domain.TModelBase;
import com.dib.model.utils.SnowflakeUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TModelBaseAddVo {

    //private Long indictorId;
    /**
     * 模型名称，用于标识模型的名称，通常为模型的标题
     */
    private String modelName;

    /**
     * 标题，用于描述模型的标题信息
     */
    private String title;

//    /**
//     * 资源 ID，用于关联与该模型相关的资源，通常为唯一标识资源的字符串
//     */
//    private String sourceId;

    /**
     * 数据库表，记录该模型所关联的数据库表名称
     */
    @JsonProperty("tName")
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_]*$", message = "表名必须以字母开头，只能包含字母、数字和下划线")
    private String tName;

    /**
     * 表ID，用于标识模型所关联的数据库表，以方便后续操作
     */
    private List<String> tableIds;

    /**
     * 备注，用于记录模型的备注信息，以方便后续维护和说明
     */
    private String remark;

    /**
     * 模型库编号，用于标识模型所属的模型库，通常为模型库的编号
     */
    private Long modelLibNo;

    /**
     * 模型列，用于记录模型的列信息，包括列名称、别名、标题、维度、关联维度等
     */
    @JsonProperty("tModelColumns")
    private List<TModelColumnVo> tModelColumns;

    /**
     * 缓存时间
     */
    private Long  cachTime;

    /**
     * 缓存类型 1:缓存，2:数据库
     */
    private Integer cachType;

    /**
     * 库表命名规则(数据字典)
     */
    private String ruleVal;

    /**
     * 数据库表名
     */
    private  String tableName;

    /**
     * 创建方式 1-基于集市创建 2-基于指标创建 3-基于数据库表创建 4-基于sql创建
     */
    @NotNull(message = "创建方式不能为空")
    @Range(min = 1, max = 4, message = "创建方式范围1-4")
    private Integer createType;

    /**
     * 基于sql创建时的sql
     */
    private String  modelSql;

    // 使用MapStruct进行对象转换，不再需要手动转换方法

}
