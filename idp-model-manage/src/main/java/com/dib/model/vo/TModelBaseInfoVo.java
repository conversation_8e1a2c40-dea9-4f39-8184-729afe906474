package com.dib.model.vo;

import lombok.Data;

/**
 * 模型基础信息VO类，用于替代直接使用TModelBase实体类的场景
 * 主要用于获取模型详情和更新模型信息
 */
@Data
public class TModelBaseInfoVo {

    /**
     * 编号
     */
    private Long id;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 标题
     */
    private String title;
//
//    /**
//     * 资源 ID
//     */
//    private String sourceId;

    /**
     * 模型 ID
     */
    private String modelUuid;

    /**
     * 代号
     */
    private String codeNo;

    /**
     * 类型
     */
    private Integer modelType;

    /**
     * 数据库表
     */
    private String tName;

    /**
     * 扩展属性
     */
    private String extendProperty;

    /**
     * 表ID
     */
    private String tableId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 所属模型库或者模型分组
     */
    private Long modelLibNo;

    /**
     * 字段排序
     */
    private Integer fieldSortNum;

    /**
     * 是否锁表
     */
    private Integer isLock;

    /**
     * 创建方式 1-基于集市创建 2-基于指标创建 3-基于数据库表创建 4-基于sql创建
     */
    private Integer createType;

    /**
     * 基于sql创建时的sql
     */
    private String modelSql;
}