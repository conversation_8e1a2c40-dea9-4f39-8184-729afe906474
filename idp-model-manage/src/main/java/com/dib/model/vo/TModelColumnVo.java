package com.dib.model.vo;

import com.dib.model.domain.TModelColumn;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TModelColumnVo implements Serializable {

    private  static final long serialVersionUID = 1L;

    /**
     * 序号，对应实体类中的编号,创建更新都不用传
     */
    private Long id;
    /**
     * 名称
     */
    @JsonProperty("tName")
    private String tName;
    /**
     * 别名
     */
    private String aliasName;
    /**
     * 标题
     */
    private String title;
    /**
     * 是否维度 0=非维度字段1=维度字段
     */
    private Integer isDim;
    /**
     * 对应维表
     */
    private String relationDim;
    /**
     * 数据类型  1=字符类型  2=数值类型  3=时间类型  4=布尔类型
     */
    private String dataLevel;
    /**
     * 长度
     */
    @JsonProperty("tLength")
    private Integer tLength;
    /**
     * 小数位数
     */
    private Integer decimalDigits;
    /**
     * 是否可为空   0=不可为空  1=可为空
     */
    private Integer isNull;
    /**
     * 是否唯一  0=不唯一  1=唯一
     */
    private Integer isUnique;
    /**
     * 描述
     */
    private String remark;
    /**
     * 模型编号
     */
    private Long modelNo;

    /**
     * 排序号
     */
    private Integer fieldSortNum;

}
