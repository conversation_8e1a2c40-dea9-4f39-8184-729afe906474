package com.dib.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 模型外键信息VO对象
 */
@Data
public class TModelFkInfoVo {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 外键名称
     */
    @JsonProperty("fName")
    private String fName;

    /**
     * 外键列名
     */
    @JsonProperty("fCol")
    private String fCol;

    /**
     * 关联表名
     */
    private String relationTable;

    /**
     * 关联列名
     */
    private String relationCol;

    /**
     * 模型编号
     */
    private Long modelNo;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}