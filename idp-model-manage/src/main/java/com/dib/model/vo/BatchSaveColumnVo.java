package com.dib.model.vo;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * @Author: lengdongxue
 * @Date: 2025/6/3 14:55
 * @Description:
 **/
@Data
public class BatchSaveColumnVo {

    /**
     * 批量保存的列信息
     */
    @NotEmpty(message = "列信息不能为空")
    private List<TModelColumnVo> columnVos;

    /**
     * 是否同步到表
     */
    private boolean syncToTable;

}
