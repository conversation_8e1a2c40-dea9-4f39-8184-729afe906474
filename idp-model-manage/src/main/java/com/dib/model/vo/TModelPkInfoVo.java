package com.dib.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


/**
 * 模型主键信息VO对象
 */
@Data
public class TModelPkInfoVo {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 主键列名
     */
    @JsonProperty("pColumn")
    private String pColumn;

    /**
     * 是否有效 0=有效 1=无效
     */
    private Integer isEnable;

    /**
     * 模型编号
     */
    private Long modelNo;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}