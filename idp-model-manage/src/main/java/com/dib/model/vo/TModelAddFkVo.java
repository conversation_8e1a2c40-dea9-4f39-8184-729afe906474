package com.dib.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TModelAddFkVo {

    private Long id;

    @JsonProperty("fName")
    private String fName;

    @JsonProperty("fCol")
    private String fCol;

    private String relationTable;

    private String relationCol;

    private Long modelNo;

}
