<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dib.model.mapper.TModelIndexMapper" >
  <resultMap id="BaseResultMap" type="com.dib.model.domain.TModelIndex" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="t_type" property="tType" jdbcType="VARCHAR" />
    <result column="t_col" property="tCol" jdbcType="VARCHAR" />
    <result column="is_enable" property="isEnable" jdbcType="INTEGER" />
    <result column="model_no" property="modelNo" jdbcType="INTEGER" />
    <result column="creator_id" property="creatorId" jdbcType="INTEGER" />
    <result column="creator_name" property="creatorName" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="modiftor" jdbcType="VARCHAR" property="modiftor" />
    <result column="modiftor_id" jdbcType="INTEGER" property="modiftorId" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, title, t_type, t_col,is_enable, model_no, creator_id, creator_name, create_date,
    modiftor, modiftor_id, modify_date
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_model_index
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByModelNo" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    from t_model_index
    where model_no = #{modelNo,jdbcType=INTEGER} and is_enable = 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_model_index
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.dib.model.domain.TModelIndex" >
    insert into t_model_index
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="title != null" >
        title,
      </if>
      <if test="tType != null" >
        t_type,
      </if>
      <if test="tCol != null" >
        t_col,
      </if>
      <if test="isEnable != null" >
        is_enable,
      </if>
      <if test="modelNo != null" >
        model_no,
      </if>
      <if test="creatorId != null" >
        creator_id,
      </if>
      <if test="creatorName != null" >
        creator_name,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="modiftor != null">
        modiftor,
      </if>
      <if test="modiftorId != null">
        modiftor_id,
      </if>
      <if test="modifyDate != null">
        modify_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="title != null" >
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="tType != null" >
        #{tType,jdbcType=VARCHAR},
      </if>
      <if test="tCol != null" >
        #{tCol,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="modelNo != null" >
        #{modelNo,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null" >
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modiftor != null">
        #{modiftor,jdbcType=VARCHAR},
      </if>
      <if test="modiftorId != null">
        #{modiftorId,jdbcType=INTEGER},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dib.model.domain.TModelIndex" >
    update t_model_index
    <set >
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="tType != null" >
        t_type = #{tType,jdbcType=VARCHAR},
      </if>
      <if test="tCol != null" >
        t_col = #{tCol,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        is_enable = #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="modelNo != null" >
        model_no = #{modelNo,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null" >
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modiftor != null">
        modiftor = #{modiftor,jdbcType=VARCHAR},
      </if>
      <if test="modiftorId != null">
        modiftor_id = #{modiftorId,jdbcType=INTEGER},
      </if>
      <if test="modifyDate != null">
        modify_date = #{modifyDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dib.model.domain.TModelIndex" >
    update t_model_index
    set title = #{title,jdbcType=VARCHAR},
      t_type = #{tType,jdbcType=VARCHAR},
      t_col = #{tCol,jdbcType=VARCHAR},
      is_enable = #{isEnable,jdbcType=INTEGER},
      model_no = #{modelNo,jdbcType=INTEGER},
      creator_id = #{creatorId,jdbcType=INTEGER},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
        modiftor = #{modiftor,jdbcType=VARCHAR},
        modiftor_id = #{modiftorId,jdbcType=INTEGER},
        modify_date = #{modifyDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>