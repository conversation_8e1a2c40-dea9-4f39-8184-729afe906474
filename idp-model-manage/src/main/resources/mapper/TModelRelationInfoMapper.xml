<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dib.model.mapper.TModelRelationInfoMapper" >
  <resultMap id="BaseResultMap" type="com.dib.model.domain.TModelRelationInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="model_relation_id" property="modelRelationId" jdbcType="BIGINT" />
    <result column="model_id" property="modelId" jdbcType="BIGINT" />
    <result column="is_enable" property="isEnable" jdbcType="INTEGER" />
    <result column="creator_id" property="creatorId" jdbcType="INTEGER" />
    <result column="creator_name" property="creatorName" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="modiftor" property="modiftor" jdbcType="VARCHAR" />
    <result column="modiftor_id" property="modiftorId" jdbcType="INTEGER" />
    <result column="modify_date" property="modifyDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, model_relation_id, model_id, is_enable, creator_id, creator_name, create_date, 
    modiftor, modiftor_id, modify_date
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_model_relation_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_model_relation_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.dib.model.domain.TModelRelationInfo" >
    insert into t_model_relation_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="modelRelationId != null" >
        model_relation_id,
      </if>
      <if test="modelId != null" >
        model_id,
      </if>
      <if test="isEnable != null" >
        is_enable,
      </if>
      <if test="creatorId != null" >
        creator_id,
      </if>
      <if test="creatorName != null" >
        creator_name,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="modiftor != null" >
        modiftor,
      </if>
      <if test="modiftorId != null" >
        modiftor_id,
      </if>
      <if test="modifyDate != null" >
        modify_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="modelRelationId != null" >
        #{modelRelationId,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null" >
        #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null" >
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modiftor != null" >
        #{modiftor,jdbcType=VARCHAR},
      </if>
      <if test="modiftorId != null" >
        #{modiftorId,jdbcType=INTEGER},
      </if>
      <if test="modifyDate != null" >
        #{modifyDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dib.model.domain.TModelRelationInfo" >
    update t_model_relation_info
    <set >
      <if test="modelRelationId != null" >
        model_relation_id = #{modelRelationId,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null" >
        model_id = #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        is_enable = #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null" >
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modiftor != null" >
        modiftor = #{modiftor,jdbcType=VARCHAR},
      </if>
      <if test="modiftorId != null" >
        modiftor_id = #{modiftorId,jdbcType=INTEGER},
      </if>
      <if test="modifyDate != null" >
        modify_date = #{modifyDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dib.model.domain.TModelRelationInfo" >
    update t_model_relation_info
    set model_relation_id = #{modelRelationId,jdbcType=VARCHAR},
      model_id = #{modelId,jdbcType=VARCHAR},
      is_enable = #{isEnable,jdbcType=INTEGER},
      creator_id = #{creatorId,jdbcType=INTEGER},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      modiftor = #{modiftor,jdbcType=VARCHAR},
      modiftor_id = #{modiftorId,jdbcType=INTEGER},
      modify_date = #{modifyDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>