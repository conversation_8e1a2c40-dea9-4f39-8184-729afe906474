<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dib.model.mapper.TModelLibInfoMapper" >
  <resultMap id="BaseResultMap" type="com.dib.model.domain.TModelLibInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="pid" property="pid" jdbcType="INTEGER" />
    <result column="model_type" property="modelType" jdbcType="INTEGER" />
    <result column="model_lib_name" property="modelLibName" jdbcType="VARCHAR" />
    <result column="source_id" property="sourceId" jdbcType="VARCHAR" />
    <result column="database_pool_id" property="databasePoolId" jdbcType="INTEGER" />
    <result column="mark" property="mark" jdbcType="VARCHAR" />
    <result column="creator_id" property="creatorId" jdbcType="INTEGER" />
    <result column="creator_name" property="creatorName" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="modify_date" property="modifyDate" jdbcType="TIMESTAMP" />
    <result column="modiftor" property="modiftor" jdbcType="VARCHAR" />
    <result column="modiftor_id" property="modiftorId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, pid, model_type, model_lib_name, source_id, database_pool_id, mark, creator_id, 
    creator_name, create_date, modify_date, modiftor, modiftor_id
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_model_lib_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_model_lib_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.dib.model.domain.TModelLibInfo" >
    insert into t_model_lib_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="pid != null" >
        pid,
      </if>
      <if test="modelType != null" >
        model_type,
      </if>
      <if test="modelLibName != null" >
        model_lib_name,
      </if>
      <if test="sourceId != null" >
        source_id,
      </if>
      <if test="databasePoolId != null" >
        database_pool_id,
      </if>
      <if test="mark != null" >
        mark,
      </if>
      <if test="creatorId != null" >
        creator_id,
      </if>
      <if test="creatorName != null" >
        creator_name,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="modifyDate != null" >
        modify_date,
      </if>
      <if test="modiftor != null" >
        modiftor,
      </if>
      <if test="modiftorId != null" >
        modiftor_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pid != null" >
        #{pid,jdbcType=INTEGER},
      </if>
      <if test="modelType != null" >
        #{modelType,jdbcType=INTEGER},
      </if>
      <if test="modelLibName != null" >
        #{modelLibName,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null" >
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="databasePoolId != null" >
        #{databasePoolId,jdbcType=INTEGER},
      </if>
      <if test="mark != null" >
        #{mark,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null" >
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyDate != null" >
        #{modifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modiftor != null" >
        #{modiftor,jdbcType=VARCHAR},
      </if>
      <if test="modiftorId != null" >
        #{modiftorId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dib.model.domain.TModelLibInfo" >
    update t_model_lib_info
    <set >
      <if test="pid != null" >
        pid = #{pid,jdbcType=INTEGER},
      </if>
      <if test="modelType != null" >
        model_type = #{modelType,jdbcType=INTEGER},
      </if>
      <if test="modelLibName != null" >
        model_lib_name = #{modelLibName,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null" >
        source_id = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="databasePoolId != null" >
        database_pool_id = #{databasePoolId,jdbcType=INTEGER},
      </if>
      <if test="mark != null" >
        mark = #{mark,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null" >
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyDate != null" >
        modify_date = #{modifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modiftor != null" >
        modiftor = #{modiftor,jdbcType=VARCHAR},
      </if>
      <if test="modiftorId != null" >
        modiftor_id = #{modiftorId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dib.model.domain.TModelLibInfo" >
    update t_model_lib_info
    set pid = #{pid,jdbcType=INTEGER},
      model_type = #{modelType,jdbcType=INTEGER},
      model_lib_name = #{modelLibName,jdbcType=VARCHAR},
      source_id = #{sourceId,jdbcType=VARCHAR},
      database_pool_id = #{databasePoolId,jdbcType=INTEGER},
      mark = #{mark,jdbcType=VARCHAR},
      creator_id = #{creatorId,jdbcType=INTEGER},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      modify_date = #{modifyDate,jdbcType=TIMESTAMP},
      modiftor = #{modiftor,jdbcType=VARCHAR},
      modiftor_id = #{modiftorId,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>