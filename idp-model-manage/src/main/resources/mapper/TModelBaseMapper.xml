<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dib.model.mapper.TModelBaseMapper" >
  <resultMap id="BaseResultMap" type="com.dib.model.domain.TModelBase" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="source_id" property="sourceId" jdbcType="VARCHAR" />
    <result column="model_uuid" property="modelUuid" jdbcType="VARCHAR" />
    <result column="code_no" property="codeNo" jdbcType="VARCHAR" />
    <result column="model_type" property="modelType" jdbcType="INTEGER" />
    <result column="t_name" property="tName" jdbcType="VARCHAR" />
    <result column="table_id" property="tableId" jdbcType="VARCHAR" />
    <result column="extend_property" property="extendProperty" jdbcType="VARCHAR" />
    <result column="is_enable" property="isEnable" jdbcType="INTEGER" />
    <result column="creator_id" property="creatorId" jdbcType="INTEGER" />
    <result column="creator_name" property="creatorName" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="modiftor" property="modiftor" jdbcType="VARCHAR" />
    <result column="modiftor_id" property="modiftorId" jdbcType="INTEGER" />
    <result column="modify_date" property="modifyDate" jdbcType="TIMESTAMP" />
    <result column="model_lib_no" property="modelLibNo" jdbcType="INTEGER" />
    <result column="model_name" property="modelName" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_type" property="remark" jdbcType="INTEGER" />
    <result column="model_sql" property="remark" jdbcType="VARCHAR" />
    <result column="copy_count" property="copyCount" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, title, source_id, model_uuid, code_no, model_type, t_name, table_id, extend_property,
    is_enable, creator_id, creator_name, create_date, modiftor, modiftor_id, modify_date,
    model_lib_no, model_name, remark,create_type,model_sql,copy_count
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from t_model_base
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_model_base
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.dib.model.domain.TModelBase" >
    insert into t_model_base
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="title != null" >
        title,
      </if>
      <if test="sourceId != null" >
        source_id,
      </if>
      <if test="modelUuid != null" >
        model_uuid,
      </if>
      <if test="codeNo != null" >
        code_no,
      </if>
      <if test="modelType != null" >
        model_type,
      </if>
      <if test="tName != null" >
        t_name,
      </if>
      <if test="tableId != null" >
        table_id,
      </if>
      <if test="extendProperty != null" >
        extend_property,
      </if>
      <if test="isEnable != null" >
        is_enable,
      </if>
      <if test="creatorId != null" >
        creator_id,
      </if>
      <if test="creatorName != null" >
        creator_name,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="modiftor != null" >
        modiftor,
      </if>
      <if test="modiftorId != null" >
        modiftor_id,
      </if>
      <if test="modifyDate != null" >
        modify_date,
      </if>
      <if test="modelLibNo != null" >
        model_lib_no,
      </if>
      <if test="modelName != null" >
        model_name,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="title != null" >
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null" >
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="modelUuid != null" >
        #{modelUuid,jdbcType=VARCHAR},
      </if>
      <if test="codeNo != null" >
        #{codeNo,jdbcType=VARCHAR},
      </if>
      <if test="modelType != null" >
        #{modelType,jdbcType=INTEGER},
      </if>
      <if test="tName != null" >
        #{tName,jdbcType=VARCHAR},
      </if>
      <if test="tableId != null" >
        #{tableId,jdbcType=VARCHAR},
      </if>
      <if test="extendProperty != null" >
        #{extendProperty,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null" >
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modiftor != null" >
        #{modiftor,jdbcType=VARCHAR},
      </if>
      <if test="modiftorId != null" >
        #{modiftorId,jdbcType=INTEGER},
      </if>
      <if test="modifyDate != null" >
        #{modifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modelLibNo != null" >
        #{modelLibNo,jdbcType=INTEGER},
      </if>
      <if test="modelName != null" >
        #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dib.model.domain.TModelBase" >
    update t_model_base
    <set >
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null" >
        source_id = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="modelUuid != null" >
        model_uuid = #{modelUuid,jdbcType=VARCHAR},
      </if>
      <if test="codeNo != null" >
        code_no = #{codeNo,jdbcType=VARCHAR},
      </if>
      <if test="modelType != null" >
        model_type = #{modelType,jdbcType=INTEGER},
      </if>
      <if test="tName != null" >
        t_name = #{tName,jdbcType=VARCHAR},
      </if>
      <if test="tableId != null" >
        table_id = #{tableId,jdbcType=VARCHAR},
      </if>
      <if test="extendProperty != null" >
        extend_property = #{extendProperty,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        is_enable = #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null" >
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modiftor != null" >
        modiftor = #{modiftor,jdbcType=VARCHAR},
      </if>
      <if test="modiftorId != null" >
        modiftor_id = #{modiftorId,jdbcType=INTEGER},
      </if>
      <if test="modifyDate != null" >
        modify_date = #{modifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modelLibNo != null" >
        model_lib_no = #{modelLibNo,jdbcType=INTEGER},
      </if>
      <if test="modelName != null" >
        model_name = #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dib.model.domain.TModelBase" >
    update t_model_base
    set title = #{title,jdbcType=VARCHAR},
        source_id = #{sourceId,jdbcType=VARCHAR},
        model_uuid = #{modelUuid,jdbcType=VARCHAR},
        code_no = #{codeNo,jdbcType=VARCHAR},
        model_type = #{modelType,jdbcType=INTEGER},
        t_name = #{tName,jdbcType=VARCHAR},
        table_id = #{tableId,jdbcType=VARCHAR},
        extend_property = #{extendProperty,jdbcType=VARCHAR},
        is_enable = #{isEnable,jdbcType=INTEGER},
        creator_id = #{creatorId,jdbcType=INTEGER},
        creator_name = #{creatorName,jdbcType=VARCHAR},
        create_date = #{createDate,jdbcType=TIMESTAMP},
        modiftor = #{modiftor,jdbcType=VARCHAR},
        modiftor_id = #{modiftorId,jdbcType=INTEGER},
        modify_date = #{modifyDate,jdbcType=TIMESTAMP},
        model_lib_no = #{modelLibNo,jdbcType=INTEGER},
        model_name = #{modelName,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>