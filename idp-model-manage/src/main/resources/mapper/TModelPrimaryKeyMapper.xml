<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dib.model.mapper.TModelPrimaryKeyMapper" >
  <resultMap id="BaseResultMap" type="com.dib.model.domain.TModelPrimaryKey" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="p_column" property="pColumn" jdbcType="VARCHAR" />
    <result column="f_name" property="fName" jdbcType="VARCHAR" />
    <result column="f_col" property="fCol" jdbcType="VARCHAR" />
    <result column="relation_table" property="relationTable" jdbcType="VARCHAR" />
    <result column="relation_col" property="relationCol" jdbcType="VARCHAR" />
    <result column="model_no" property="modelNo" jdbcType="INTEGER" />
    <result column="creator_id" property="creatorId" jdbcType="INTEGER" />
    <result column="creator_name" property="creatorName" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, p_column, f_name, f_col, relation_table, relation_col, model_no, creator_id, 
    creator_name, create_date
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_model_primary_key
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_model_primary_key
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.dib.model.domain.TModelPrimaryKey" >
    insert into t_model_primary_key
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="pColumn != null" >
        p_column,
      </if>
      <if test="fName != null" >
        f_name,
      </if>
      <if test="fCol != null" >
        f_col,
      </if>
      <if test="relationTable != null" >
        relation_table,
      </if>
      <if test="relationCol != null" >
        relation_col,
      </if>
      <if test="modelNo != null" >
        model_no,
      </if>
      <if test="creatorId != null" >
        creator_id,
      </if>
      <if test="creatorName != null" >
        creator_name,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pColumn != null" >
        #{pColumn,jdbcType=VARCHAR},
      </if>
      <if test="fName != null" >
        #{fName,jdbcType=VARCHAR},
      </if>
      <if test="fCol != null" >
        #{fCol,jdbcType=VARCHAR},
      </if>
      <if test="relationTable != null" >
        #{relationTable,jdbcType=VARCHAR},
      </if>
      <if test="relationCol != null" >
        #{relationCol,jdbcType=VARCHAR},
      </if>
      <if test="modelNo != null" >
        #{modelNo,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null" >
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dib.model.domain.TModelPrimaryKey" >
    update t_model_primary_key
    <set >
      <if test="pColumn != null" >
        p_column = #{pColumn,jdbcType=VARCHAR},
      </if>
      <if test="fName != null" >
        f_name = #{fName,jdbcType=VARCHAR},
      </if>
      <if test="fCol != null" >
        f_col = #{fCol,jdbcType=VARCHAR},
      </if>
      <if test="relationTable != null" >
        relation_table = #{relationTable,jdbcType=VARCHAR},
      </if>
      <if test="relationCol != null" >
        relation_col = #{relationCol,jdbcType=VARCHAR},
      </if>
      <if test="modelNo != null" >
        model_no = #{modelNo,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null" >
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dib.model.domain.TModelPrimaryKey" >
    update t_model_primary_key
    set p_column = #{pColumn,jdbcType=VARCHAR},
      f_name = #{fName,jdbcType=VARCHAR},
      f_col = #{fCol,jdbcType=VARCHAR},
      relation_table = #{relationTable,jdbcType=VARCHAR},
      relation_col = #{relationCol,jdbcType=VARCHAR},
      model_no = #{modelNo,jdbcType=INTEGER},
      creator_id = #{creatorId,jdbcType=INTEGER},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>