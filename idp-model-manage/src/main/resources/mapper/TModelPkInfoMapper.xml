<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dib.model.mapper.TModelPkInfoMapper" >
  <resultMap id="BaseResultMap" type="com.dib.model.domain.TModelPkInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="p_column" property="pColumn" jdbcType="VARCHAR" />
    <result column="is_enable" property="isEnable" jdbcType="INTEGER" />
    <result column="model_no" property="modelNo" jdbcType="BIGINT" />
    <result column="creator_id" property="creatorId" jdbcType="INTEGER" />
    <result column="creator_name" property="creatorName" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="modiftor_id" property="modiftorId" jdbcType="INTEGER" />
    <result column="modiftor" property="modiftor" jdbcType="VARCHAR" />
    <result column="modify_date" property="modifyDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, p_column, is_enable, model_no, creator_id, creator_name, create_date, modiftor_id, 
    modiftor, modify_date
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_model_pk_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_model_pk_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.dib.model.domain.TModelPkInfo" >
    insert into t_model_pk_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="pColumn != null" >
        p_column,
      </if>
      <if test="isEnable != null" >
        is_enable,
      </if>
      <if test="modelNo != null" >
        model_no,
      </if>
      <if test="creatorId != null" >
        creator_id,
      </if>
      <if test="creatorName != null" >
        creator_name,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="modiftorId != null" >
        modiftor_id,
      </if>
      <if test="modiftor != null" >
        modiftor,
      </if>
      <if test="modifyDate != null" >
        modify_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pColumn != null" >
        #{pColumn,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="modelNo != null" >
        #{modelNo,jdbcType=BIGINT},
      </if>
      <if test="creatorId != null" >
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modiftorId != null" >
        #{modiftorId,jdbcType=INTEGER},
      </if>
      <if test="modiftor != null" >
        #{modiftor,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null" >
        #{modifyDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dib.model.domain.TModelPkInfo" >
    update t_model_pk_info
    <set >
      <if test="pColumn != null" >
        p_column = #{pColumn,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        is_enable = #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="modelNo != null" >
        model_no = #{modelNo,jdbcType=BIGINT},
      </if>
      <if test="creatorId != null" >
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null" >
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modiftorId != null" >
        modiftor_id = #{modiftorId,jdbcType=INTEGER},
      </if>
      <if test="modiftor != null" >
        modiftor = #{modiftor,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null" >
        modify_date = #{modifyDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dib.model.domain.TModelPkInfo" >
    update t_model_pk_info
    set p_column = #{pColumn,jdbcType=VARCHAR},
      is_enable = #{isEnable,jdbcType=INTEGER},
      model_no = #{modelNo,jdbcType=BIGINT},
      creator_id = #{creatorId,jdbcType=INTEGER},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      modiftor_id = #{modiftorId,jdbcType=INTEGER},
      modiftor = #{modiftor,jdbcType=VARCHAR},
      modify_date = #{modifyDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>