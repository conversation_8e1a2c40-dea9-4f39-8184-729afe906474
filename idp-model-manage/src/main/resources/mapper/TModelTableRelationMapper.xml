<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dib.model.mapper.TModelTableRelationMapper">

  <resultMap id="BaseResultMap" type="com.dib.model.domain.TModelTableRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="t_name" jdbcType="VARCHAR" property="tName" />
    <result column="t_def" jdbcType="VARCHAR" property="tDef" />
    <result column="union_method" jdbcType="INTEGER" property="unionMethod" />
    <result column="t_exp" jdbcType="VARCHAR" property="tExp" />
    <result column="model_lib_no" jdbcType="INTEGER" property="modelLibNo" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="modiftor" jdbcType="VARCHAR" property="modiftor" />
    <result column="modiftor_id" jdbcType="INTEGER" property="modiftorId" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, t_name, t_def, union_method, t_exp, model_lib_no, creator_id, creator_name, create_date, 
    modiftor, modiftor_id, modify_date
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_model_table_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_model_table_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.dib.model.domain.TModelTableRelation">
    insert into t_model_table_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tName != null">
        t_name,
      </if>
      <if test="tDef != null">
        t_def,
      </if>
      <if test="unionMethod != null">
        union_method,
      </if>
      <if test="tExp != null">
        t_exp,
      </if>
      <if test="modelLibNo != null">
        model_lib_no,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="modiftor != null">
        modiftor,
      </if>
      <if test="modiftorId != null">
        modiftor_id,
      </if>
      <if test="modifyDate != null">
        modify_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tName != null">
        #{tName,jdbcType=VARCHAR},
      </if>
      <if test="tDef != null">
        #{tDef,jdbcType=VARCHAR},
      </if>
      <if test="unionMethod != null">
        #{unionMethod,jdbcType=INTEGER},
      </if>
      <if test="tExp != null">
        #{tExp,jdbcType=VARCHAR},
      </if>
      <if test="modelLibNo != null">
        #{modelLibNo,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modiftor != null">
        #{modiftor,jdbcType=VARCHAR},
      </if>
      <if test="modiftorId != null">
        #{modiftorId,jdbcType=INTEGER},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dib.model.domain.TModelTableRelation">
    update t_model_table_relation
    <set>
      <if test="tName != null">
        t_name = #{tName,jdbcType=VARCHAR},
      </if>
      <if test="tDef != null">
        t_def = #{tDef,jdbcType=VARCHAR},
      </if>
      <if test="unionMethod != null">
        union_method = #{unionMethod,jdbcType=INTEGER},
      </if>
      <if test="tExp != null">
        t_exp = #{tExp,jdbcType=VARCHAR},
      </if>
      <if test="modelLibNo != null">
        model_lib_no = #{modelLibNo,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modiftor != null">
        modiftor = #{modiftor,jdbcType=VARCHAR},
      </if>
      <if test="modiftorId != null">
        modiftor_id = #{modiftorId,jdbcType=INTEGER},
      </if>
      <if test="modifyDate != null">
        modify_date = #{modifyDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dib.model.domain.TModelTableRelation">
    update t_model_table_relation
    set t_name = #{tName,jdbcType=VARCHAR},
      t_def = #{tDef,jdbcType=VARCHAR},
      union_method = #{unionMethod,jdbcType=INTEGER},
      t_exp = #{tExp,jdbcType=VARCHAR},
      model_lib_no = #{modelLibNo,jdbcType=INTEGER},
      creator_id = #{creatorId,jdbcType=INTEGER},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      modiftor = #{modiftor,jdbcType=VARCHAR},
      modiftor_id = #{modiftorId,jdbcType=INTEGER},
      modify_date = #{modifyDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>