package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.entity.AttributeDataEntity;
import com.dib.domin.query.AttributeDataQuery;
import com.dib.service.AttributeDataService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 属性数据
 */
@Tag(name = "属性数据")
@RestController
@RequestMapping("/attributeData")
public class AttributeDataController {

    @Autowired
    private AttributeDataService attributeDataService;

    // 添加
    @Operation(summary = "添加属性", description = "添加属性")
    @PostMapping("/add")
    @LogRecord(
        type = "属性数据",
        subType = "添加属性",
        bizNo = "",
        success = "添加属性成功",
        fail = "添加属性失败",
        extra = "执行参数：{{#attributeDataEntity}}"
    )
    public AjaxResult addAttributeData(@RequestBody AttributeDataEntity attributeDataEntity) {
        boolean result = attributeDataService.saveAttributeData(attributeDataEntity);
        if (result) {
            return AjaxResult.success(result);
        } else {
            return AjaxResult.error("Failed to add attribute data");
        }
    }

    // 更新
    @Operation(summary = "更新属性", description = "更新属性")
    @PostMapping("/update")
    @LogRecord(
        type = "属性数据",
        subType = "更新属性",
        bizNo = "",
        success = "更新属性成功",
        fail = "更新属性失败",
        extra = "执行参数：{{#attributeDataEntity}}"
    )
    public AjaxResult updateAttributeData(@RequestBody AttributeDataEntity attributeDataEntity) {
        boolean result = attributeDataService.updateAttributeData(attributeDataEntity);
        if (result) {
            return AjaxResult.success(attributeDataEntity);
        } else {
            return AjaxResult.error("Failed to update attribute data");
        }
    }

    // 删除
    @Operation(summary = "删除属性", description = "删除属性")
    @DeleteMapping("/deleteAttributeData")
    @LogRecord(
        type = "属性数据",
        subType = "删除属性",
        bizNo = "",
        success = "删除属性成功",
        fail = "删除属性失败",
        extra = "执行参数：{{#id}}"
    )
    public AjaxResult deleteAttributeData(@RequestParam String id) {
        boolean result = attributeDataService.deleteAttributeDataById(id);
        if (result) {
            return AjaxResult.success(null);
        } else {
            return AjaxResult.error("Failed to delete attribute data");
        }
    }

    // 查询
    @Operation(summary = "查询属性集下属性", description = "查询属性集下属性")
    @PostMapping("/getAttributeDataBySetId")
    @LogRecord(
        type = "属性数据",
        subType = "查询属性集下属性",
        bizNo = "",
        success = "查询属性集下属性成功",
        fail = "查询属性集下属性失败",
        extra = "执行参数：{{#attributeDataQuery}}"
    )
    public AjaxResult getAttributeDataBySetId(@RequestBody AttributeDataQuery attributeDataQuery) {
        List<AttributeDataEntity> result = attributeDataService.getAttributeDataById(attributeDataQuery);
        if (result != null) {
            return AjaxResult.success(result);
        } else {
            return AjaxResult.error("Attribute data not found");
        }
    }
}
