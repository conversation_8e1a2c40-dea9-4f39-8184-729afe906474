package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.entity.CateGoryEntity;
import com.dib.service.CateGoryService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 目录类目
 */
@Tag(name = "资产目录类目")
@RestController
@RequestMapping("/cateGory")
public class CateGoryController {

    @Autowired
    private CateGoryService cateGoryService;

    /**
     * 创建目录
     */
    @Operation(summary = "添加目录类目", description = "添加目录类目")
    @PostMapping("/add")
    @LogRecord(
        type = "资产目录类目",
        subType = "添加目录类目",
        bizNo = "",
        success = "添加目录类目成功",
        fail = "添加目录类目失败",
        extra = "执行参数：{{#cateGoryEntity}}"
    )
    public AjaxResult addCateGory(@RequestBody CateGoryEntity cateGoryEntity) {
        return AjaxResult.success(cateGoryService.addCateGory(cateGoryEntity));
    }

    // 读取单个分类（Read One）
    @Operation(summary = "获取目录类目树", description = "获取目录类目树")
    @GetMapping("/getCateGoryTreeById")
    @LogRecord(
        type = "资产目录类目",
        subType = "获取目录类目树",
        bizNo = "",
        success = "获取目录类目树成功",
        fail = "获取目录类目树失败",
        extra = "执行参数：{{#catalogueId}}"
    )
    public AjaxResult getCateGoryTreeById(@RequestParam String catalogueId) {
        return AjaxResult.success(cateGoryService.getCateGoryTreeById(catalogueId));
    }

    // 更新（Update）
    @Operation(summary = "更新目录类目", description = "更新目录类目")
    @PostMapping("/update")
    @LogRecord(
        type = "资产目录类目",
        subType = "更新目录类目",
        bizNo = "",
        success = "更新目录类目成功",
        fail = "更新目录类目失败",
        extra = "执行参数：{{#cateGoryEntity}}"
    )
    public AjaxResult updateCateGory(@RequestBody CateGoryEntity cateGoryEntity) {
        return AjaxResult.success(cateGoryService.updateCateGory(cateGoryEntity));
    }

    // 删除（Delete）
    @Operation(summary = "删除目录类目", description = "删除目录类目")
    @PostMapping("/deleteCateGory")
    @LogRecord(
        type = "资产目录类目",
        subType = "删除目录类目",
        bizNo = "",
        success = "删除目录类目成功",
        fail = "删除目录类目失败",
        extra = "执行参数：{{#cateGoryEntity}}"
    )
    public AjaxResult deleteCateGory(@RequestBody CateGoryEntity cateGoryEntity) {
        return AjaxResult.success(cateGoryService.deleteCateGory(cateGoryEntity));
    }
}
