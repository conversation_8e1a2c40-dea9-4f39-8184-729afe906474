package com.dib.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.core.database.core.JsonPage;
import com.dib.market.dto.ApiMaskDto;
import com.dib.market.entity.ApiMaskEntity;
import com.dib.market.mapstruct.ApiMaskMapper;
import com.dib.market.query.ApiMaskQuery;
import com.dib.market.service.ApiMaskService;
import com.dib.market.vo.ApiMaskVo;
import com.dib.metadata.validate.ValidationGroups;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据API脱敏信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
@Tag(name = "数据API脱敏信息表")
@RestController
@RequestMapping("market/apiMasks")
public class ApiMaskController extends BaseController {

    @Autowired
    private ApiMaskService apiMaskService;

    @Autowired
    private ApiMaskMapper apiMaskMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @LogRecord(
            type = "数据API脱敏",
            subType = "获取详细信息",
            bizNo = "",
            success = "获取详细信息成功",
            fail = "获取详细信息失败",
            extra = "执行参数：id={{#id}}"
    )
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true)
    @GetMapping("/{id}")
    public AjaxResult getApiMaskById(@PathVariable String id) {
        ApiMaskEntity apiMaskEntity = apiMaskService.getApiMaskById(id);
        return AjaxResult.success(apiMaskMapper.toVO(apiMaskEntity));
    }

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @LogRecord(
            type = "数据API脱敏",
            subType = "获取API详情",
            bizNo = "",
            success = "获取API详情成功",
            fail = "获取API详情失败",
            extra = "执行参数：id={{#id}}"
    )
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true)
    @GetMapping("/api/{id}")
    public AjaxResult getApiMaskByApiId(@PathVariable String id) {
        ApiMaskEntity apiMaskEntity = apiMaskService.getApiMaskByApiId(id);
        return AjaxResult.success(apiMaskMapper.toVO(apiMaskEntity));
    }

    /**
     * 分页查询信息
     *
     * @param apiMaskQuery
     * @return
     */
    @LogRecord(
            type = "数据API脱敏",
            subType = "分页查询",
            bizNo = "",
            success = "分页查询成功",
            fail = "分页查询失败",
            extra = "执行参数：{{#apiMaskQuery}}"
    )
    @Operation(summary = "分页查询", description = "")
    @Parameter(name = "apiMaskQuery", description = "查询实体apiMaskQuery", required = true)
    @GetMapping("/page")
    public AjaxResult getApiMaskPage(ApiMaskQuery apiMaskQuery) {
        QueryWrapper<ApiMaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(apiMaskQuery.getMaskName()), "mask_name", apiMaskQuery.getMaskName());
        IPage<ApiMaskEntity> page = apiMaskService.page(new Page<>(apiMaskQuery.getPageNum(), apiMaskQuery.getPageSize()), queryWrapper);
        List<ApiMaskVo> collect = page.getRecords().stream().map(apiMaskMapper::toVO).collect(Collectors.toList());
        JsonPage<ApiMaskVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }

    /**
     * 添加
     * @param apiMask
     * @return
     */
    @LogRecord(
            type = "数据API脱敏",
            subType = "添加信息",
            bizNo = "",
            success = "添加信息成功",
            fail = "添加信息失败",
            extra = "执行参数：{{#apiMask}}"
    )
    @Operation(summary = "添加信息", description = "根据apiMask对象添加信息")
    @Parameter(name = "apiMask", description = "详细实体apiMask", required = true)
    @PostMapping()
    public AjaxResult saveApiMask(@RequestBody @Validated({ValidationGroups.Insert.class}) ApiMaskDto apiMask) {
        apiMaskService.saveApiMask(apiMask);
        return AjaxResult.success();
    }

    /**
     * 修改
     * @param apiMask
     * @return
     */
    @LogRecord(
            type = "数据API脱敏",
            subType = "修改信息",
            bizNo = "",
            success = "修改信息成功",
            fail = "修改信息失败",
            extra = "执行参数：{{#apiMask}}"
    )
    @Operation(summary = "修改信息", description = "根据url的id来指定修改对象，并根据传过来的信息来修改详细信息")
    @Parameter(name = "id", description = "ID", required = true)
    @Parameter(name = "apiMask", description = "详细实体apiMask", required = true)
    @PutMapping("/{id}")
    public AjaxResult updateApiMask(@PathVariable String id, @RequestBody @Validated({ValidationGroups.Update.class}) ApiMaskDto apiMask) {
        apiMaskService.updateApiMask(apiMask);
        return AjaxResult.success();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @LogRecord(
            type = "数据API脱敏",
            subType = "删除",
            bizNo = "",
            success = "删除成功",
            fail = "删除失败",
            extra = "执行参数：id={{#id}}"
    )
    @Operation(summary = "删除", description = "根据url的id来指定删除对象")
    @Parameter(name = "id", description = "ID", required = true)
    @DeleteMapping("/{id}")
    public AjaxResult deleteApiMaskById(@PathVariable String id) {
        apiMaskService.deleteApiMaskById(id);
        return AjaxResult.success();
    }

    @LogRecord(
            type = "数据API脱敏",
            subType = "批量删除",
            bizNo = "",
            success = "批量删除成功",
            fail = "批量删除失败",
            extra = "执行参数：ids={{#ids}}"
    )
    @Operation(summary = "批量删除", description = "根据url的ids来批量删除对象")
    @Parameter(name = "ids", description = "ID集合", required = true)
    @DeleteMapping("/batch/{ids}")
    public AjaxResult deleteApiMaskBatch(@PathVariable List<String> ids) {
        apiMaskService.deleteApiMaskBatch(ids);
        return AjaxResult.success();
    }
}
