package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.entity.CatalogueEntity;
import com.dib.domin.query.CatalogueQuery;
import com.dib.service.CatalogueService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 目录
 */
@Tag(name = "目录管理")
@RestController
@RequestMapping("/catalogue")
public class CatalogueController {

    @Autowired
    private CatalogueService catalogueService;

    @Operation(summary = "获取所有目录", description = "获取所有目录")
    @GetMapping("/list")
    @LogRecord(
        type = "目录管理",
        subType = "获取所有目录",
        bizNo = "",
        success = "获取所有目录成功",
        fail = "获取所有目录失败",
        extra = ""
    )
    public AjaxResult getCatalogueList() {
        return AjaxResult.success(catalogueService.getCatalogueList());
    }

    @Operation(summary = "分页查询目录", description = "分页查询目录")
    @PostMapping("/page")
    @LogRecord(
        type = "目录管理",
        subType = "分页查询目录",
        bizNo = "",
        success = "分页查询目录成功",
        fail = "分页查询目录失败",
        extra = "执行参数：{{#catalogueQuery}}"
    )
    public AjaxResult getCataloguePage(@RequestBody CatalogueQuery catalogueQuery) {
        return AjaxResult.success(catalogueService.getCataloguePage(catalogueQuery));
    }

    @Operation(summary = "新增目录", description = "新增目录")
    @PostMapping("/addCatalogue")
    @LogRecord(
        type = "目录管理",
        subType = "新增目录",
        bizNo = "",
        success = "新增目录成功",
        fail = "新增目录失败",
        extra = "执行参数：{{#catalogueEntity}}"
    )
    public AjaxResult addCatalogue(@RequestBody CatalogueEntity catalogueEntity) {
        boolean result = catalogueService.addCatalogue(catalogueEntity);
        if (!result) {
            return AjaxResult.error("新增失败");
        }
        return AjaxResult.success("新增成功");
    }

    @Operation(summary = "删除目录", description = "删除目录")
    @PostMapping("/deleteCatalogue")
    @LogRecord(
        type = "目录管理",
        subType = "删除目录",
        bizNo = "",
        success = "删除目录成功",
        fail = "删除目录失败",
        extra = "执行参数：{{#ids}}"
    )
    public AjaxResult deleteCatalogue(@RequestBody List<String> ids) {
        boolean result = catalogueService.deleteCatalogue(ids);
        if (!result) {
            return AjaxResult.error("删除失败");
        }
        return AjaxResult.success("删除成功");
    }

    @Operation(summary = "删除目录测试", description = "删除目录测试")
    @PostMapping("/deleteTestCatalogue")
    @LogRecord(
        type = "目录管理",
        subType = "删除目录测试",
        bizNo = "",
        success = "删除目录测试成功",
        fail = "删除目录测试失败",
        extra = "执行参数：{{#ids}}"
    )
    public AjaxResult deleteTestCatalogue(@RequestBody List<String> ids) {
        return AjaxResult.success(catalogueService.deleteTestCatalogue(ids));
    }

    @Operation(summary = "修改目录", description = "修改目录")
    @PostMapping("/updateCatalogue")
    @LogRecord(
        type = "目录管理",
        subType = "修改目录",
        bizNo = "",
        success = "修改目录成功",
        fail = "修改目录失败",
        extra = "执行参数：{{#catalogueEntity}}"
    )
    public AjaxResult updateCatalogue(@RequestBody CatalogueEntity catalogueEntity) {
        boolean result = catalogueService.updateCatalogue(catalogueEntity);
        if (!result) {
            return AjaxResult.error("修改失败");
        }
        return AjaxResult.success("修改成功");
    }

    @Operation(summary = "查询在编目录", description = "查询在编目录")
    @GetMapping("/queryEditCatalogueList")
    @LogRecord(
        type = "目录管理",
        subType = "查询在编目录",
        bizNo = "",
        success = "查询在编目录成功",
        fail = "查询在编目录失败",
        extra = ""
    )
    public AjaxResult queryEditCatalogueList() {
        return AjaxResult.success(catalogueService.queryEditCatalogueList());
    }

    @Operation(summary = "查询开放目录", description = "查询开放目录")
    @GetMapping("/queryOpennessCatalogueList")
    @LogRecord(
        type = "目录管理",
        subType = "查询开放目录",
        bizNo = "",
        success = "查询开放目录成功",
        fail = "查询开放目录失败",
        extra = ""
    )
    public AjaxResult queryOpennessCatalogueList() {
        return AjaxResult.success(catalogueService.queryOpennessCatalogueList());
    }
}
