package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.entity.AttributeSetEntity;
import com.dib.service.AttributeSetService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "属性集合")
@RestController
@RequestMapping("/attributeSet")
public class AttributeSetController {

    @Autowired
    private AttributeSetService attributeSetService;

    // 添加
    @Operation(summary = "新增属性集", description = "新增属性集")
    @PostMapping("/add")
    @LogRecord(
        type = "属性集合",
        subType = "新增属性集",
        bizNo = "",
        success = "新增属性集成功",
        fail = "新增属性集失败",
        extra = "执行参数：{{#attributeSetEntity}}"
    )
    public AjaxResult addAttributeSet(@RequestBody AttributeSetEntity attributeSetEntity) {
        return AjaxResult.success(attributeSetService.saveAttributeSet(attributeSetEntity));
    }

    // 更新
    @Operation(summary = "修改属性集", description = "修改属性集")
    @PostMapping("/update")
    @LogRecord(
        type = "属性集合",
        subType = "修改属性集",
        bizNo = "",
        success = "修改属性集成功",
        fail = "修改属性集失败",
        extra = "执行参数：{{#attributeSetEntity}}"
    )
    public AjaxResult updateAttributeSet(@RequestBody AttributeSetEntity attributeSetEntity) {
        return AjaxResult.success(attributeSetService.updateAttributeSet(attributeSetEntity));
    }

    // 删除
    @Operation(summary = "删除属性集", description = "删除属性集")
    @DeleteMapping("/deleteAttributeSet")
    @LogRecord(
        type = "属性集合",
        subType = "删除属性集",
        bizNo = "",
        success = "删除属性集成功",
        fail = "删除属性集失败",
        extra = "执行参数：{{#id}}"
    )
    public AjaxResult deleteAttributeSet(@RequestParam Long id) {
        return AjaxResult.success(attributeSetService.deleteAttributeSetById(id));
    }

    // 查询属性树
    @Operation(summary = "获取属性集树", description = "获取属性集树")
    @GetMapping("/getAttributeSetTree")
    @LogRecord(
        type = "属性集合",
        subType = "获取属性集树",
        bizNo = "",
        success = "获取属性集树成功",
        fail = "获取属性集树失败",
        extra = ""
    )
    public AjaxResult getAttributeSetTree() {
        return AjaxResult.success(attributeSetService.getAttributeSetTree());
    }

    // 查询
    @Operation(summary = "获取目录属性集及数据树", description = "获取目录属性集及数据树")
    @GetMapping("/queryAttributeSetAndDataTree")
    @LogRecord(
        type = "属性集合",
        subType = "获取目录属性集及数据树",
        bizNo = "",
        success = "获取目录属性集及数据树成功",
        fail = "获取目录属性集及数据树失败",
        extra = "执行参数：{{#catalogueId}}"
    )
    public AjaxResult queryAttributeSetAndDataTree(@RequestParam String catalogueId) {
        return AjaxResult.success(attributeSetService.queryAttributeSetAndDataTree(catalogueId));
    }

    // 查询
    @Operation(summary = "获取所有属性集", description = "获取所有属性集")
    @GetMapping("/getAttributeSetAll")
    @LogRecord(
        type = "属性集合",
        subType = "获取所有属性集",
        bizNo = "",
        success = "获取所有属性集成功",
        fail = "获取所有属性集失败",
        extra = ""
    )
    public AjaxResult getAttributeSetAll() {
        return AjaxResult.success(attributeSetService.getAttributeSetAll());
    }
}
