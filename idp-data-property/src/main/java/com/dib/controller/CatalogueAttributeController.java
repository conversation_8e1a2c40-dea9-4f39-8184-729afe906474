package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.dto.AttributeDataDto;
import com.dib.domin.dto.CatalogueAttributeDto;
import com.dib.domin.entity.CatalogueAttributeEntity;
import com.dib.service.CatalogueAttributeService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "目录属性")
@RestController
@RequestMapping("/catalogueAttribute")
public class CatalogueAttributeController {

    @Autowired
    private CatalogueAttributeService catalogueAttributeService;

    /**
     * 新增
     * @param attributeDataDtoList
     * @return
     */
    @Operation(summary = "绑定目录属性", description = "绑定目录属性")
    @PostMapping("/add")
    @LogRecord(
        type = "目录属性",
        subType = "绑定目录属性",
        bizNo = "",
        success = "绑定目录属性成功",
        fail = "绑定目录属性失败",
        extra = "执行参数：{{#attributeDataDtoList}}"
    )
    public AjaxResult createCatalogueAttribute(@RequestBody List<AttributeDataDto> attributeDataDtoList) {
        return AjaxResult.success(catalogueAttributeService.createCatalogueAttribute(attributeDataDtoList));
    }

    /**
     * 获取指定目录，指定属性类型下数据
     * @param catalogueAttributeDto
     * @return
     */
    @Operation(summary = "获取指定目录指定属性集下属性", description = "获取指定目录指定属性集下属性")
    @PostMapping("/getCatalogueAttributeData")
    @LogRecord(
        type = "目录属性",
        subType = "获取指定目录指定属性集下属性",
        bizNo = "",
        success = "获取指定目录指定属性集下属性成功",
        fail = "获取指定目录指定属性集下属性失败",
        extra = "执行参数：{{#catalogueAttributeDto}}"
    )
    public AjaxResult getCatalogueAttributeData(@RequestBody CatalogueAttributeDto catalogueAttributeDto) {
        return AjaxResult.success(catalogueAttributeService.getCatalogueAttributeData(catalogueAttributeDto));
    }

    /**
     * 获取指定目录下数据
     * @param catalogueId
     * @return
     */
    @Operation(summary = "获取指定目录下详细属性", description = "获取指定目录下详细属性")
    @GetMapping("/getAssetCatalogueData")
    @LogRecord(
        type = "目录属性",
        subType = "获取指定目录下详细属性",
        bizNo = "",
        success = "获取指定目录下详细属性成功",
        fail = "获取指定目录下详细属性失败",
        extra = "执行参数：{{#catalogueId}}"
    )
    public AjaxResult getAssetCatalogueData(@RequestParam String catalogueId) {
        return AjaxResult.success(catalogueAttributeService.getAssetCatalogueData(catalogueId));
    }

    /**
     * 获取指定目录下目录数据
     * @param catalogueId
     * @return
     */
    @Operation(summary = "获取目录属性集下详细属性", description = "获取目录属性集下详细属性")
    @GetMapping("/getAttributeSetCatalogueData")
    @LogRecord(
        type = "目录属性",
        subType = "获取目录属性集下详细属性",
        bizNo = "",
        success = "获取目录属性集下详细属性成功",
        fail = "获取目录属性集下详细属性失败",
        extra = "执行参数：{{#catalogueId}}"
    )
    public AjaxResult getAttributeSetCatalogueData(@RequestParam String catalogueId) {
        return AjaxResult.success(catalogueAttributeService.getAttributeSetCatalogueData(catalogueId));
    }

    /**
     * 修改目录属性
     * @param catalogueAttribute
     * @return
     */
    @Operation(summary = "修改目录属性", description = "修改目录属性")
    @PostMapping("/updateCatalogueAttribute")
    @LogRecord(
        type = "目录属性",
        subType = "修改目录属性",
        bizNo = "",
        success = "修改目录属性成功",
        fail = "修改目录属性失败",
        extra = "执行参数：{{#catalogueAttribute}}"
    )
    public AjaxResult updateCatalogueAttribute(@RequestBody CatalogueAttributeEntity catalogueAttribute) {
        boolean row = catalogueAttributeService.updateCatalogueAttribute(catalogueAttribute);
        if (row) {
            return AjaxResult.success(row);
        } else {
            return AjaxResult.error();
        }
    }

    /**
     * 根据id删除目录属性
     * @param id
     * @return
     */
    @Operation(summary = "删除目录属性", description = "删除目录属性")
    @DeleteMapping("deleteCatalogueAttribute")
    @LogRecord(
        type = "目录属性",
        subType = "删除目录属性",
        bizNo = "",
        success = "删除目录属性成功",
        fail = "删除目录属性失败",
        extra = "执行参数：{{#id}}"
    )
    public AjaxResult deleteCatalogueAttribute(@RequestParam String id) {
        return AjaxResult.success(catalogueAttributeService.deleteCatalogueAttribute(id));
    }
}
