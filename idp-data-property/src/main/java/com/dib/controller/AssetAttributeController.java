package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.entity.AssetAttributeSetEntity;
import com.dib.service.AssetAttributeService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 资产
 */
@Tag(name = "资产属性数据")
@RestController
@RequestMapping("/assetAttribute")
public class AssetAttributeController {

    @Autowired
    private AssetAttributeService assetAttributeService;

    // 读取所有
    @LogRecord(
            type = "资产属性",
            subType = "查询资产下所有属性",
            bizNo = "",
            success = "查询资产下所有属性成功",
            fail = "查询资产下所有属性失败",
            extra = "执行参数：{{#assetAttributeSetEntity}}"
    )
    @Operation(summary = "查询资产下所有属性", description = "查询资产下所有属性")
    @PostMapping("/queryAssetAttributeData")
    public AjaxResult queryAssetAttributeData(@RequestBody AssetAttributeSetEntity assetAttributeSetEntity) {
        return AjaxResult.success(assetAttributeService.queryAssetAttributeData(assetAttributeSetEntity));
    }

    // 查询信息项属性
    @LogRecord(
            type = "资产属性",
            subType = "查询资产下信息项属性",
            bizNo = "",
            success = "查询资产下信息项属性成功",
            fail = "查询资产下信息项属性失败",
            extra = "执行参数：{{#assetAttributeSetEntity}}"
    )
    @Operation(summary = "查询资产下信息项属性", description = "查询资产下信息项属性")
    @PostMapping("/queryInfoAttribute")
    public AjaxResult queryInfoAttribute(@RequestBody AssetAttributeSetEntity assetAttributeSetEntity) {
        return AjaxResult.success(assetAttributeService.queryInfoAttribute(assetAttributeSetEntity));
    }

    // 查询挂接属性
    @LogRecord(
            type = "资产属性",
            subType = "查询资产下挂接属性",
            bizNo = "",
            success = "查询资产下挂接属性成功",
            fail = "查询资产下挂接属性失败",
            extra = "执行参数：{{#assetAttributeSetEntity}}"
    )
    @Operation(summary = "查询资产下所有挂接属性", description = "查询资产下所有挂接属性")
    @PostMapping("/queryMountAssetAttributeDataList")
    public AjaxResult queryMountAssetAttributeDataList(@RequestBody AssetAttributeSetEntity assetAttributeSetEntity) {
        return AjaxResult.success(assetAttributeService.queryMountAssetAttributeDataList(assetAttributeSetEntity));
    }
}
