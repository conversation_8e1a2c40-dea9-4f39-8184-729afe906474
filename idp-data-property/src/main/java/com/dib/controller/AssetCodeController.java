package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.entity.AssetCodeEntity;
import com.dib.service.AssetCodeService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 编码规则
 */
@Tag(name = "资产编码")
@RestController
@RequestMapping("/assetCode")
public class AssetCodeController {

    @Autowired
    private AssetCodeService assetCodeService;

    // 创建（Create）
    @Operation(summary = "创建资产编码", description = "创建资产编码")
    @PostMapping("/createAssetCode")
    @LogRecord(
        type = "资产编码",
        subType = "创建资产编码",
        bizNo = "",
        success = "创建资产编码成功",
        fail = "创建资产编码失败",
        extra = "执行参数：{{#assetCodeEntities}}"
    )
    public AjaxResult createAssetCode(@RequestBody List<AssetCodeEntity> assetCodeEntities) {
        return AjaxResult.success(assetCodeService.createAssetCode(assetCodeEntities));
    }

    // 读取目录下所有资产编码（Read One）
    @Operation(summary = "根据目录id获取资产编码", description = "根据目录id获取资产编码")
    @GetMapping("/getAssetCodeByCatalogueId")
    @LogRecord(
        type = "资产编码",
        subType = "根据目录id获取资产编码",
        bizNo = "",
        success = "根据目录id获取资产编码成功",
        fail = "根据目录id获取资产编码失败",
        extra = "执行参数：{{#catalogueId}}"
    )
    public AjaxResult getAssetCodeByCatalogueId(@RequestParam String catalogueId) {
        return AjaxResult.success(assetCodeService.getAssetCodeByCatalogueId(catalogueId));
    }

    // 读取目录下所有资产编码（Read One）
    @Operation(summary = "预览资产编码", description = "预览资产编码")
    @PostMapping("/getPreviewCode")
    @LogRecord(
        type = "资产编码",
        subType = "预览资产编码",
        bizNo = "",
        success = "预览资产编码成功",
        fail = "预览资产编码失败",
        extra = "执行参数：{{#assetCodeEntities}}"
    )
    public AjaxResult getPreviewCode(@RequestBody List<AssetCodeEntity> assetCodeEntities) {
        return AjaxResult.success("预览生成成功", assetCodeService.getPreviewCode(assetCodeEntities));
    }
}
