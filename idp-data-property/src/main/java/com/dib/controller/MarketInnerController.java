package com.dib.controller;

import cn.hutool.core.map.MapUtil;
import com.aspose.words.net.System.Data.DataException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.core.database.core.DataConstant;
import com.dib.core.database.core.PageResult;
import com.dib.market.entity.ApiMaskEntity;
import com.dib.market.entity.DataApiEntity;
import com.dib.market.handler.MappingHandlerMapping;
import com.dib.market.handler.RequestHandler;
import com.dib.market.service.ApiMaskService;
import com.dib.market.service.DataApiService;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 内部市场 API 控制器
 */
@RestController
@RequestMapping("/inner")
public class MarketInnerController extends BaseController {

    @Autowired
    private DataApiService dataApiService;

    @Autowired
    private ApiMaskService apiMaskService;

    @Autowired
    private RequestHandler requestHandler;

    /**
     * 获取数据API详情
     */
    @GetMapping("/apis/{id}")
    @LogRecord(
        type = "数据API",
        subType = "获取数据API详情",
        bizNo = "",
        success = "获取数据API详情成功",
        fail = "获取数据API详情失败",
        extra = "执行参数：{{#id}}"
    )
    public DataApiEntity getDataApiById(@PathVariable("id") String id) {
        return dataApiService.getDataApiById(id);
    }

    /**
     * 获取已发布API列表
     */
    @GetMapping("/apis/release/list")
    @LogRecord(
        type = "数据API",
        subType = "获取已发布API列表",
        bizNo = "",
        success = "获取已发布API列表成功",
        fail = "获取已发布API列表失败",
        extra = ""
    )
    public List<DataApiEntity> getReleaseDataApiList() {
        QueryWrapper<DataApiEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConstant.ApiState.RELEASE.getKey());
        return dataApiService.list(queryWrapper);
    }

    /**
     * 获取API掩码信息
     */
    @GetMapping("/apiMasks/api/{id}")
    @LogRecord(
        type = "API掩码",
        subType = "获取API掩码信息",
        bizNo = "",
        success = "获取API掩码信息成功",
        fail = "获取API掩码信息失败",
        extra = "执行参数：{{#id}}"
    )
    public ApiMaskEntity getApiMaskByApiId(@PathVariable("id") String id) {
        return apiMaskService.getApiMaskByApiId(id);
    }

    /**
     * GET 请求处理
     */
    @SneakyThrows
    @ResponseBody
    @GetMapping("/data/api")
    @LogRecord(
        type = "数据API请求",
        subType = "GET请求处理",
        bizNo = "",
        success = "GET请求处理成功",
        fail = "GET请求处理失败",
        extra = "执行参数：{{#params}}"
    )
    public Object getApiMaskByApiId(HttpServletRequest request, HttpServletResponse response,
                                   @PathVariable(required = false) Map<String, Object> pathVariables,
                                   @RequestParam(required = false) Map<String, Object> requestParams,
                                   @RequestBody(required = false) Map<String, Object> requestBodys) {

        DataApiEntity api;
        Map<String, Object> params = new HashMap<>();
        if (MapUtil.isNotEmpty(pathVariables)) {
            params.putAll(pathVariables);
        }
        if (MapUtil.isNotEmpty(requestParams)) {
            params.putAll(requestParams);
        }
        if (MapUtil.isNotEmpty(requestBodys)) {
            params.putAll(requestBodys);
        }
        api = MappingHandlerMapping.getMappingApiInfoA(params.get("apiCode").toString());
        params.remove("apiCode");
        if(null == api){
            throw new DataException("api接口未发布或不存在!");
        }
        api = requestHandler.getObjectMapper().readValue(requestHandler.getObjectMapper().writeValueAsString(api), DataApiEntity.class);
        requestHandler.getRequestInterceptor().preHandle(request, response, api, params);
        PageResult<Map<String, Object>> value = requestHandler.getApiMappingEngine().execute(api, params);
        requestHandler.getRequestInterceptor().postHandle(request, response, api, params, value);
        return AjaxResult.success(value);
    }

    /**
     * POST 请求处理
     */
    @SneakyThrows
    @ResponseBody
    @PostMapping("/data/api")
    @LogRecord(
        type = "数据API请求",
        subType = "POST请求处理",
        bizNo = "",
        success = "POST请求处理成功",
        fail = "POST请求处理失败",
        extra = "执行参数：{{#params}}"
    )
    public Object postApiMaskByApiId(HttpServletRequest request, HttpServletResponse response,
                                    @PathVariable(required = false) Map<String, Object> pathVariables,
                                    @RequestParam(required = false) Map<String, Object> requestParams,
                                    @RequestBody(required = false) Map<String, Object> requestBodys) {

        DataApiEntity api;
        Map<String, Object> params = new HashMap<>();
        if (MapUtil.isNotEmpty(pathVariables)) {
            params.putAll(pathVariables);
        }
        if (MapUtil.isNotEmpty(requestParams)) {
            params.putAll(requestParams);
        }
        if (MapUtil.isNotEmpty(requestBodys)) {
            params.putAll(requestBodys);
        }
        api = MappingHandlerMapping.getMappingApiInfoA(params.get("apiCode").toString());
        params.remove("apiCode");
        if(null == api){
            throw new DataException("api接口未发布或不存在!");
        }
        api = requestHandler.getObjectMapper().readValue(requestHandler.getObjectMapper().writeValueAsString(api), DataApiEntity.class);
        requestHandler.getRequestInterceptor().preHandle(request, response, api, params);
        PageResult<Map<String, Object>> value = requestHandler.getApiMappingEngine().execute(api, params);
        requestHandler.getRequestInterceptor().postHandle(request, response, api, params, value);
        return AjaxResult.success(value);
    }
}
