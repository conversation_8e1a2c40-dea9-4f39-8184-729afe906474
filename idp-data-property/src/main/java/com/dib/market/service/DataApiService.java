package com.dib.market.service;


import com.aspose.words.Document;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dib.market.dto.DataApiDto;
import com.dib.market.dto.SqlParseDto;
import com.dib.market.entity.DataApiEntity;
import com.dib.market.vo.SqlParseVo;
import net.sf.jsqlparser.JSQLParserException;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据API信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-31
 */
public interface DataApiService extends IService<DataApiEntity> {

    void saveDataApi(DataApiDto dataApi);

    void updateDataApi(DataApiDto dataApi);

    DataApiEntity getDataApiById(String id);

    void deleteDataApiById(String id);

    void deleteDataApiBatch(List<String> ids);

    SqlParseVo sqlParse(SqlParseDto sqlParseDto) throws SQLException, JSQLParserException;

    void copyDataApi(String id);

    void releaseDataApi(String id);

    void cancelDataApi(String id);

    Document wordDataApi(String id) throws Exception;

    Map<String, Object> getDataApiDetailById(String id);

    List<DataApiEntity> getDataApiEntityList(String status);

    List<DataApiEntity> getUnboundAssetDataApi(String assetAttributeSetId);
}
