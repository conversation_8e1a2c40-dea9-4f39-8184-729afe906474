package com.dib.controller;

import com.mzt.logapi.starter.annotation.LogRecord;
import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.entity.TableRelation;
import com.dib.service.TableRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Tag(name = "数仓集市-关联关系")
@RestController
@RequestMapping("/api/tableRelation")
public class TableRelationController {

    @Autowired
    private TableRelationService tableRelationService;

    @LogRecord(
            type = "数仓集市-关联关系",
            subType = "创建表关系",
            bizNo = "",
            success = "创建表关系成功",
            fail = "创建表关系失败",
            extra = "执行参数：{{#tableRelation}}"
    )
    @Operation(summary = "创建表关系")
    @PostMapping("/createTableRelation")
    public AjaxResult createTableRelation(@RequestBody TableRelation tableRelation) {
        return AjaxResult.success(tableRelationService.createTableRelation(tableRelation));
    }

    @LogRecord(
            type = "数仓集市-关联关系",
            subType = "删除表关系",
            bizNo = "",
            success = "删除表关系成功",
            fail = "删除表关系失败",
            extra = "执行参数：id={{#id}}"
    )
    @Operation(summary = "根据ID删除表之间的关系")
    @PostMapping("/delTableRelationById")
    public AjaxResult delTableRelationById(@RequestParam String id) {
        return AjaxResult.success(tableRelationService.delTableRelation(id));
    }

    @LogRecord(
            type = "数仓集市-关联关系",
            subType = "查看属性关联关系",
            bizNo = "",
            success = "查看属性关联关系成功",
            fail = "查看属性关联关系失败",
            extra = "执行参数：{{#tableRelation}}"
    )
    @Operation(summary = "查看属性关联关系", description = "查看属性关联关系")
    @PostMapping("/selectTableColumnRelation")
    public AjaxResult selectTableColumnRelation(@RequestBody TableRelation tableRelation) {
        return AjaxResult.success(tableRelationService.selectTableColumnRelation(tableRelation));
    }

    @LogRecord(
            type = "数仓集市-关联关系",
            subType = "修改表关系",
            bizNo = "",
            success = "修改表关系成功",
            fail = "修改表关系失败",
            extra = "执行参数：{{#tableRelation}}"
    )
    @Operation(summary = "根据id修改表关系")
    @PostMapping("/updateTableRelation")
    public AjaxResult updateTableRelation(@RequestBody TableRelation tableRelation) {
        return AjaxResult.success(tableRelationService.updateTableRelation(tableRelation));
    }

}
