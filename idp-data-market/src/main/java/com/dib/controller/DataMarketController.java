package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.dto.DataSetAndColumnAndIndexDto;
import com.dib.domin.entity.DataSetEntity;
import com.dib.service.DataMarketService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "数仓集市")
@RestController
@RequestMapping("/api/dataMarket")
public class DataMarketController {

    @Autowired
    private DataMarketService dataMarketService;

    @LogRecord(
            type = "数仓集市",
            subType = "新增数据集",
            bizNo = "",
            success = "新增数据集成功",
            fail = "新增数据集失败",
            extra = "执行参数：{{#dataSetAndColumnAndIndexDto}}"
    )
    @Operation(summary = "新增数据集", description = "新增数据集")
    @PostMapping("saveDataSet")
    public AjaxResult saveDataSet(@RequestBody DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto) {
        return AjaxResult.success(dataMarketService.saveDataSetDirectory(dataSetAndColumnAndIndexDto));
    }

    @LogRecord(
            type = "数仓集市",
            subType = "获取SQL",
            bizNo = "",
            success = "获取SQL成功",
            fail = "获取SQL失败",
            extra = "执行参数：{{#dataSetAndColumnAndIndexDto}}"
    )
    @Operation(summary = "获取sql", description = "根据id修改数据集")
    @PostMapping("getCreateSql")
    public AjaxResult getCreateSql(@RequestBody DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto) {
        String createSql = dataMarketService.getCreateSql(dataSetAndColumnAndIndexDto);
        if (StringUtils.isNotBlank(createSql)) {
            return AjaxResult.success("创建sql成功", createSql);
        } else {
            return AjaxResult.error("创建sql失败");
        }
    }

    @LogRecord(
            type = "数仓集市",
            subType = "修改数据集",
            bizNo = "",
            success = "修改数据集成功",
            fail = "修改数据集失败",
            extra = "执行参数：{{#dataSetAndColumnAndIndexDto}}"
    )
    @Operation(summary = "修改数据集", description = "根据id修改数据集")
    @PostMapping("updateDataSet")
    public AjaxResult updateDataSet(@RequestBody DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto) {
        if (dataSetAndColumnAndIndexDto.getDataHierarchy().equals("ODS")) {
            return AjaxResult.error("ODS层不支持修改！！！");
        }
        return AjaxResult.success(dataMarketService.updateDataSetDirectory(dataSetAndColumnAndIndexDto));
    }

    @LogRecord(
            type = "数仓集市",
            subType = "删除数据集",
            bizNo = "",
            success = "删除数据集成功",
            fail = "删除数据集失败",
            extra = "执行参数：{{#ids}}"
    )
    @Operation(summary = "删除数据集", description = "根据id删除数据集")
    @PostMapping("deleteDataSet")
    public AjaxResult deleteDataSet(@RequestBody List<String> ids) {
        return AjaxResult.success(dataMarketService.deleteDataSetDirectory(ids));
    }

    @LogRecord(
            type = "数仓集市",
            subType = "获取目录树",
            bizNo = "",
            success = "获取目录树成功",
            fail = "获取目录树失败",
            extra = "执行参数：{{#dataSetEntity}}"
    )
    @Operation(summary = "获取目录树", description = "获取目录树")
    @PostMapping("getTreeDataSet")
    public AjaxResult getTreeDataSet(@RequestBody DataSetEntity dataSetEntity) {
        return AjaxResult.success(dataMarketService.getTreeDataSet(dataSetEntity, null));
    }

    @LogRecord(
            type = "数仓集市",
            subType = "获取数据层下数据集",
            bizNo = "",
            success = "获取数据层下数据集成功",
            fail = "获取数据层下数据集失败",
            extra = "执行参数：{{#dataSetEntity}}"
    )
    @Operation(summary = "获取数据层下所有数据集", description = "获取数据层下所有数据集")
    @PostMapping("getDataLayerDataSet")
    public AjaxResult getDataLayerDataSet(@RequestBody DataSetEntity dataSetEntity) {
        return AjaxResult.success(dataMarketService.getDataLayerDataSet(dataSetEntity, null));
    }

    @LogRecord(
            type = "数仓集市",
            subType = "获取数据集详情",
            bizNo = "",
            success = "获取数据集详情成功",
            fail = "获取数据集详情失败",
            extra = "执行参数：id={{#id}}"
    )
    @Operation(summary = "获取事实表详细信息", description = "获取事实表详细信息")
    @PostMapping("getDataSet")
    public AjaxResult getDataSet(@RequestParam String id) {
        return AjaxResult.success(dataMarketService.getDataSet(id));
    }

    @LogRecord(
            type = "数仓集市",
            subType = "查询关联关系",
            bizNo = "",
            success = "查询关联关系成功",
            fail = "查询关联关系失败",
            extra = "执行参数：{{#dataSetEntity}}"
    )
    @Operation(summary = "获取数据集下关联关系", description = "获取数据集下关联关系")
    @PostMapping("queryRelatedRelationships")
    public AjaxResult queryRelatedRelationships(@RequestBody DataSetEntity dataSetEntity) {
        return AjaxResult.success(dataMarketService.queryRelatedRelationships(dataSetEntity));
    }


}
