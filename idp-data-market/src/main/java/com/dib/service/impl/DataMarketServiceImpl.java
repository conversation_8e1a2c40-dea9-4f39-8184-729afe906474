package com.dib.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.domin.dto.*;
import com.dib.domin.entity.DataSetEntity;
import com.dib.domin.entity.TableRelation;
import com.dib.domin.enums.DataHierarchy;
import com.dib.mapper.DataSetMapper;
import com.dib.mapper.TableRelationMapper;
import com.dib.metadata.dto.MetadataColumnMarketDto;
import com.dib.metadata.dto.MetadataCreateSqlDto;
import com.dib.metadata.dto.MetadataIndexMarketDto;
import com.dib.metadata.entity.MetadataColumnEntity;
import com.dib.metadata.entity.MetadataIndexEntity;
import com.dib.metadata.entity.MetadataSourceEntity;
import com.dib.metadata.entity.MetadataTableEntity;
import com.dib.metadata.mapper.MetadataSourceDao;
import com.dib.metadata.mapper.MetadataTableDao;
import com.dib.metadata.service.MetadataColumnService;
import com.dib.metadata.service.MetadataCreateDataBaseAndTableService;
import com.dib.metadata.service.MetadataIndexService;
import com.dib.service.DataMarketService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.datanucleus.store.rdbms.exceptions.NullValueException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.dib.common.database.utils.SecurityUtil.getUserName;

@Slf4j
@Service
@Transactional
public class DataMarketServiceImpl extends ServiceImpl<DataSetMapper, DataSetEntity> implements DataMarketService {


    @Autowired
    private TableRelationMapper tableRelationMapper;
    @Autowired
    private MetadataCreateDataBaseAndTableService metadataCreateTableService;
    @Autowired
    private MetadataColumnService metadataColumnService;
    @Autowired
    private MetadataSourceDao metadataSourceDao;
    @Autowired
    private MetadataTableDao metadataTableDao;
    @Autowired
    private MetadataIndexService metadataIndexService;

    @Override
    public String getCreateSql(DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto) {
        MetadataCreateSqlDto metadataCreateSqlDto = getMetadataCreateSqlDto(dataSetAndColumnAndIndexDto);

        return metadataCreateTableService.createSql(metadataCreateSqlDto);
    }

    @Override
    public boolean updateDataSetList(List<DataSetAndColumnAndIndexDto> dataSetAndColumnAndIndexDtoList) {
        List<DataSetEntity> dataSetEntityList = new ArrayList<>();
        for (DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto : dataSetAndColumnAndIndexDtoList) {
            DataSetEntity dataSetEntity = new DataSetEntity();
            BeanUtils.copyProperties(dataSetAndColumnAndIndexDto, dataSetEntity);
            dataSetEntityList.add(dataSetEntity);
        }
        return this.updateBatchById(dataSetEntityList);
    }

    public MetadataCreateSqlDto getMetadataCreateSqlDto(DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto) {
        MetadataCreateSqlDto metadataCreateTableDto = new MetadataCreateSqlDto();

        metadataCreateTableDto.setMetadataIndexMarketDtoList(dataSetAndColumnAndIndexDto.getMetadataIndexMarketDtoList());
        metadataCreateTableDto.setMetadataColumnMarketDtoList(dataSetAndColumnAndIndexDto.getMetadataColumnDtoList());

        Map<String, String> param = new HashMap<>();

        if (StringUtils.isNotEmpty(dataSetAndColumnAndIndexDto.getDataSourceId())) {
            MetadataSourceEntity metadataSourceEntity = metadataSourceDao.selectById(dataSetAndColumnAndIndexDto.getDataSourceId());
            if (Objects.isNull(metadataSourceEntity)) {
                return null;
            }
            metadataCreateTableDto.setTargetId(metadataSourceEntity.getId());
            metadataCreateTableDto.setTargetSchemaName(metadataSourceEntity.getDbSchema().getSid());
            metadataCreateTableDto.setTargetTableName(dataSetAndColumnAndIndexDto.getMetaTableName());
            metadataCreateTableDto.setSourceTableRemarks(StringUtils.isEmpty(dataSetAndColumnAndIndexDto.getMetaTableDescribe()) ? dataSetAndColumnAndIndexDto.getName() : dataSetAndColumnAndIndexDto.getMetaTableDescribe());
        }
        if (StringUtils.isNotBlank(dataSetAndColumnAndIndexDto.getPrimaryKeySchema())){
            param.put("primaryKeySchema", dataSetAndColumnAndIndexDto.getPrimaryKeySchema());
        }
        metadataCreateTableDto.setParam(param);

        return metadataCreateTableDto;
    }


    @Override
    public boolean saveDataSetDirectory(DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto) {
        DataSetEntity dataSetEntity = new DataSetEntity();
        BeanUtils.copyProperties(dataSetAndColumnAndIndexDto, dataSetEntity);
        if (StringUtils.isBlank(dataSetEntity.getParentId())) {
            dataSetEntity.setParentId("0");
        }

        //创建表时，没有MetaTableId
        if (dataSetEntity.getMetaTableId() == null && "RealTable".equals(dataSetEntity.getType())) {
            if (dataSetAndColumnAndIndexDto.getMetadataColumnDtoList() == null) {
                throw new NullValueException("字段不能为空");
            }
            if (dataSetAndColumnAndIndexDto.getMetaTableId() != null) {
                log.info("当前选择现有表");
            } else {
                log.info("开始生成建表sql");
                MetadataCreateSqlDto metadataCreateSqlDto = getMetadataCreateSqlDto(dataSetAndColumnAndIndexDto);
                metadataCreateSqlDto.setSqlType("createTable");

                String createSql = metadataCreateTableService.createSql(metadataCreateSqlDto);
                if (StringUtils.isNotBlank(createSql) && metadataCreateSqlDto != null) {
                    metadataCreateSqlDto.setTableSql(createSql);
                    log.info("开始执行建表sql");
                    String tableId = metadataCreateTableService.runSql(metadataCreateSqlDto);
                    dataSetEntity.setMetaTableId(tableId);
                }
            }
        }else {
            List<MetadataColumnMarketDto> metadataColumnDtoList = dataSetAndColumnAndIndexDto.getMetadataColumnDtoList();
            List<MetadataColumnEntity> metadataColumnEntities = new ArrayList<>();

            if (CollectionUtil.isNotEmpty(metadataColumnDtoList)) {
                for (MetadataColumnMarketDto metadataColumnMarketDto : metadataColumnDtoList) {
                    MetadataColumnEntity metadataColumnEntity = new MetadataColumnEntity();
                    metadataColumnEntity.setId(metadataColumnMarketDto.getId());
                    metadataColumnEntity.setColumnComment(metadataColumnMarketDto.getColumnComment());
                    metadataColumnEntities.add(metadataColumnEntity);
                }
            }
            metadataColumnService.updateBatchById(metadataColumnEntities);
        }


        dataSetEntity.setCreateBy(getUserName());
        dataSetEntity.setUpdateBy(getUserName());

        return this.save(dataSetEntity);
    }

    @Override
    public boolean updateDataSetDirectory(DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto) {
        DataSetEntity dataSetEntity = new DataSetEntity();
        BeanUtils.copyProperties(dataSetAndColumnAndIndexDto, dataSetEntity);
        dataSetEntity.setUpdateBy(getUserName());

        List<MetadataColumnEntity> metadataColumnEntities = new ArrayList<>();
        List<MetadataIndexEntity> metadataIndexEntities = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(dataSetAndColumnAndIndexDto.getMetadataColumnDtoList())){
            metadataColumnEntities = dataSetAndColumnAndIndexDto.getMetadataColumnDtoList().stream().filter(metadataColumnMarketDto -> StringUtils.isNotBlank(metadataColumnMarketDto.getId()))
                    .filter(metadataColumnMarketDto -> StringUtils.isNotBlank(metadataColumnMarketDto.getActionType()))
                    .map(metadataColumn -> {
                        MetadataColumnEntity metadataColumnEntity = new MetadataColumnEntity();
                        BeanUtils.copyProperties(metadataColumn, metadataColumnEntity);
                        return metadataColumnEntity;
                    })
                    .collect(Collectors.toList());
        }

        if (CollectionUtil.isNotEmpty(dataSetAndColumnAndIndexDto.getMetadataIndexMarketDtoList())){
            metadataIndexEntities = dataSetAndColumnAndIndexDto.getMetadataIndexMarketDtoList().stream().filter(metadataIndexMarketDto -> StringUtils.isNotBlank(metadataIndexMarketDto.getId()))
                    .filter(metadataIndexMarketDto -> StringUtils.isNotBlank(metadataIndexMarketDto.getActionType()))
                    .map(metadataIndex -> {
                        MetadataIndexEntity metadataIndexEntity = new MetadataIndexEntity();
                        BeanUtils.copyProperties(metadataIndex, metadataIndexEntity);
                        return metadataIndexEntity;
                    })
                    .collect(Collectors.toList());
        }


        if ("RealTable".equals(dataSetEntity.getType())) {
            if (dataSetAndColumnAndIndexDto.isUpdateDatabase()) {
                log.info("开始生成修改sql");
                MetadataCreateSqlDto metadataCreateSqlDto = getMetadataCreateSqlDto(dataSetAndColumnAndIndexDto);
                metadataCreateSqlDto.setSqlType("updateTable");

                String tableSql = metadataCreateTableService.createSql(metadataCreateSqlDto);
                metadataCreateSqlDto.setTableSql(tableSql);

                //先修改表字段信息，后同步再次修正
                if (CollectionUtil.isNotEmpty(metadataColumnEntities)) {
                    metadataColumnService.updateBatchById(metadataColumnEntities);
                }
                //先修改表索引信息，后同步再次修正
                if (CollectionUtil.isNotEmpty(metadataIndexEntities)) {
                    metadataIndexService.updateBatchById(metadataIndexEntities);
                }
                log.info("开始执行修改sql");
                String tableId = metadataCreateTableService.runSql(metadataCreateSqlDto);
            } else {
                if (CollectionUtil.isNotEmpty(metadataColumnEntities)) {
                    metadataColumnService.updateBatchById(metadataColumnEntities);
                }
                if (CollectionUtil.isNotEmpty(metadataIndexEntities)) {
                    metadataIndexService.updateBatchById(metadataIndexEntities);
                }
            }
        }


        return this.updateById(dataSetEntity);
    }

    @Override
    public boolean deleteDataSetDirectory(List<String> ids) {
        List<String> idsToDelete = new ArrayList<>();
        for (String id : ids) {
            collectIdsToDelete(id, idsToDelete);
        }

        log.info("删除数据集表及关联关系");
        //删除关联关系
        LambdaQueryWrapper<TableRelation> lqw = new LambdaQueryWrapper<>();
        lqw.in(TableRelation::getDataSetId, idsToDelete).or().in(TableRelation::getTargetDataSetId,idsToDelete);
        tableRelationMapper.delete(lqw);

        return this.removeByIds(idsToDelete);
    }

    private void collectIdsToDelete(String id, List<String> idsToDelete) {
        idsToDelete.add(id);
        List<DataSetEntity> children = getChildrenById(id);
        for (DataSetEntity child : children) {
            collectIdsToDelete(child.getId(), idsToDelete);
        }
    }

    private List<DataSetEntity> getChildrenById(String parentId) {
        LambdaQueryWrapper<DataSetEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(DataSetEntity::getParentId, parentId);
        return this.list(lqw);
    }

    @Override
    public List<DataSetDto> getTreeDataSet(DataSetEntity dataSetEntity, String parentId) {
        parentId = parentId == null ? "0" : parentId;
        LambdaQueryWrapper<DataSetEntity> lqw = new LambdaQueryWrapper<>();
        if (!Objects.equals(dataSetEntity.getDataHierarchy(), DataHierarchy.DIM.getCode())){
            lqw.eq(DataSetEntity::getProjectId, dataSetEntity.getProjectId());
            lqw.eq(DataSetEntity::getMetaSourceId, dataSetEntity.getMetaSourceId());
        }
        lqw.eq(DataSetEntity::getDataHierarchy, dataSetEntity.getDataHierarchy());

        List<DataSetEntity> list = this.list(lqw);

        Map<String, DataSetDto> map = new HashMap<>();
        List<DataSetDto> rootNodes = new ArrayList<>();

        for (DataSetEntity dataSetDir : list) {
            DataSetDto dto = new DataSetDto(dataSetDir);
            map.put(dto.getId(), dto);
            if (parentId.equals(dto.getParentId())) {
                rootNodes.add(dto);
            }
        }
        // 使用 buildTree 方法构建树形结构
        for (DataSetDto rootNode : rootNodes) {
            buildTree(rootNode, map);
        }

        return rootNodes;
    }

    @Override
    public DataSetAndColumnAndIndexDto getDataSet(String id) {
        log.info("获取数据表详细信息");
        DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto = new DataSetAndColumnAndIndexDto();
        DataSetEntity dataSetEntity = this.getById(id);

        if (!Objects.isNull(dataSetEntity)) {
            DataSetEntity parentDataSetEntity = this.getById(dataSetEntity.getParentId());
            if (!Objects.isNull(parentDataSetEntity)) {
                dataSetAndColumnAndIndexDto.setParentName(parentDataSetEntity.getName());
            }

            BeanUtils.copyProperties(dataSetEntity, dataSetAndColumnAndIndexDto);
            LambdaQueryWrapper<MetadataColumnEntity> lqw = new LambdaQueryWrapper<>();
            lqw.eq(MetadataColumnEntity::getTableId, dataSetEntity.getMetaTableId());
            List<MetadataColumnEntity> metadataColumnEntities = metadataColumnService.list(lqw);
            if (CollectionUtil.isNotEmpty(metadataColumnEntities)) {
                Set<String> sourceDataIds = metadataColumnEntities.stream().map(MetadataColumnEntity::getSourceDataId).collect(Collectors.toSet());
                Set<String> sourceTableIds = metadataColumnEntities.stream().map(MetadataColumnEntity::getSourceTableId).collect(Collectors.toSet());
                Set<String> sourceColumnIds = metadataColumnEntities.stream().map(MetadataColumnEntity::getSourceColumnId).collect(Collectors.toSet());
                List<MetadataSourceEntity> sourceMetadataSourceEntities = metadataSourceDao.selectBatchIds(sourceDataIds);
                List<MetadataTableEntity> sourceMetadataTableEntities = metadataTableDao.selectBatchIds(sourceTableIds);
                List<MetadataColumnEntity> sourceMetadataColumnEntities = metadataColumnService.listByIds(sourceColumnIds);
                Map<String, String> sourceMetadataNameById = new HashMap<>();
                Map<String, String> sourceTableNameById = new HashMap<>();
                Map<String, String> sourceColumnNameById = new HashMap<>();
                if (CollectionUtil.isNotEmpty(sourceMetadataSourceEntities)) {
                    sourceMetadataNameById = sourceMetadataSourceEntities.stream().collect(Collectors.toMap(MetadataSourceEntity::getId, MetadataSourceEntity::getSourceName));
                }
                if (CollectionUtil.isNotEmpty(sourceMetadataTableEntities)) {
                    sourceTableNameById = sourceMetadataTableEntities.stream().collect(Collectors.toMap(MetadataTableEntity::getId, MetadataTableEntity::getTableName));
                }
                if (CollectionUtil.isNotEmpty(sourceMetadataColumnEntities)) {
                    sourceColumnNameById = sourceMetadataColumnEntities.stream().collect(Collectors.toMap(MetadataColumnEntity::getId, MetadataColumnEntity::getColumnName));
                }
                List<MetadataColumnMarketDto> metadataColumnDtoList = new ArrayList<>();
                for (MetadataColumnEntity metadataColumnEntity : metadataColumnEntities) {
                    MetadataColumnMarketDto metadataColumnDto = new MetadataColumnMarketDto();
                    BeanUtils.copyProperties(metadataColumnEntity, metadataColumnDto);
                    String dataSourcePath = sourceMetadataNameById.get(metadataColumnEntity.getSourceDataId()) + "-" +
                            sourceTableNameById.get(metadataColumnEntity.getSourceTableId()) + "-" +
                            sourceColumnNameById.get(metadataColumnEntity.getSourceColumnId());
                    metadataColumnDto.setDataSourcePath(dataSourcePath.replace("null", " "));
                    metadataColumnDtoList.add(metadataColumnDto);
                }

                dataSetAndColumnAndIndexDto.setMetadataColumnDtoList(metadataColumnDtoList);
            }

            LambdaQueryWrapper<MetadataIndexEntity> indexLqw = new LambdaQueryWrapper<>();
            indexLqw.eq(MetadataIndexEntity::getTableId, dataSetEntity.getMetaTableId());
            List<MetadataIndexEntity> metadataIndexEntities = metadataIndexService.list(indexLqw);
            // 将 MetadataIndexEntity 转换为 MetadataIndexMarketDto 列表
            if (CollectionUtil.isNotEmpty(metadataIndexEntities)) {
                List<MetadataIndexMarketDto> metadataIndexMarketDtoList = metadataIndexEntities.stream()
                        .map(metadataIndexEntity -> {
                            MetadataIndexMarketDto metadataIndexMarketDto = new MetadataIndexMarketDto();
                            BeanUtils.copyProperties(metadataIndexEntity, metadataIndexMarketDto);
                            return metadataIndexMarketDto;
                        })
                        .collect(Collectors.toList());
                dataSetAndColumnAndIndexDto.setMetadataIndexMarketDtoList(metadataIndexMarketDtoList);
            }


        }

        return dataSetAndColumnAndIndexDto;
    }

    private void buildTree(DataSetDto root, Map<String, DataSetDto> map) {
        for (DataSetDto child : map.values()) {
            if (child.getParentId().equals(root.getId())) {
                root.getChildrenData().add(child);
                buildTree(child, map);
            }
        }
    }

    private void buildTreeToTile(List<DataSetTileDataDto> dataSetAndTableRelationDtoList, List<DataSetDto> childrenData
            , List<String> dataSetIds, List<String> metaTableIds) {

        if (CollectionUtil.isEmpty(childrenData)) {
            return;
        }

        for (DataSetDto dataSetDto : childrenData) {
            DataSetTileDataDto dataSetAndTableRelationDto = new DataSetTileDataDto();

            //数据集
            if (dataSetDto.getType().equals("Folder")) {
                dataSetAndTableRelationDto.setType("Folder");

                if (CollectionUtil.isNotEmpty(dataSetDto.getChildrenData())) {
                    List<String> ids = dataSetDto.getChildrenData().stream().map(DataSetEntity::getId).collect(Collectors.toList());

                    buildTreeToTile(dataSetAndTableRelationDtoList, dataSetDto.getChildrenData(), dataSetIds, metaTableIds);

                    dataSetDto.setChildrenData(null);
                    dataSetAndTableRelationDto.setChildren(ids);
                }
            } else {
                dataSetAndTableRelationDto.setType("RealTable");
                dataSetIds.add(dataSetDto.getId());
                metaTableIds.add(dataSetDto.getMetaTableId());
            }
            dataSetAndTableRelationDto.setProperties(dataSetDto);

            dataSetAndTableRelationDtoList.add(dataSetAndTableRelationDto);
        }
    }

    /**
     * 根据数据集查询，数据集下使用表之间的关系
     *
     * @param dataSetEntity 用户名称
     * @return 菜单列表
     */
    @Override
    public Map<String, List<? extends Object>> queryRelatedRelationships(DataSetEntity dataSetEntity) {
//        Assert.isTrue(dataSetEntity.getType().equals("Folder"), "当前非数据集层");

        log.info("查询数据集下表及关联关系");


        Map<String, List<? extends Object>> result = new HashMap<>();

        //查询当前项目当前数据层当前父节点下所有数据
//        List<DataSetDto> treeDataSet = getTreeDataSet(dataSetEntity, dataSetEntity.getId());
        List<DataSetDto> treeDataSet = getTreeDataSet(dataSetEntity, null);

        // 收集所有表的 id
        List<String> dataSetIds = new ArrayList<>();
        List<String> metaTableIds = new ArrayList<>();
        //平铺树状结构
        List<DataSetTileDataDto> dataSetTileDataDtoList = new ArrayList<>();
        buildTreeToTile(dataSetTileDataDtoList, treeDataSet, dataSetIds, metaTableIds);

        if (CollectionUtil.isNotEmpty(dataSetTileDataDtoList)) {

            Map<String, List<MetadataColumnEntity>> metaColumnByTableIdMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(metaTableIds)) {
                LambdaQueryWrapper<MetadataColumnEntity> lqw = new LambdaQueryWrapper<>();
                lqw.in(MetadataColumnEntity::getTableId, metaTableIds);
                lqw.select(MetadataColumnEntity::getTableId, MetadataColumnEntity::getColumnName, MetadataColumnEntity::getColumnComment, MetadataColumnEntity::getColumnKey);
                List<MetadataColumnEntity> metadataColumnEntities = metadataColumnService.list(lqw);
                if (CollectionUtil.isNotEmpty(metadataColumnEntities)) {
                    metaColumnByTableIdMap = metadataColumnEntities.stream().collect(Collectors.groupingBy(
                            MetadataColumnEntity::getTableId));
                }
            }
            addRelationAndColumns(dataSetTileDataDtoList, metaColumnByTableIdMap);

            result.put("nodes", dataSetTileDataDtoList);


            List<TableRelationEdgesDto> tableRelationEdgesDtoList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(dataSetIds)) {
                LambdaQueryWrapper<TableRelation> lqw = new LambdaQueryWrapper<>();
                lqw.in(TableRelation::getDataSetId, dataSetIds);
                List<TableRelation> tableRelations = tableRelationMapper.selectList(lqw);
                if (CollectionUtil.isNotEmpty(tableRelations)) {
                    tableRelations.forEach(tableRelation -> {
                        TableRelationEdgesDto tableRelationEdgesDto = new TableRelationEdgesDto();
                        tableRelationEdgesDto.setTargetNodeId(tableRelation.getTargetDataSetId());
                        tableRelationEdgesDto.setSourceNodeId(tableRelation.getDataSetId());
                        tableRelationEdgesDto.setProperties(tableRelation);
                        tableRelationEdgesDtoList.add(tableRelationEdgesDto);
                    });
                }
            }
            result.put("edges", tableRelationEdgesDtoList);
        }


        return result;
    }

    @Override
    public List<DataSetEntity> getDataLayerDataSet(DataSetEntity dataSetEntity, String parentId) {
        LambdaQueryWrapper<DataSetEntity> lqw = new LambdaQueryWrapper<>();
        lqw.select(DataSetEntity::getId, DataSetEntity::getName);
        lqw.eq(DataSetEntity::getProjectId, dataSetEntity.getProjectId());
        lqw.eq(DataSetEntity::getDataHierarchy, dataSetEntity.getDataHierarchy());
        lqw.eq(DataSetEntity::getMetaSourceId, dataSetEntity.getMetaSourceId());
        lqw.eq(DataSetEntity::getType, "Folder");


        return this.list(lqw);
    }


    /**
     * 递归收集所有 type 等于 2 的节点的 id
     *
     * @param dataSetTileDataDtoList 树形结构的数据集
     * @param metaColumnByTableIdMap 表的字段
     */
    private void addRelationAndColumns(List<DataSetTileDataDto> dataSetTileDataDtoList, Map<String, List<MetadataColumnEntity>> metaColumnByTableIdMap) {
        for (DataSetTileDataDto dto : dataSetTileDataDtoList) {
            DataSetDto properties = dto.getProperties();
            if (!Objects.isNull(properties)) {
                if (MapUtil.isNotEmpty(metaColumnByTableIdMap)) {
                    properties.setMetaColumnIds(metaColumnByTableIdMap.get(properties.getMetaTableId()));
                }
            }
        }
    }


}
