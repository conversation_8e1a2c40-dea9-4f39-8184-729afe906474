package com.dib.common.database.query;

import java.util.function.Predicate;
import cn.hutool.core.collection.CollectionUtil;
import com.dib.common.annotation.Product;
import com.dib.common.database.constants.DbType;
import com.dib.common.database.schema.*;
import com.dib.common.database.service.DbDialect;
import com.dib.common.database.service.DbQuery;
import com.dib.common.database.utils.ExamineUtils;
import com.dib.core.database.core.DbColumn;
import com.dib.core.database.core.DbIndex;
import com.dib.core.database.core.DbTable;
import com.dib.core.database.core.PageResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;


import java.sql.*;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Data
public abstract class AbstractDbQueryFactory implements DbQuery {

    protected JdbcTemplate jdbcTemplate;

    protected DbDialect dbDialect;

    static {
        for (DbType value : DbType.values()) {
            if(StringUtils.isNotBlank(value.getJdbc())){
                try {
                    Class.forName(value.getJdbc());
                }catch (Exception e) {
                    log.error("数据库驱动{}加载失败",value.getJdbc(),e);
                }
            }
        }
    }


    @Override
    public final DbType getDbType() {
        Product annotation = getClass().getAnnotation(Product.class);
        ExamineUtils.checkState(
                Objects.nonNull(annotation),
                "Should use Product annotation for class : %s",
                getClass().getName());
        return annotation.value();
    }

    @Override
    public Connection getConnection() {
        try {
            return Objects.requireNonNull(jdbcTemplate.getDataSource()).getConnection();
        } catch (SQLException e) {
            throw new RuntimeException("获取数据库连接出错");
        }
    }

    @Override
    public boolean valid() {
        Connection conn = null;
        try {
            if (jdbcTemplate.getDataSource() != null){
                conn = jdbcTemplate.getDataSource().getConnection();
            }
            return conn.isValid(0);
        } catch (SQLException e) {
            throw new RuntimeException("检测连通性出错");
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    throw new RuntimeException("关闭数据库连接出错");
                }
            }
        }

    }

//    @Override
//    public void close() {
//        if (dataSource instanceof HikariDataSource) {
//            ((HikariDataSource) dataSource).close();
//        } else {
//            throw new RuntimeException("不合法数据源类型");
//        }
//    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    @Override
    public List<DbColumn> getTableColumns(DbType dbType,String dbName, String dbSchema,String tableName, String columnSql) {
        //hive数据库特殊处理
        if (dbType.getDesc().contains("hive")){
            return dbDialect.getTableColumnsInfo(jdbcTemplate, dbName, dbSchema,tableName);
        }

        final String sql = dbDialect.columns(dbName,dbSchema, tableName);
        List<DbColumn> query = jdbcTemplate.query(sql, dbDialect.columnMapper());
        query = query.stream()
                .filter(distinctByKey(DbColumn::getColName))
                .toList();

        // 获取字段类型
        if (StringUtils.isNotBlank(columnSql) && CollectionUtil.isNotEmpty(query)) {
            Map<String, DbColumn> collect = query.stream().collect(Collectors.toMap(DbColumn::getColName, v -> v));
            try (Connection connection = getConnection()){
                Statement st = connection.createStatement();
                try (ResultSet rs = st.executeQuery(columnSql)) {
                    ResultSetMetaData m = rs.getMetaData();
                    int columns = m.getColumnCount();

                    for (int i = 1; i <= columns; i++) {
                        String name = StringUtils.defaultString(m.getColumnLabel(i), m.getColumnName(i));
                        int columnType = m.getColumnType(i);
                        if (collect.containsKey(name)) {
                            collect.get(name).setFieldType(columnType);
                            collect.get(name).setAutoIncrement(m.isAutoIncrement(i));
                        }
                    }
                }
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }
        return query;
    }

    @Override
    public List<DbIndex> getIndexs(String dbName, String dbSchema ,String tableName){
        String sql = dbDialect.indexs(dbName,dbSchema,tableName);
        List<DbIndex> query = jdbcTemplate.query(sql, dbDialect.indexMapper());

        return query;
    }


    @Override
    public List<DbTable> getTables(DbType dbType,String dbName,String dbSchema) {
        //hive数据库特殊处理
        if (dbType.getDesc().contains("hive")){
            return dbDialect.getTableInfo(jdbcTemplate, dbName, dbSchema);
        }
        String sql = dbDialect.tables(dbName,dbSchema);
        return jdbcTemplate.query(sql, dbDialect.tableMapper());
    }

    @Override
    public int count(String sql) {
        return jdbcTemplate.queryForObject(dbDialect.count(sql), Integer.class);
    }

    @Override
    public int count(String sql, Object[] args) {
        return jdbcTemplate.queryForObject(dbDialect.count(sql), args, Integer.class);
    }

    @Override
    public int count(String sql, Map<String, Object> params) {
        NamedParameterJdbcTemplate namedJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
        return namedJdbcTemplate.queryForObject(dbDialect.count(sql), params, Integer.class);
    }

    @Override
    public List<Map<String, Object>> queryList(String sql) {
        return jdbcTemplate.queryForList(sql);
    }

    @Override
    public List<Map<String, Object>> queryList(String sql, Object[] args) {
        return jdbcTemplate.queryForList(sql, args);
    }

    @Override
    public PageResult<Map<String, Object>> queryByPage(String sql, long offset, long size) {
        int total = count(sql);
        String pageSql = dbDialect.buildPaginationSql(sql, offset, size);
        List<Map<String, Object>> records = jdbcTemplate.queryForList(pageSql);
        return new PageResult<>(total, records);
    }

    @Override
    public PageResult<Map<String, Object>> queryByPage(String sql, Object[] args, long offset, long size) {
        int total = count(sql, args);
        String pageSql = dbDialect.buildPaginationSql(sql, offset, size);
        List<Map<String, Object>> records = jdbcTemplate.queryForList(pageSql, args);
        return new PageResult<>(total, records);
    }

    @Override
    public PageResult<Map<String, Object>> queryByPage(String sql, Map<String, Object> params, long offset, long size) {
        int total = count(sql, params);
        String pageSql = dbDialect.buildPaginationSql(sql, offset, size);
        NamedParameterJdbcTemplate namedJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
        List<Map<String, Object>> records = namedJdbcTemplate.queryForList(pageSql, params);
        return new PageResult<>(total, records);
    }


    @Override
    public String getQuotedSchemaTableCombination(String schemaName, String tableName) {
        return quoteSchemaTableName(schemaName, tableName);
    }

    protected String quoteSchemaTableName(String schemaName, String tableName) {
        return getDbType().quoteSchemaTableName(schemaName, tableName);
    }

    protected String quoteName(String name) {
        return getDbType().quoteName(name);
    }

    @Override
    public String getFieldDefinition(ColumnMetaData v, List<String> pks, boolean useAutoInc,
                                     boolean addCr, boolean withRemarks) {
        throw new RuntimeException("AbstractDatabase Unimplemented!");
    }

    @Override
    public void preAppendCreateTableSql(StringBuilder builder) {
        // NOTHING, Please override by subclass!
    }

    @Override
    public void appendPrimaryKeyForCreateTableSql(StringBuilder builder,String primaryKeySchema ,List<String> primaryKeys) {
        // 不支持主键的数据库类型(例如：hive)，需要覆盖掉该方法
        if (CollectionUtils.isNotEmpty(primaryKeys)) {
            String primaryKeyAsString = getPrimaryKeyAsString(primaryKeys);
            builder.append(", PRIMARY KEY (").append(primaryKeyAsString).append(")");
        }
    }

    @Override
    public void postAppendCreateTableSql(StringBuilder builder, String tblComment, List<String> primaryKeys,
                                         SourceProperties tblProperties) {
        // Nothing, please override by subclass!
    }


    @Override
    public String getPrimaryKeyAsString(List<String> pks) {
        if (!pks.isEmpty()) {
            return quoteName(
                    StringUtils.join(
                            pks.stream().distinct().collect(Collectors.toList())
                            , quoteName(",")
//                            , quoteName(StrPool.COMMA)
                    )
            );
        }
        return StringUtils.EMPTY;
    }

    @Override
    public String getTableFieldsQuerySQL(String schemaName, String tableName) {
        String fullTableName = quoteSchemaTableName(schemaName, tableName);
        return String.format("SELECT %s FROM %s WHERE 1=2", "*", fullTableName);
    }

    @Override
    public List<String> getTableColumnCommentDefinition(TableDescription td,
                                                        List<ColumnDescription> cds) {
        throw new RuntimeException("AbstractDatabase Unimplemented!");
    }

    //    ALTER TABLE public.data_source_sys ADD aaa varchar NULL;
//ALTER TABLE datax_web.job_group DROP COLUMN address_list;
//    ALTER TABLE datax_web.job_group MODIFY COLUMN address_list varchar(512) NOT NULL ;

    @Override
    public String getAddColumnSQL(ColumnMetaData v, List<String> pks, boolean autoIncr, boolean b, boolean withRemarks,String schemaName, String tableName) {
        String sql = "ALTER TABLE" + " " + schemaName + "." + tableName + " ADD ";
        sql += getFieldDefinition(v, pks, autoIncr, b, withRemarks);
        return sql;
    }

    @Override
    public String getDelColumnSQL(ColumnMetaData v, String schemaName, String tableName) {
        return "ALTER TABLE" + " " + schemaName + "." + tableName + " DROP COLUMN " + v.getName();
    }

    @Override
    public String getUpdateColumnSQL(ColumnMetaData v, List<String> pks, boolean autoIncr, boolean b, boolean withRemarks,String schemaName, String tableName) {
        String sql = "ALTER TABLE" + " " + schemaName + "." + tableName + " MODIFY COLUMN ";
        sql += getFieldDefinition(v, pks, autoIncr, b, withRemarks);
        return sql;
    }

    @Override
    public String getRenameColumnSQL(ColumnMetaData v,String schemaName, String tableName,String oldColumnName) {
        return "ALTER TABLE" + " " + schemaName + "." + tableName + " RENAME COLUMN " + oldColumnName + " TO " + v.getName();
    }


    @Override
    public String getAddIndexSQL(IndexMetaData v, String schemaName, String tableName) {
        return "CREATE INDEX " +v.getIndexName() + " ON " + schemaName + "." + tableName +"  (" + v.getColumnName() + ");";
    }

    @Override
    public String getDelIndexSQL(IndexMetaData v,String schemaName, String tableName) {
        return "DROP INDEX " + schemaName + "." + v.getIndexName();
    }

    @Override
    public void getCreateDataBaseSql(StringBuilder builder,String dbName){
        throw new UnsupportedOperationException("Unsupported function!");
    };

}
