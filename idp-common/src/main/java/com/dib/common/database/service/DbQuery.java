package com.dib.common.database.service;




import com.dib.common.database.constants.DbType;
import com.dib.common.database.schema.*;
import com.dib.core.database.core.DbColumn;
import com.dib.core.database.core.DbIndex;
import com.dib.core.database.core.DbTable;
import com.dib.core.database.core.PageResult;

import java.sql.Connection;
import java.util.List;
import java.util.Map;

/**
 * 表数据查询接口
 *
 * <AUTHOR>
 * @since 2020-03-14
 */
public interface DbQuery {


    /**
     * 获取数据库类型
     *
     * @return ProductTypeEnum
     */
    DbType getDbType();

    /**
     * 获取数据库连接
     */
    Connection getConnection();

    /**
     * 检测连通性
     */
    boolean valid();

    /**
     * 关闭数据源
     */
//    void close();

    /**
     *  获取指定表 具有的所有字段列表
     * @param dbType
     * @param dbName
     * @param tableName
     * @return
     */
    List<DbColumn> getTableColumns(DbType dbType,String dbName,String dbSchema, String tableName,String columnSql);

    /**
     *  获取指定库 具有的所有索引列表
     * @param dbName
     * @param sid
     * @return
     */
    List<DbIndex> getIndexs(String dbName, String sid, String tableName);

    /**
     * 获取指定数据库下 所有的表信息
     *
     * @param dbName
     * @return
     */
    List<DbTable> getTables(DbType dbType,String dbName,String dbSchema);

    /**
     * 获取总数
     *
     * @param sql
     * @return
     */
    int count(String sql);

    /**
     * 获取总数带查询参数
     *
     * @param sql
     * @return
     */
    int count(String sql, Object[] args);

    /**
     * 获取总数带查询参数 NamedParameterJdbcTemplate
     *
     * @param sql
     * @return
     */
    int count(String sql, Map<String, Object> params);

    /**
     * 查询结果列表
     *
     * @param sql
     * @return
     */
    List<Map<String, Object>> queryList(String sql);

    /**
     * 查询结果列表带查询参数
     *
     * @param sql
     * @param args
     * @return
     */
    List<Map<String, Object>> queryList(String sql, Object[] args);

    /**
     * 查询结果分页
     *
     * @param sql
     * @param offset
     * @param size
     * @return
     */
    PageResult<Map<String, Object>> queryByPage(String sql, long offset, long size);

    /**
     * 查询结果分页带查询参数
     * @param sql
     * @param args
     * @param offset
     * @param size
     * @return
     */
    PageResult<Map<String, Object>> queryByPage(String sql, Object[] args, long offset, long size);

    /**
     * 查询结果分页带查询参数 NamedParameterJdbcTemplate
     * @param sql
     * @param params
     * @param offset
     * @param size
     * @return
     */
    PageResult<Map<String, Object>> queryByPage(String sql, Map<String, Object> params, long offset, long size);

    /**
     * 获取数据库的表全名
     *
     * @param schemaName 模式名称
     * @param tableName  表名称
     * @return 表全名
     */
    String getQuotedSchemaTableCombination(String schemaName, String tableName);

    /**
     * 获取字段列的结构定义
     *
     * @param v           值元数据定义
     * @param pks         主键字段名称列表
     * @param addCr       是否结尾换行
     * @param useAutoInc  是否自增
     * @param withRemarks 是否带有注释
     * @return 字段定义字符串
     */
    String getFieldDefinition(ColumnMetaData v, List<String> pks, boolean useAutoInc, boolean addCr,
                              boolean withRemarks);

    /**
     * 前置补充建表SQL
     *
     * @param builder 建表SQL的字符串构造器
     */
    void preAppendCreateTableSql(StringBuilder builder);


    /**
     * 在建表SQl中补充主键
     *
     * @param builder     建表SQL的字符串构造器
     * @param primaryKeys 主键字段列表
     */
    void appendPrimaryKeyForCreateTableSql(StringBuilder builder, String primaryKeySchema ,List<String> primaryKeys);

    /**
     * 后置补充建表SQL
     *
     * @param builder       建表SQL的字符串构造器
     * @param tblComment    表的注释
     * @param primaryKeys   主键列表
     * @param tblProperties 表的属性
     */
    void postAppendCreateTableSql(StringBuilder builder, String tblComment, List<String> primaryKeys,
                                  SourceProperties tblProperties);

    /**
     * 主键列转换为逗号分隔的字符串
     *
     * @param pks 主键字段列表
     * @return 主键字段拼接串
     */
    String getPrimaryKeyAsString(List<String> pks);


    /**
     * 获取表和字段的注释定义
     *
     * @param td  表信息定义
     * @param cds 列信息定义
     * @return 定义字符串列表
     */
    List<String> getTableColumnCommentDefinition(TableDescription td, List<ColumnDescription> cds);


    String getTableFieldsQuerySQL(String schemaName, String tableName);

    /**
     * 为hive等定制的获取联邦建表导数SQL列表
     *
     * @param fieldNames    字段结构信息
     * @param primaryKeys   主键字段信息
     * @param schemaName    模式名称
     * @param tableName     表名称
     * @param autoIncr      是否允许主键自增
     * @param tblProperties 表的属性信息
     * @return 建表导数SQL列表
     */
    default List<String> getCreateTableSqlList(List<ColumnDescription> fieldNames, List<String> primaryKeys,Map<String, String> param,
                                               String schemaName, String tableName, String tableRemarks, boolean autoIncr, SourceProperties tblProperties) {
        throw new UnsupportedOperationException("Unsupported function!");
    }

    //获取新增字段sql
    String getAddColumnSQL(ColumnMetaData v, List<String> pks, boolean autoIncr, boolean b, boolean withRemarks,String schemaName, String tableName);

    //获取删除字段sql
    String getDelColumnSQL(ColumnMetaData v, String schemaName, String tableName);

    //获取修改字段sql
    String getUpdateColumnSQL(ColumnMetaData v, List<String> pks, boolean autoIncr, boolean b, boolean withRemarks,String schemaName, String tableName);

    //获取修改字段名
    String getRenameColumnSQL(ColumnMetaData v,String schemaName, String tableName,String oldColumnName);

    //获取新增索引sql
    String getAddIndexSQL(IndexMetaData v, String schemaName, String tableName);

    //获取删除索引sql
    String getDelIndexSQL(IndexMetaData v, String schemaName, String tableName);

    /**
     * 创建建库sql
     *
     * @param builder    建库SQL的字符串构造器
     * @param dbName    数据库名称
     * @return 创建建库sql
     */
   void getCreateDataBaseSql(StringBuilder builder,String dbName);
}
