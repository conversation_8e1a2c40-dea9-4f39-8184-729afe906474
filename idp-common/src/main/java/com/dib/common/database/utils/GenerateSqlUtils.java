package com.dib.common.database.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.dib.common.database.constants.Constants;
import com.dib.common.database.constants.DbType;
import com.dib.common.database.constants.ProductTableEnum;
import com.dib.common.database.schema.*;
import com.dib.common.database.service.DbQuery;
import com.google.common.collect.Lists;
import jakarta.validation.constraints.NotBlank;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 拼接SQL工具类
 *
 * <AUTHOR>
 */
@UtilityClass
public final class GenerateSqlUtils {

    public static String getDDLCreateTableSQL(
            DbQuery provider,
            List<ColumnDescription> fieldNames,
            List<String> primaryKeys,
            Map<String, String> param,
            String schemaName,
            String tableName,
            boolean autoIncr) {
        return getDDLCreateTableSQL(
                provider,
                fieldNames,
                primaryKeys,
                param,
                schemaName,
                tableName,
                false,
                null,
                autoIncr,
                null);
    }

    public static String getDDLCreateTableSQL(
            DbQuery provider,
            List<ColumnDescription> fieldNames,
            List<String> primaryKeys,
            Map<String, String> param,
            String schemaName,
            String tableName,
            boolean withRemarks,
            String tableRemarks,
            boolean autoIncr,
            SourceProperties tblProperties) {
        DbType type = provider.getDbType();
        StringBuilder sb = new StringBuilder();
        Set<String> fieldNameSets = fieldNames.stream()
                .map(ColumnDescription::getFieldName)
                .collect(Collectors.toSet());
        List<String> pks = primaryKeys.stream()
                .filter(fieldNameSets::contains)
                .collect(Collectors.toList());

        sb.append(Constants.CREATE_TABLE);
        provider.preAppendCreateTableSql(sb);
        sb.append(provider.getQuotedSchemaTableCombination(schemaName, tableName));
        sb.append("(");

        // StarRocks 当中，字段主键的情况下，必须将字段放在最前面，并且顺序一致。
        if (type.isPrimaryKeyShouldAtFirst()) {
            List<ColumnDescription> copyFieldNames = new ArrayList<>();
            Integer fieldIndex = 0;
            for (int i = 0; i < fieldNames.size(); i++) {
                ColumnDescription cd = fieldNames.get(i);
                if (primaryKeys.contains(cd.getFieldName())) {
                    copyFieldNames.add(fieldIndex++, cd);
                } else {
                    copyFieldNames.add(cd);
                }
            }
            fieldNames = copyFieldNames;
        }

        for (int i = 0; i < fieldNames.size(); i++) {
            if (i > 0) {
                sb.append(", ");
            } else {
                sb.append("  ");
            }

            ColumnMetaData v = fieldNames.get(i).getMetaData();
            sb.append(provider.getFieldDefinition(v, pks, autoIncr, false, withRemarks));
        }

        String primaryKeySchema = "";
        if (MapUtil.isNotEmpty(param)){
            primaryKeySchema = param.get("primaryKeySchema");
        }

        if (type.isParenthesisBefore()) {
            sb.append(")");
            provider.appendPrimaryKeyForCreateTableSql(sb, primaryKeySchema, pks);
        } else {
            provider.appendPrimaryKeyForCreateTableSql(sb, primaryKeySchema, pks);
            sb.append(")");
        }

        provider.postAppendCreateTableSql(sb, tableRemarks, pks, tblProperties);

        return DDLFormatterUtils.format(sb.toString());
    }

    public static List<String> getDDLCreateTableSQL(
            DbQuery provider,
            List<ColumnDescription> fieldNames,
            List<String> primaryKeys,
            Map<String, String> param,
            String schemaName,
            String tableName,
            String tableRemarks,
            boolean autoIncr,
            SourceProperties tblProperties) {
        DbType productType = provider.getDbType();
        if (productType.isLikeHive()) {
            return provider.getCreateTableSqlList(
                    fieldNames, primaryKeys, param,schemaName, tableName, tableRemarks, autoIncr, tblProperties);
        } else if (productType.noCommentStatement()) {
            String createTableSql = getDDLCreateTableSQL(provider, fieldNames, primaryKeys, param,schemaName,
                    tableName, true, tableRemarks, autoIncr, tblProperties);
            return Arrays.asList(createTableSql);
        } else {
            String createTableSql = getDDLCreateTableSQL(provider, fieldNames, primaryKeys,param, schemaName,
                    tableName, true, tableRemarks, autoIncr, tblProperties);
            TableDescription td = new TableDescription();
            td.setSchemaName(schemaName);
            td.setTableName(tableName);
            td.setRemarks(tableRemarks);
            td.setTableType(ProductTableEnum.TABLE.name());
            List<String> results = provider.getTableColumnCommentDefinition(td, fieldNames);
            if (CollectionUtils.isEmpty(results)) {
                results = Lists.newArrayList(createTableSql);
            } else {
                results.add(0, createTableSql);
            }
            return results;
        }
    }

    public static List<String> getUpdateColumnSQL(
            DbQuery provider,
            List<ColumnDescription> fieldNames,
            List<String> primaryKeys,
            String schemaName,
            String tableName,
            String sqlType) {
        DbType type = provider.getDbType();
        StringBuilder sb = new StringBuilder();
        Set<String> fieldNameSets = fieldNames.stream()
                .map(ColumnDescription::getFieldName)
                .collect(Collectors.toSet());
        List<String> pks = primaryKeys.stream()
                .filter(fieldNameSets::contains)
                .collect(Collectors.toList());


        // StarRocks 当中，字段主键的情况下，必须将字段放在最前面，并且顺序一致。
        if (type.isPrimaryKeyShouldAtFirst()) {
            List<ColumnDescription> copyFieldNames = new ArrayList<>();
            Integer fieldIndex = 0;
            for (int i = 0; i < fieldNames.size(); i++) {
                ColumnDescription cd = fieldNames.get(i);
                if (primaryKeys.contains(cd.getFieldName())) {
                    copyFieldNames.add(fieldIndex++, cd);
                } else {
                    copyFieldNames.add(cd);
                }
            }
            fieldNames = copyFieldNames;
        }

        for (int i = 0; i < fieldNames.size(); i++) {

            if (i > 0) {
                sb.append(";\n");
            }
            ColumnDescription columnDescription = fieldNames.get(i);
            ColumnMetaData v = columnDescription.getMetaData();

            String columnSQL = "";
            if (sqlType.equals("addColumn")){
                columnSQL = provider.getAddColumnSQL(v, pks, false, false, true, schemaName, tableName);
            }else if (sqlType.equals("delColumn")){
                columnSQL = provider.getDelColumnSQL(v, schemaName, tableName);
            }else if (sqlType.equals("updateColumn")){
                columnSQL = provider.getUpdateColumnSQL(v, pks, false, false, true, schemaName, tableName);
            }else if (StringUtils.isNotBlank(columnDescription.getOldName()) && sqlType.equals("renameColumn") ){
                columnSQL = provider.getRenameColumnSQL(v, schemaName, tableName,columnDescription.getOldName());
            }
            sb.append(columnSQL);
        }

        String createTableSql = DDLFormatterUtils.format(sb.toString());

        TableDescription td = new TableDescription();
        td.setSchemaName(schemaName);
        td.setTableName(tableName);
        td.setTableType(ProductTableEnum.TABLE.name());
        List<String> results = provider.getTableColumnCommentDefinition(td, fieldNames);
        if (CollectionUtils.isEmpty(results)) {
            results = Lists.newArrayList(createTableSql);
        } else {
            results.add(0, createTableSql);
        }
        return results;
    }

    public static List<String> getDelAndAddIndexSQL(
            DbQuery provider,
            List<IndexMetaData> indexMetaData,
            String schemaName,
            String tableName,
            String sqlType) {
        DbType type = provider.getDbType();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < indexMetaData.size(); i++) {

            if (i > 0) {
                sb.append(";\n");
            }

            IndexMetaData v = indexMetaData.get(i);

            String columnSQL = "";
            if (sqlType.equals("addIndex")) {
                columnSQL = provider.getAddIndexSQL(v, schemaName, tableName);
            } else if (sqlType.equals("delIndex")) {
                columnSQL = provider.getDelIndexSQL(v, schemaName, tableName);
            }
            sb.append(columnSQL);
        }

        String createTableSql = DDLFormatterUtils.format(sb.toString());

        List<String> results = new ArrayList<>();

        if (CollectionUtils.isEmpty(results)) {
            results = Lists.newArrayList(createTableSql);
        } else {
            results.add(0, createTableSql);
        }
        return results;
    }

    public static String getDDlCreateDataBaseSQL(DbQuery provider, @NotBlank(message = "目标数据源数据库名不能为空") String dbName) {

        StringBuilder sb = new StringBuilder();

        provider.getCreateDataBaseSql(sb, dbName);

        String createDataBaseSql = DDLFormatterUtils.format(sb.toString());

        List<String> createSqlList = new ArrayList<>();
//        if (CollectionUtils.isEmpty(createSqlList)) {
//            createSqlList = Lists.newArrayList(createDataBaseSql);
//        } else {
            createSqlList.add(0, createDataBaseSql);
//        }


        if (CollectionUtil.isNotEmpty(createSqlList)) {
            StringBuilder results = new StringBuilder();
            createSqlList.stream().distinct().forEach(sql -> results.append(sql).append(sql.endsWith(";") ? "" : ";").append("\n"));
            return results.toString();
        }

        return null;
    }
}
