package com.dib.common.database.datasource;

import com.dib.common.database.constants.DbQueryProperty;
import com.dib.common.database.constants.DbType;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 带缓存功能的数据源工厂类，基于 MD5 key 缓存 DataSource。
 */
@Component
public class CacheDataSourceFactoryBean {

    /**
     * 数据源缓存
     */
    private static Map<String, JdbcTemplate> dataSourceMap = new ConcurrentHashMap<>();


    /**
     * 根据 DbQueryProperty 创建并缓存一个数据源
     *
     * @param property 数据库连接属性
     * @return 数据源实例
     */
    public JdbcTemplate createAndAddDataSource(DbQueryProperty property, boolean isDataBase) {
        String key = generateKey(property,isDataBase);
        String compressedKey = compress(key);

        return dataSourceMap.computeIfAbsent(compressedKey, k -> buildDataSource(compressedKey,property));
    }


    /**
     * 构建一个新的 HikariDataSource
     */
    private JdbcTemplate buildDataSource(String compressedKey,DbQueryProperty property) {
        HikariDataSource dataSource = new HikariDataSource();

        if (DbType.ORACLE_12C == property.getDbType()) {
            Properties properties = new Properties();
            properties.put("driverType", "thin");
            dataSource.setDataSourceProperties(properties);
        }

        dataSource.setJdbcUrl(trainToJdbcUrl(property));
        dataSource.setUsername(property.getUsername());
        dataSource.setPassword(property.getPassword());

        // 可选：设置连接池参数
        dataSource.setMaximumPoolSize(10);
        dataSource.setMinimumIdle(2);
        dataSource.setIdleTimeout(60000);
        dataSource.setMaxLifetime(600000);
        setAppropriateValidationQuery(dataSource, property.getDbType());

        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);

        return jdbcTemplate;
    }


    private void setAppropriateValidationQuery(HikariDataSource dataSource, DbType dbType) {
        switch (dbType) {
            case ORACLE, ORACLE_12C, DM8 -> dataSource.setConnectionTestQuery("SELECT 1 FROM DUAL");
            case POSTGRE_SQL -> dataSource.setConnectionTestQuery("SELECT version()");
            default -> dataSource.setConnectionTestQuery("SELECT 1");
        }
    }




    /**
     * 生成数据源唯一标识 key
     *
     * @param property 数据库连接属性
     * @return 数据源唯一 key
     */
    private String generateKey(DbQueryProperty property,boolean isDataBase) {
        return  String.join(":",
                property.getHost(),
                String.valueOf(property.getPort()),
                property.getUsername(),
                property.getPassword(),
                isDataBase ? "" : property.getDbName(),
                isDataBase ? "" : property.getSid());
    }

    protected  String trainToJdbcUrl(DbQueryProperty property) {
        String url = property.getDbType().getUrl();
        if (StringUtils.isEmpty(url)) {
            throw new RuntimeException("无效数据库类型!");
        }
        url = url.replace("${host}", property.getHost());
        url = url.replace("${port}", String.valueOf(property.getPort()));
        if (DbType.ORACLE == property.getDbType() || DbType.ORACLE_12C == property.getDbType()) {
            url = url.replace("${sid}", property.getSid());
        } else {
            url = url.replace("${dbName}", property.getDbName());
        }
        return url;
    }

    /**
     * 清除所有缓存的数据源（用于应用关闭或重新加载配置）
     */
    public void clearAll() {
        dataSourceMap.clear();
    }

    /**
     * 清除指定 key 的数据源（用于单个数据库配置变更）
     */
    public void clear(String key) {
        String compressedKey = compress(key);
        dataSourceMap.remove(compressedKey);
    }

    /**
     * 使用 MD5 对字符串进行压缩处理，生成16位大写MD5值。
     *
     * @param str 待压缩字符串
     * @return 压缩后的字符串
     */
    public static String compress(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(str.getBytes());

            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }

            // 截取中间16位，并转为大写
            return hexString.toString().substring(8, 24).toUpperCase();

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不可用", e);
        }
    }
}
