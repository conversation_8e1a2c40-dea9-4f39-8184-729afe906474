package com.dib.web.controller.standard;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.controller.BaseController;
import com.dib.core.database.core.DataConstant;
import com.dib.core.database.core.JsonPage;
import com.dib.metadata.validate.ValidationGroups;
import com.dib.standard.dto.TypeDto;
import com.dib.standard.entity.TypeEntity;
import com.dib.standard.mapstruct.TypeMapper;
import com.dib.standard.query.TypeQuery;
import com.dib.common.core.domain.AjaxResult;
import com.dib.standard.service.TypeService;
import com.dib.standard.vo.TypeVo;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据标准类别表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-26
 */
@Tag(name = "数据标准类别表")
@RestController
@RequestMapping("/standard/types")
public class TypeController extends BaseController {

    @Autowired
    private TypeService typeService;

    @Autowired
    private TypeMapper typeMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @LogRecord(
            type = "数据标准类别管理",
            subType = "类别详情查询",
            bizNo = "",
            success = "获取标准类别详细信息成功",
            fail = "获取标准类别详细信息失败",
            extra = "查询参数：{{#id}}"
    )
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true, schema = @Schema(type = "string"))
    @GetMapping("/{id}")
    public AjaxResult getTypeById(@PathVariable String id) {
        TypeEntity typeEntity = typeService.getTypeById(id);
        return AjaxResult.success(typeMapper.toVO(typeEntity));
    }

    @LogRecord(
            type = "数据标准类别管理",
            subType = "类别列表查询",
            bizNo = "",
            success = "获取标准类别列表成功",
            fail = "获取标准类别列表失败",
            extra = ""
    )
    @Operation(summary = "获取列表", description = "")
    @GetMapping("/list")
    public AjaxResult getTypeList() {
        QueryWrapper<TypeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConstant.EnableState.ENABLE.getKey());
        List<TypeEntity> list = typeService.list(queryWrapper);
        List<TypeVo> collect = list.stream().map(typeMapper::toVO).collect(Collectors.toList());
        return AjaxResult.success(collect);
    }

    /**
     * 分页查询信息
     *
     * @param typeQuery
     * @return
     */
    @LogRecord(
            type = "数据标准类别管理",
            subType = "类别分页查询",
            bizNo = "",
            success = "分页查询标准类别成功",
            fail = "分页查询标准类别失败",
            extra = "查询参数：{{#typeQuery}}"
    )
    @Operation(summary = "分页查询", description = "")
    @Parameter(name = "typeQuery", description = "查询实体typeQuery", required = true, schema = @Schema(implementation = TypeQuery.class))
    @GetMapping("/page")
    public AjaxResult getTypePage(TypeQuery typeQuery) {
        QueryWrapper<TypeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(typeQuery.getGbTypeCode()), "gb_type_code", typeQuery.getGbTypeCode());
        queryWrapper.like(StrUtil.isNotBlank(typeQuery.getGbTypeName()), "gb_type_name", typeQuery.getGbTypeName());
        IPage<TypeEntity> page = typeService.page(new Page<>(typeQuery.getPageNum(), typeQuery.getPageSize()), queryWrapper);
        List<TypeVo> collect = page.getRecords().stream().map(typeMapper::toVO).collect(Collectors.toList());
        JsonPage<TypeVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }

    /**
     * 添加
     * @param type
     * @return
     */
    @LogRecord(
            type = "数据标准类别管理",
            subType = "类别新增",
            bizNo = "",
            success = "添加标准类别成功",
            fail = "添加标准类别失败",
            extra = "新增参数：{{#type}}"
    )
    @Operation(summary = "添加信息", description = "根据type对象添加信息")
    @Parameter(name = "type", description = "详细实体type", required = true, schema = @Schema(implementation = TypeDto.class))
    @PostMapping()
    public AjaxResult saveType(@RequestBody @Validated({ValidationGroups.Insert.class}) TypeDto type) {
        TypeEntity typeEntity = typeService.saveType(type);
        return AjaxResult.success(typeMapper.toVO(typeEntity));
    }

    /**
     * 修改
     * @param type
     * @return
     */
    @LogRecord(
            type = "数据标准类别管理",
            subType = "类别修改",
            bizNo = "",
            success = "修改标准类别成功",
            fail = "修改标准类别失败",
            extra = "修改参数：id={{#id}}, type={{#type}}"
    )
    @Operation(summary = "修改信息", description = "根据url的id来指定修改对象，并根据传过来的信息来修改详细信息")
    @Parameter(name = "id", description = "ID", required = true, schema = @Schema(type = "string"))
    @Parameter(name = "type", description = "详细实体type", required = true, schema = @Schema(implementation = TypeDto.class))
    @PutMapping("/{id}")
    public AjaxResult updateType(@PathVariable String id, @RequestBody @Validated({ValidationGroups.Update.class}) TypeDto type) {
        TypeEntity typeEntity = typeService.updateType(type);
        return AjaxResult.success(typeMapper.toVO(typeEntity));
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @LogRecord(
            type = "数据标准类别管理",
            subType = "类别删除",
            bizNo = "",
            success = "删除标准类别成功",
            fail = "删除标准类别失败",
            extra = "删除参数：{{#id}}"
    )
    @Operation(summary = "删除", description = "根据url的id来指定删除对象")
    @Parameter(name = "id", description = "ID", required = true, schema = @Schema(type = "string"))
    @DeleteMapping("/{id}")
    public AjaxResult deleteTypeById(@PathVariable String id) {
        typeService.deleteTypeById(id);
        return AjaxResult.success();
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @LogRecord(
            type = "数据标准类别管理",
            subType = "类别批量删除",
            bizNo = "",
            success = "批量删除标准类别成功",
            fail = "批量删除标准类别失败",
            extra = "删除参数：{{#ids}}"
    )
    @Operation(summary = "批量删除角色", description = "根据url的ids来批量删除对象")
    @Parameter(name = "ids", description = "ID集合", required = true, schema = @Schema(type = "array"))
    @DeleteMapping("/batch/{ids}")
    public AjaxResult deleteTypeBatch(@PathVariable List<String> ids) {
        typeService.deleteTypeBatch(ids);
        return AjaxResult.success();
    }
}
