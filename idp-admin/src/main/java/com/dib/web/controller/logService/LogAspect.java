package com.dib.web.controller.logService;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


import java.lang.reflect.Method;
import java.util.Arrays;

import static cn.hutool.system.SystemUtil.getUserInfo;

@Aspect
@Component
@Slf4j
public class LogAspect {

    // 定义切点：controller包下所有类的所有方法
    @Pointcut("execution(* com.dib.web.controller..*.*(..))")
    public void controllerPointcut() {}

    // 定义切点：service包下所有类的所有方法
    @Pointcut("execution(* com.dib..service..*.*(..))")
    public void servicePointcut() {}

    @Value("${common.url}")
    private static String commonUrl;

    public static ResponseEntity<String> getRemote() {
        String url = commonUrl +  "/api/v1/auth/get-permission-info";

        RestTemplate restTemplate = new RestTemplate();
        String tokenValue = StpUtil.getTokenValue();
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + tokenValue);

        headers.setContentType(MediaType.APPLICATION_JSON);
        // 可以添加其他需要的请求头

        HttpEntity<String> entity = new HttpEntity<>(headers);

        return restTemplate.exchange(
                url,
                HttpMethod.GET,
                entity,
                String.class
        );
    }

    public OperateLogCreateReqDTO getUserInfo(OperateLogCreateReqDTO operateLogCreateReqDTO) {
        ResponseEntity<String> remote = getRemote();
        JSONObject obj = JSON.parseObject(remote.getBody());
        obj = obj.getJSONObject("user");
        operateLogCreateReqDTO.setUserId(obj.getString("id"));
        operateLogCreateReqDTO.setUserName(obj.getString("username"));
        return operateLogCreateReqDTO;
    }

    @Before("controllerPointcut() || servicePointcut()")
    public void doBefore(JoinPoint joinPoint) {
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            OperateLogCreateReqDTO reqDTO = new OperateLogCreateReqDTO();

            reqDTO.setRequestMethod(request.getMethod());
            reqDTO.setRequestUrl(request.getRequestURI());
            reqDTO.setUserIp(ServletUtils.getClientIP(request));
            reqDTO.setUserAgent(ServletUtils.getUserAgent(request));

            reqDTO = getUserInfo(reqDTO);




        }

        // 获取方法信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        log.info("方法: {}.{}", method.getDeclaringClass().getName(), method.getName());
        log.info("参数: {}", Arrays.toString(joinPoint.getArgs()));



    }

    @AfterReturning(pointcut = "controllerPointcut() || servicePointcut()", returning = "result")
    public void doAfterReturning(JoinPoint joinPoint, Object result) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        log.info("方法返回: {}.{}", method.getDeclaringClass().getName(), method.getName());
        log.info("返回值: {}", JSON.toJSONString(result));
    }

    @AfterThrowing(pointcut = "controllerPointcut() || servicePointcut()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Throwable e) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        log.error("方法异常: {}.{}", method.getDeclaringClass().getName(), method.getName());
        log.error("异常信息: {}", e.getMessage(), e);
    }
}
