package com.dib.web.controller.apidata;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.dib.apidata.entity.ApiSyncConfig;
import com.dib.apidata.service.ApiSyncConfigService;
import com.dib.apidata.service.impl.ApiSyncExecutor;
import com.dib.common.core.domain.AjaxResult;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Tag(name = "API 同步配置管理", description = "ApiSyncConfigController")
@RestController
@RequestMapping("/api/sync-config")
public class ApiSyncConfigController {

    @Autowired
    private ApiSyncExecutor executor;

    private final ApiSyncConfigService service;

    public ApiSyncConfigController(ApiSyncConfigService service) {
        this.service = service;
    }

    @LogRecord(
            type = "API同步配置管理",
            subType = "配置新增",
            bizNo = "",
            success = "新增API同步配置成功",
            fail = "新增API同步配置失败",
            extra = "新增参数：{{#config}}"
    )
    @SaCheckPermission("system:api:add")
    @Operation(summary = "创建接口同步配置", description = "创建一个新的 API 同步配置")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "创建成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误")
    })
    @PostMapping("/create")
    public AjaxResult create(@RequestBody ApiSyncConfig config) {
        return AjaxResult.success(service.save(config));
    }

    @LogRecord(
            type = "API同步配置管理",
            subType = "配置修改",
            bizNo = "",
            success = "修改API同步配置成功",
            fail = "修改API同步配置失败",
            extra = "修改参数：{{#config}}"
    )
    @SaCheckPermission("system:api:edit")
    @Operation(summary = "更新接口同步配置", description = "更新已存在的 API 同步配置")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "更新成功"),
            @ApiResponse(responseCode = "404", description = "配置不存在")
    })
    @PutMapping("update")
    public AjaxResult update(@RequestBody ApiSyncConfig config) {
        return AjaxResult.success(service.updateById(config));
    }

    @LogRecord(
            type = "API同步配置管理",
            subType = "配置删除",
            bizNo = "",
            success = "删除API同步配置成功",
            fail = "删除API同步配置失败",
            extra = "删除参数：{{#id}}"
    )
    @SaCheckPermission("system:api:remove")
    @Operation(summary = "删除接口同步配置", description = "删除指定的 API 同步配置")
    @ApiResponses({
            @ApiResponse(responseCode = "204", description = "删除成功"),
            @ApiResponse(responseCode = "404", description = "配置不存在")
    })
    @DeleteMapping("/{id}")
    public AjaxResult delete(@PathVariable Long id) {
        return AjaxResult.success(service.removeById(id));
    }

    @LogRecord(
            type = "API同步配置管理",
            subType = "配置查询",
            bizNo = "",
            success = "查询API同步配置详情成功",
            fail = "查询API同步配置详情失败",
            extra = "查询参数：{{#id}}"
    )
    @SaCheckPermission("system:api:query")
    @Operation(summary = "获取指定配置详情", description = "根据 ID 获取一个 API 同步配置的详细信息")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "成功获取配置"),
            @ApiResponse(responseCode = "404", description = "配置不存在")
    })
    @GetMapping("/{id}")
    public AjaxResult get(@PathVariable Long id) {
        return AjaxResult.success(service.getById(id));
    }

    @LogRecord(
            type = "API同步配置管理",
            subType = "配置列表查询",
            bizNo = "",
            success = "查询API同步配置列表成功",
            fail = "查询API同步配置列表失败",
            extra = ""
    )
    @SaCheckPermission("system:api:query")
    @Operation(summary = "获取所有配置列表", description = "获取所有的 API 同步配置列表")
    @ApiResponse(responseCode = "200", description = "成功获取配置列表")
    @GetMapping("/list")
    public AjaxResult list() {
        return AjaxResult.success(service.list());
    }

    @LogRecord(
            type = "API同步配置管理",
            subType = "配置分页查询",
            bizNo = "",
            success = "分页查询API同步配置成功",
            fail = "分页查询API同步配置失败",
            extra = "查询参数：page={{#page}}, size={{#size}}"
    )
    @SaCheckPermission("system:api:query")
    @Operation(summary = "获取所有配置列表（分页）", description = "分页获取所有的 API 同步配置列表")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "成功获取配置列表"),
            @ApiResponse(responseCode = "400", description = "分页参数错误")
    })
    @GetMapping("/pageList")
    public AjaxResult list(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        return AjaxResult.success(service.list(page,size));
    }

    @LogRecord(
            type = "API同步配置管理",
            subType = "配置执行",
            bizNo = "",
            success = "执行API同步配置成功",
            fail = "执行API同步配置失败",
            extra = "执行参数：{{#id}}"
    )
    @SaCheckPermission("system:api:edit")
    @Operation(summary = "测试连接", description = "测试连接")
    @PostMapping("/run/{id}")
    public  AjaxResult   runById(@PathVariable("id") Long id) {
            Map<String, Object> result = executor.executeById(id);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "接口调用成功");
            response.put("data", result);
            return AjaxResult.success(response);
    }
}
