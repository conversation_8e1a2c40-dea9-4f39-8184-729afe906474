package com.dib.web.controller.metadata;


import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.metadata.dto.SqlConsoleDto;
import com.dib.metadata.service.SqlConsoleService;
import com.dib.metadata.validate.ValidationGroups;
import com.dib.metadata.vo.SqlConsoleVo;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.sql.SQLException;
import java.util.List;

@RestController
@RequestMapping("/sql")
public class SqlConsoleController extends BaseController {

    @Autowired
    private SqlConsoleService sqlConsoleService;

    @LogRecord(
            type = "SQL控制台管理",
            subType = "SQL执行",
            bizNo = "",
            success = "执行SQL成功",
            fail = "执行SQL失败",
            extra = "执行参数：{{#sqlConsoleDto}}"
    )
    @PostMapping("/run")
    public AjaxResult sqlRun(@RequestBody @Validated SqlConsoleDto sqlConsoleDto) throws SQLException {
        List<SqlConsoleVo> list = sqlConsoleService.sqlRun(sqlConsoleDto);
        return AjaxResult.success(list);
    }

    @LogRecord(
            type = "SQL控制台管理",
            subType = "SQL停止",
            bizNo = "",
            success = "停止SQL执行成功",
            fail = "停止SQL执行失败",
            extra = "停止参数：{{#sqlConsoleDto}}"
    )
    @PostMapping("/stop")
    public AjaxResult sqlStop(@RequestBody @Validated({ValidationGroups.Other.class}) SqlConsoleDto sqlConsoleDto){
        sqlConsoleService.sqlStop(sqlConsoleDto);
        return AjaxResult.success();
    }

    @LogRecord(
            type = "SQL控制台管理",
            subType = "表查询",
            bizNo = "",
            success = "查询表列表成功",
            fail = "查询表列表失败",
            extra = "查询参数：{{#sqlConsoleDto}}"
    )
    @PostMapping("/queryTables")
    public AjaxResult queryTables(@RequestBody @Validated SqlConsoleDto sqlConsoleDto){
        return AjaxResult.success(sqlConsoleService.selectTablesBySourceId(sqlConsoleDto));
    }

    @LogRecord(
            type = "SQL控制台管理",
            subType = "SQL查询",
            bizNo = "",
            success = "查询SQL成功",
            fail = "查询SQL失败",
            extra = "查询参数：{{#sqlConsoleDto}}"
    )
    @PostMapping("/querySql")
    public AjaxResult querySql(@RequestBody @Validated SqlConsoleDto sqlConsoleDto){
        return AjaxResult.success(sqlConsoleService.querySql(sqlConsoleDto));
    }
}
