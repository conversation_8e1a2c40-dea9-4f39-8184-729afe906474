//package com.dib.web.controller.logService;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.*;
//import org.springframework.web.client.RestTemplate;
//
//@Slf4j
//public class LogRecordServiceImpl {
//
//    @Value("${common.url}")
//    private static String commonUrl;
//
//    public static ResponseEntity<String> getRemote() {
//        String url = commonUrl +  "/api/v2/auth/get-permission-info";
//
//        RestTemplate restTemplate = new RestTemplate();
////        String tokenValue = StpUtil.getTokenValue();
//        HttpHeaders headers = new HttpHeaders();
//        headers.set("Authorization", "Bearer " + "ff289df8a15e4a358ce53038c6acb704");
//
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        // 可以添加其他需要的请求头
//
//        HttpEntity<String> entity = new HttpEntity<>(headers);
//
//        return restTemplate.exchange(
//                url,
//                HttpMethod.GET,
//                entity,
//                String.class
//        );
//    }
//
//    public OperateLogCreateReqDTO getUserInfo(OperateLogCreateReqDTO operateLogCreateReqDTO) {
//        ResponseEntity<String> remote = getRemote();
//        JSONObject obj = JSON.parseObject(remote.getBody());
//        obj = obj.getJSONObject("user");
//        operateLogCreateReqDTO.setUserId(obj.getString("id"));
//        operateLogCreateReqDTO.setUserName(obj.getString("username"));
//        return operateLogCreateReqDTO;
//    }
//
//
//
//}
