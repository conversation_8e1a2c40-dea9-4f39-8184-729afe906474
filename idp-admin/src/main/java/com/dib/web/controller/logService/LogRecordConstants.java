package com.dib.web.controller.logService;

/**
 * SystemDeptController 操作日志枚举
 * 目的：统一管理，也减少 Service 里各种"复杂"字符串
 *
 */
public interface LogRecordConstants {

    // ======================= SYSTEM_USER 用户 =======================

    String SYSTEM_USER_TYPE = "SYSTEM 用户";
    String SYSTEM_USER_CREATE_SUB_TYPE = "创建用户";
    String SYSTEM_USER_CREATE_SUCCESS = "创建了用户【{{#reqVO.nick}}】";
    String SYSTEM_USER_UPDATE_SUB_TYPE = "更新用户";
    String SYSTEM_USER_UPDATE_SUCCESS = "更新了用户【{{#reqVO.nick}}】: {{#reqVO}}";
    String SYSTEM_USER_DELETE_SUB_TYPE = "删除用户";
    String SYSTEM_USER_DELETE_SUCCESS = "删除了用户【{{#user.nick}}】";
    String SYSTEM_USER_UPDATE_PASSWORD_SUB_TYPE = "重置用户密码";
    String SYSTEM_USER_UPDATE_PASSWORD_SUCCESS = "重置了用户【{{#reqVO.id}}】的密码";

    // ======================= SYSTEM_ROLE 角色 =======================

    String SYSTEM_ROLE_TYPE = "SYSTEM 角色";
    String SYSTEM_ROLE_CREATE_SUB_TYPE = "创建角色";
    String SYSTEM_ROLE_CREATE_SUCCESS = "创建了角色【{{#systemRoleSaveVO.name}}】";
    String SYSTEM_ROLE_UPDATE_SUB_TYPE = "更新角色";
    String SYSTEM_ROLE_UPDATE_SUCCESS = "更新了角色【{{#systemRoleSaveVO.name}}】: {{#systemRoleSaveVO}}";
    String SYSTEM_ROLE_DELETE_SUB_TYPE = "删除角色";
    String SYSTEM_ROLE_DELETE_SUCCESS = "删除了角色【{{#id}}】";
    String SYSTEM_ROLE_ASSIGN_PERMISSION_SUB_TYPE = "配置角色权限";
    String SYSTEM_ROLE_ASSIGN_PERMISSION_SUCCESS = "为角色【{{#systemRoleReqVO.roleId}}】配置了权限";

    // ======================= SYSTEM_USER_ROLE 用户角色管理 =======================

    String SYSTEM_USER_ROLE_TYPE = "SYSTEM 用户角色";
    String SYSTEM_USER_ROLE_ASSIGN_SUB_TYPE = "分配用户角色";
    String SYSTEM_USER_ROLE_ASSIGN_SUCCESS = "为用户【{{#reqVO.userIds}}】分配了角色【{{#reqVO.roleIds}}】";
    String SYSTEM_USER_ROLE_REMOVE_SUB_TYPE = "移除用户角色";
    String SYSTEM_USER_ROLE_REMOVE_SUCCESS = "移除了用户【{{#reqVO.userIds}}】的角色【{{#reqVO.roleIds}}】";

    // ======================= SYSTEM_ORG 组织机构 =======================

    String SYSTEM_ORG_TYPE = "SYSTEM 组织机构";
    String SYSTEM_ORG_CREATE_SUB_TYPE = "创建组织机构";
    String SYSTEM_ORG_CREATE_SUCCESS = "创建了组织机构【{{#reqVO.name}}】";
    String SYSTEM_ORG_UPDATE_SUB_TYPE = "更新组织机构";
    String SYSTEM_ORG_UPDATE_SUCCESS = "更新了组织机构【{{#reqVO.name}}】: {{#reqVO}}";
    String SYSTEM_ORG_DELETE_SUB_TYPE = "删除组织机构";
    String SYSTEM_ORG_DELETE_SUCCESS = "删除了组织机构【{{#id}}】";

    // ======================= SYSTEM_RESOURCE 资源权限 =======================

    String SYSTEM_RESOURCE_TYPE = "SYSTEM 资源权限";
    String SYSTEM_RESOURCE_CREATE_SUB_TYPE = "创建资源";
    String SYSTEM_RESOURCE_CREATE_SUCCESS = "创建了资源【{{#resourceReqVO.name}}】";
    String SYSTEM_RESOURCE_UPDATE_SUB_TYPE = "更新资源";
    String SYSTEM_RESOURCE_UPDATE_SUCCESS = "更新了资源【{{#resourceReqVO.name}}】: {{#resourceReqVO}}";
    String SYSTEM_RESOURCE_DELETE_SUB_TYPE = "删除资源";
    String SYSTEM_RESOURCE_DELETE_SUCCESS = "删除了资源【{{#reqVO.id}}】";
    String SYSTEM_RESOURCE_ASSIGN_PERMISSION_SUB_TYPE = "配置资源权限";
    String SYSTEM_RESOURCE_ASSIGN_PERMISSION_SUCCESS = "配置了资源【{{#reqVO.resourceSign}}】的数据权限";

    // ======================= SYSTEM_MENU 系统菜单 =======================

    String SYSTEM_MENU_TYPE = "SYSTEM 菜单";
    String SYSTEM_MENU_CREATE_SUB_TYPE = "创建菜单";
    String SYSTEM_MENU_CREATE_SUCCESS = "创建了菜单【{{#systemMenuSaveVO.name}}】";
    String SYSTEM_MENU_UPDATE_SUB_TYPE = "更新菜单";
    String SYSTEM_MENU_UPDATE_SUCCESS = "更新了菜单【{{#systemMenuSaveVO.name}}】: {{#systemMenuSaveVO}}";
    String SYSTEM_MENU_DELETE_SUB_TYPE = "删除菜单";
    String SYSTEM_MENU_DELETE_SUCCESS = "删除了菜单【{{#id}}】";

    // ======================= SYSTEM_ROLE_GROUP 角色组 =======================

    String SYSTEM_ROLE_GROUP_TYPE = "SYSTEM 角色组";
    String SYSTEM_ROLE_GROUP_CREATE_SUB_TYPE = "创建角色组";
    String SYSTEM_ROLE_GROUP_CREATE_SUCCESS = "创建了角色组【{{#systemRoleGroupSaveVO.name}}】";
    String SYSTEM_ROLE_GROUP_UPDATE_SUB_TYPE = "更新角色组";
    String SYSTEM_ROLE_GROUP_UPDATE_SUCCESS = "更新了角色组【{{#systemRoleGroupSaveVO.name}}】: {{#systemRoleGroupSaveVO}}";
    String SYSTEM_ROLE_GROUP_DELETE_SUB_TYPE = "删除角色组";
    String SYSTEM_ROLE_GROUP_DELETE_SUCCESS = "删除了角色组【{{#id}}】";
    String SYSTEM_ROLE_GROUP_BIND_SUB_TYPE = "角色组绑定角色";
    String SYSTEM_ROLE_GROUP_BIND_SUCCESS = "为角色组【{{#systemRoleGroupMiddleReqVO.roleGroupId}}】绑定了角色";

    // ======================= SYSTEM_USER_GROUP 用户组 =======================

    String SYSTEM_USER_GROUP_TYPE = "SYSTEM 用户组";
    String SYSTEM_USER_GROUP_CREATE_SUB_TYPE = "创建用户组";
    String SYSTEM_USER_GROUP_CREATE_SUCCESS = "创建了用户组【{{#reqVO.name}}】";
    String SYSTEM_USER_GROUP_UPDATE_SUB_TYPE = "更新用户组";
    String SYSTEM_USER_GROUP_UPDATE_SUCCESS = "更新了用户组【{{#reqVO.name}}】: {{#reqVO}}";
    String SYSTEM_USER_GROUP_DELETE_SUB_TYPE = "删除用户组";
    String SYSTEM_USER_GROUP_DELETE_SUCCESS = "删除了用户组【{{#id}}】";

    // ======================= USER_PROFILE 用户个人信息 =======================

    String USER_PROFILE_TYPE = "用户个人信息";
    String USER_PROFILE_UPDATE_SUB_TYPE = "更新个人信息";
    String USER_PROFILE_UPDATE_SUCCESS = "更新了个人信息: {{#reqVO}}";
    String USER_PROFILE_UPDATE_PASSWORD_SUB_TYPE = "修改个人密码";
    String USER_PROFILE_UPDATE_PASSWORD_SUCCESS = "修改了个人密码";
    String USER_PROFILE_UPDATE_AVATAR_SUB_TYPE = "更新个人头像";
    String USER_PROFILE_UPDATE_AVATAR_SUCCESS = "更新了个人头像";

    // ======================= SYSTEM_DEPT 部门 =======================

    String SYSTEM_DEPT_TYPE = "SYSTEM 部门";
    String SYSTEM_DEPT_CREATE_SUB_TYPE = "创建部门";
    String SYSTEM_DEPT_CREATE_SUCCESS = "创建了部门【{{#reqVO.name}}】";
    String SYSTEM_DEPT_UPDATE_SUB_TYPE = "更新部门";
    String SYSTEM_DEPT_UPDATE_SUCCESS = "更新了部门【{{#reqVO.name}}】: {{#reqVO}}";
    String SYSTEM_DEPT_DELETE_SUB_TYPE = "删除部门";
    String SYSTEM_DEPT_DELETE_SUCCESS = "删除了部门【{{#id}}】";

    // ======================= SYSTEM_PANEL 面板 =======================

    String SYSTEM_PANEL_TYPE = "SYSTEM 面板";
    String SYSTEM_PANEL_BIND_ROLE_SUB_TYPE = "角色面板绑定";
    String SYSTEM_PANEL_BIND_ROLE_SUCCESS = "为角色【{{#systemRolePanelReqVO.roleId}}】绑定了面板";

    // ======================= SYSTEM_TENANT 租户 =======================

    String SYSTEM_TENANT_TYPE = "SYSTEM 租户";
    String SYSTEM_TENANT_CREATE_SUB_TYPE = "创建租户";
    String SYSTEM_TENANT_CREATE_SUCCESS = "创建了租户【{{#systemTenant.name}}】";
    String SYSTEM_TENANT_UPDATE_SUB_TYPE = "更新租户";
    String SYSTEM_TENANT_UPDATE_SUCCESS = "更新了租户【{{#systemTenant.name}}】: {{#systemTenant}}";
    String SYSTEM_TENANT_DELETE_SUB_TYPE = "删除租户";
    String SYSTEM_TENANT_DELETE_SUCCESS = "删除了租户【{{#id}}】";

    // ======================= SYSTEM_APPS 系统应用 =======================

    String SYSTEM_APPS_TYPE = "SYSTEM 系统应用";
    String SYSTEM_APPS_CREATE_SUB_TYPE = "创建系统应用";
    String SYSTEM_APPS_CREATE_SUCCESS = "创建了系统应用【{{#systemAppsSaveVO.name}}】";
    String SYSTEM_APPS_UPDATE_SUB_TYPE = "更新系统应用";
    String SYSTEM_APPS_UPDATE_SUCCESS = "更新了系统应用【{{#systemAppsSaveVO.name}}】: {{#systemAppsSaveVO}}";
    String SYSTEM_APPS_DELETE_SUB_TYPE = "删除系统应用";
    String SYSTEM_APPS_DELETE_SUCCESS = "删除了系统应用【{{#id}}】";

    // ======================= SYSTEM_MENU_RESOURCE 菜单资源 =======================

    String SYSTEM_MENU_RESOURCE_TYPE = "SYSTEM 菜单资源";
    String SYSTEM_MENU_RESOURCE_CREATE_SUB_TYPE = "创建菜单资源";
    String SYSTEM_MENU_RESOURCE_CREATE_SUCCESS = "创建了菜单资源【{{#systemMenuResourceSaveVO.name}}】";
    String SYSTEM_MENU_RESOURCE_UPDATE_SUB_TYPE = "更新菜单资源";
    String SYSTEM_MENU_RESOURCE_UPDATE_SUCCESS = "更新了菜单资源【{{#systemMenuResourceSaveVO.name}}】: {{#systemMenuResourceSaveVO}}";
    String SYSTEM_MENU_RESOURCE_DELETE_SUB_TYPE = "删除菜单资源";
    String SYSTEM_MENU_RESOURCE_DELETE_SUCCESS = "删除了菜单资源【{{#id}}】";



}
