package com.dib.web.controller.dib;


import com.dib.bigdata.core.cron.CronExpression;
import com.dib.bigdata.core.thread.JobTriggerPoolHelper;
import com.dib.bigdata.core.trigger.TriggerTypeEnum;
import com.dib.bigdata.core.util.I18nUtil;
import com.dib.bigdata.dto.DataXBatchJsonBuildDto;
import com.dib.bigdata.dto.TriggerJobDto;
import com.dib.bigdata.entity.JobInfo;
import com.dib.bigdata.service.JobService;
import com.dib.core.biz.model.ReturnT;
import com.dib.core.util.DateUtil;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import org.springframework.beans.factory.annotation.Autowired;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * index controller
 *
 * <AUTHOR> 2015-12-19 16:13:16
 */
@Tag(name = "任务配置接口")
@RestController
@RequestMapping("/api/job")
public class JobInfoController extends BaseController{

    @Autowired
    private JobService jobService;


    @LogRecord(
            type = "任务配置管理",
            subType = "任务分页查询",
            bizNo = "",
            success = "查询任务列表成功",
            fail = "查询任务列表失败",
            extra = "查询参数：current={{#current}}, size={{#size}}, jobGroup={{#jobGroup}}, triggerStatus={{#triggerStatus}}, jobDesc={{#jobDesc}}, glueType={{#glueType}}, projectIds={{#projectIds}}"
    )
    @GetMapping("/pageList")
    @Operation(summary = "任务列表")
    @PreAuthorize("@ss.hasPermi('datax:job:list')")
    public ReturnT<Map<String, Object>> pageList(@RequestParam(required = false, defaultValue = "0") int current,
                                                 @RequestParam(required = false, defaultValue = "10") int size,
                                                 int jobGroup, int triggerStatus, String jobDesc, String glueType, Integer[] projectIds) {

        return new ReturnT<>(jobService.pageList((current-1)*size, size, jobGroup, triggerStatus, jobDesc, glueType, 0, projectIds));
    }

    @LogRecord(
            type = "任务配置管理",
            subType = "任务列表查询",
            bizNo = "",
            success = "查询全部任务列表成功",
            fail = "查询全部任务列表失败",
            extra = ""
    )
    @GetMapping("/list")
    @Operation(summary = "全部任务列表")
    @PreAuthorize("@ss.hasPermi('datax:job:query')")
    public ReturnT<List<JobInfo>> list(){
        return new ReturnT<>(jobService.list());
    }

    @LogRecord(
            type = "任务配置管理",
            subType = "任务新增",
            bizNo = "",
            success = "添加任务成功",
            fail = "添加任务失败",
            extra = "新增参数：{{#jobInfo}}"
    )
    @PostMapping("/add")
    @Operation(summary = "添加任务")
    @PreAuthorize("@ss.hasPermi('datax:job:add')")
    public ReturnT<String> add(HttpServletRequest request, @RequestBody JobInfo jobInfo) {
        jobInfo.setUserId(getUserId());
        return jobService.add(jobInfo);
    }

    @LogRecord(
            type = "任务配置管理",
            subType = "任务修改",
            bizNo = "",
            success = "更新任务成功",
            fail = "更新任务失败",
            extra = "修改参数：{{#jobInfo}}"
    )
    @PostMapping("/update")
    @Operation(summary = "更新任务")
    @PreAuthorize("@ss.hasPermi('datax:job:edit')")
    public ReturnT<String> update(HttpServletRequest request,@RequestBody JobInfo jobInfo) {
        jobInfo.setUserId(getUserId());
        return jobService.update(jobInfo);
    }

    @LogRecord(
            type = "任务配置管理",
            subType = "任务删除",
            bizNo = "",
            success = "移除任务成功",
            fail = "移除任务失败",
            extra = "删除参数：{{#id}}"
    )
    @PostMapping(value = "/remove/{id}")
    @Operation(summary = "移除任务")
    @PreAuthorize("@ss.hasPermi('datax:job:remove')")
    public ReturnT<String> remove(@PathVariable(value = "id") int id) {
        return jobService.remove(id);
    }

    @LogRecord(
            type = "任务配置管理",
            subType = "任务停止",
            bizNo = "",
            success = "停止任务成功",
            fail = "停止任务失败",
            extra = "停止参数：{{#id}}"
    )
    @RequestMapping(value = "/stop",method = RequestMethod.POST)
    @Operation(summary = "停止任务")
    @PreAuthorize("@ss.hasPermi('datax:job:startorstop')")
    public ReturnT<String> pause(int id) {
        return jobService.stop(id);
    }

    @LogRecord(
            type = "任务配置管理",
            subType = "任务启动",
            bizNo = "",
            success = "开启任务成功",
            fail = "开启任务失败",
            extra = "启动参数：{{#id}}"
    )
    @RequestMapping(value = "/start",method = RequestMethod.POST)
    @Operation(summary = "开启任务")
    @PreAuthorize("@ss.hasPermi('datax:job:startorstop')")
    public ReturnT<String> start(int id) {
        return jobService.start(id);
    }

    @LogRecord(
            type = "任务配置管理",
            subType = "任务触发",
            bizNo = "",
            success = "触发任务成功",
            fail = "触发任务失败",
            extra = "触发参数：{{#dto}}"
    )
    @PostMapping(value = "/trigger")
    @Operation(summary = "触发任务")
    @PreAuthorize("@ss.hasPermi('datax:job:trigger')")
    public ReturnT<String> triggerJob(@RequestBody TriggerJobDto dto) {
        // force cover job param
        String executorParam=dto.getExecutorParam();
        if (executorParam == null) {
            executorParam = "";
        }
        JobTriggerPoolHelper.trigger(dto.getJobId(), TriggerTypeEnum.MANUAL, -1, null, executorParam);
        return ReturnT.SUCCESS;
    }

    @LogRecord(
            type = "任务配置管理",
            subType = "触发时间查询",
            bizNo = "",
            success = "获取近5次触发时间成功",
            fail = "获取近5次触发时间失败",
            extra = "查询参数：{{#cron}}"
    )
    @GetMapping("/nextTriggerTime")
    @Operation(summary = "获取近5次触发时间")
    public ReturnT<List<String>> nextTriggerTime(String cron) {
        List<String> result = new ArrayList<>();
        try {
            CronExpression cronExpression = new CronExpression(cron);
            Date lastTime = new Date();
            for (int i = 0; i < 5; i++) {
                lastTime = cronExpression.getNextValidTimeAfter(lastTime);
                if (lastTime != null) {
                    result.add(DateUtil.formatDateTime(lastTime));
                } else {
                    break;
                }
            }
        } catch (ParseException e) {
            return new ReturnT<>(ReturnT.FAIL_CODE, I18nUtil.getString("jobinfo_field_cron_invalid"));
        }
        return new ReturnT<>(result);
    }

    @LogRecord(
            type = "任务配置管理",
            subType = "任务批量创建",
            bizNo = "",
            success = "批量创建任务成功",
            fail = "批量创建任务失败",
            extra = "批量创建参数：{{#dto}}"
    )
    @PostMapping("/batchAdd")
    @Operation(summary = "批量创建任务")
    public ReturnT<String> batchAdd(@RequestBody DataXBatchJsonBuildDto dto) throws IOException {
        if (dto.getTemplateId() ==0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, (I18nUtil.getString("system_please_choose") + I18nUtil.getString("jobinfo_field_temp")));
        }
        return jobService.batchAdd(dto);
    }
}
