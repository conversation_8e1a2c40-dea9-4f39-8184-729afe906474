package com.dib.web.controller.apidata;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.dib.apidata.entity.ApiSyncConfig;
import com.dib.apidata.service.impl.ApiSyncExecutor;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api-sync")
public class ApiSyncController {

    @Autowired
    private ApiSyncExecutor executor;

    @LogRecord(
            type = "API同步管理",
            subType = "API同步执行",
            bizNo = "",
            success = "执行API同步成功",
            fail = "执行API同步失败",
            extra = "执行参数：{{#id}}"
    )
    @SaCheckPermission("system:api:edit")
    @PostMapping("/run/{id}")
    public  ResponseEntity<?>   runById(@PathVariable("id") Long id) {
        try {
            Map<String, Object> result = executor.executeById(id);
             Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "接口调用成功");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 500);
            error.put("message", "接口调用失败");
            error.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);

        }
    }
}