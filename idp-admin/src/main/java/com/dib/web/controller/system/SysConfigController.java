package com.dib.web.controller.system;

import java.util.List;

import com.mzt.logapi.starter.annotation.LogRecord;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dib.common.annotation.Log;
import com.dib.common.constant.UserConstants;
import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.common.core.page.TableDataInfo;
import com.dib.common.enums.BusinessType;
import com.dib.common.utils.poi.ExcelUtil;
import com.dib.system.domain.SysConfig;
import com.dib.system.service.ISysConfigService;

/**
 * 参数配置 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/config")
public class SysConfigController extends BaseController
{
    @Autowired
    private ISysConfigService configService;

    /**
     * 获取参数配置列表
     */
    @LogRecord(
            type = "参数配置管理",
            subType = "参数列表查询",
            bizNo = "",
            success = "查询参数配置列表成功",
            fail = "查询参数配置列表失败",
            extra = "查询参数：{{#config}}"
    )
    @PreAuthorize("@ss.hasPermi('system:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysConfig config)
    {
        startPage();
        List<SysConfig> list = configService.selectConfigList(config);
        return getDataTable(list);
    }

    @LogRecord(
            type = "参数配置管理",
            subType = "参数导出",
            bizNo = "",
            success = "导出参数配置成功",
            fail = "导出参数配置失败",
            extra = "导出参数：{{#config}}"
    )
    @Log(title = "参数管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:config:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysConfig config)
    {
        List<SysConfig> list = configService.selectConfigList(config);
        ExcelUtil<SysConfig> util = new ExcelUtil<SysConfig>(SysConfig.class);
        util.exportExcel(response, list, "参数数据");
    }

    /**
     * 根据参数编号获取详细信息
     */
    @LogRecord(
            type = "参数配置管理",
            subType = "参数详情查询",
            bizNo = "",
            success = "查询参数配置详情成功",
            fail = "查询参数配置详情失败",
            extra = "查询参数：{{#configId}}"
    )
    @PreAuthorize("@ss.hasPermi('system:config:query')")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable Long configId)
    {
        return AjaxResult.success(configService.selectConfigById(configId));
    }

    /**
     * 根据参数键名查询参数值
     */
    @LogRecord(
            type = "参数配置管理",
            subType = "参数键值查询",
            bizNo = "",
            success = "根据键名查询参数值成功",
            fail = "根据键名查询参数值失败",
            extra = "查询参数：{{#configKey}}"
    )
    @GetMapping(value = "/configKey/{configKey}")
    public AjaxResult getConfigKey(@PathVariable String configKey)
    {
        return AjaxResult.success(configService.selectConfigByKey(configKey));
    }

    /**
     * 新增参数配置
     */
    @LogRecord(
            type = "参数配置管理",
            subType = "参数新增",
            bizNo = "",
            success = "新增参数配置成功",
            fail = "新增参数配置失败",
            extra = "新增参数：{{#config}}"
    )
    @PreAuthorize("@ss.hasPermi('system:config:add')")
    @Log(title = "参数管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysConfig config)
    {
        if (UserConstants.NOT_UNIQUE.equals(configService.checkConfigKeyUnique(config)))
        {
            return AjaxResult.error("新增参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        config.setCreateBy(getUsername());
        return toAjax(configService.insertConfig(config));
    }

    /**
     * 修改参数配置
     */
    @LogRecord(
            type = "参数配置管理",
            subType = "参数修改",
            bizNo = "",
            success = "修改参数配置成功",
            fail = "修改参数配置失败",
            extra = "修改参数：{{#config}}"
    )
    @PreAuthorize("@ss.hasPermi('system:config:edit')")
    @Log(title = "参数管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysConfig config)
    {
        if (UserConstants.NOT_UNIQUE.equals(configService.checkConfigKeyUnique(config)))
        {
            return AjaxResult.error("修改参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        config.setUpdateBy(getUsername());
        return toAjax(configService.updateConfig(config));
    }

    /**
     * 删除参数配置
     */
    @LogRecord(
            type = "参数配置管理",
            subType = "参数删除",
            bizNo = "",
            success = "删除参数配置成功",
            fail = "删除参数配置失败",
            extra = "删除参数：{{#configIds}}"
    )
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "参数管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds)
    {
        configService.deleteConfigByIds(configIds);
        return success();
    }

    /**
     * 刷新参数缓存
     */
    @LogRecord(
            type = "参数配置管理",
            subType = "缓存刷新",
            bizNo = "",
            success = "刷新参数缓存成功",
            fail = "刷新参数缓存失败",
            extra = ""
    )
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "参数管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    public AjaxResult refreshCache()
    {
        configService.resetConfigCache();
        return AjaxResult.success();
    }
}
