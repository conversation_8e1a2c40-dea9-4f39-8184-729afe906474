package com.dib.web.controller.system;

import java.util.ArrayList;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dib.common.annotation.Log;
import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.common.core.domain.entity.SysDictData;
import com.dib.common.core.page.TableDataInfo;
import com.dib.common.enums.BusinessType;
import com.dib.common.utils.StringUtils;
import com.dib.common.utils.poi.ExcelUtil;
import com.dib.system.service.ISysDictDataService;
import com.dib.system.service.ISysDictTypeService;
import com.mzt.logapi.starter.annotation.LogRecord;

/**
 * 数据字典信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dict/data")
public class SysDictDataController extends BaseController
{
    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @LogRecord(
            type = "字典数据管理",
            subType = "字典数据列表查询",
            bizNo = "",
            success = "查询字典数据列表成功",
            fail = "查询字典数据列表失败",
            extra = "查询参数：{{#dictData}}"
    )
    @PreAuthorize("@ss.hasPermi('system:dict:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysDictData dictData)
    {
        startPage();
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        return getDataTable(list);
    }

    @LogRecord(
            type = "字典数据管理",
            subType = "字典数据导出",
            bizNo = "",
            success = "导出字典数据成功",
            fail = "导出字典数据失败",
            extra = "导出参数：{{#dictData}}"
    )
    @Log(title = "字典数据", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:dict:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysDictData dictData)
    {
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        ExcelUtil<SysDictData> util = new ExcelUtil<SysDictData>(SysDictData.class);
        util.exportExcel(response, list, "字典数据");
    }

    /**
     * 查询字典数据详细
     */
    @LogRecord(
            type = "字典数据管理",
            subType = "字典数据详情查询",
            bizNo = "",
            success = "查询字典数据详情成功",
            fail = "查询字典数据详情失败",
            extra = "查询参数：{{#dictCode}}"
    )
    @PreAuthorize("@ss.hasPermi('system:dict:query')")
    @GetMapping(value = "/{dictCode}")
    public AjaxResult getInfo(@PathVariable Long dictCode)
    {
        return AjaxResult.success(dictDataService.selectDictDataById(dictCode));
    }

    /**
     * 根据字典类型查询字典数据信息
     */
    @LogRecord(
            type = "字典数据管理",
            subType = "字典类型数据查询",
            bizNo = "",
            success = "根据字典类型查询数据成功",
            fail = "根据字典类型查询数据失败",
            extra = "查询参数：{{#dictType}}"
    )
    @GetMapping(value = "/type/{dictType}")
    public AjaxResult dictType(@PathVariable String dictType)
    {
        List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
        if (StringUtils.isNull(data))
        {
            data = new ArrayList<SysDictData>();
        }
        return AjaxResult.success(data);
    }

    /**
     * 新增字典类型
     */
    @LogRecord(
            type = "字典数据管理",
            subType = "字典数据新增",
            bizNo = "",
            success = "新增字典数据成功",
            fail = "新增字典数据失败",
            extra = "新增参数：{{#dict}}"
    )
    @PreAuthorize("@ss.hasPermi('system:dict:add')")
    @Log(title = "字典数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDictData dict)
    {
        dict.setCreateBy(getUsername());
        return toAjax(dictDataService.insertDictData(dict));
    }

    /**
     * 修改保存字典类型
     */
    @LogRecord(
            type = "字典数据管理",
            subType = "字典数据修改",
            bizNo = "",
            success = "修改字典数据成功",
            fail = "修改字典数据失败",
            extra = "修改参数：{{#dict}}"
    )
    @PreAuthorize("@ss.hasPermi('system:dict:edit')")
    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDictData dict)
    {
        dict.setUpdateBy(getUsername());
        return toAjax(dictDataService.updateDictData(dict));
    }

    /**
     * 删除字典类型
     */
    @LogRecord(
            type = "字典数据管理",
            subType = "字典数据删除",
            bizNo = "",
            success = "删除字典数据成功",
            fail = "删除字典数据失败",
            extra = "删除参数：{{#dictCodes}}"
    )
    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dictCodes}")
    public AjaxResult remove(@PathVariable Long[] dictCodes)
    {
        dictDataService.deleteDictDataByIds(dictCodes);
        return success();
    }
}
