package com.dib.web.controller.hdfsdata;

import com.dib.common.core.domain.AjaxResult;
import com.dib.hdfsdata.entity.HdfsDataSource;
import com.dib.hdfsdata.service.HdfsDataSourceService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping("/api/hdfs")
@Tag(name = "HDFS数据源配置")
public class HdfsDataSourceController {

    @Autowired
    private HdfsDataSourceService hdfsService;

    @LogRecord(
            type = "HDFS数据源管理",
            subType = "配置列表查询",
            bizNo = "",
            success = "获取所有HDFS配置成功",
            fail = "获取所有HDFS配置失败",
            extra = ""
    )
    @Operation(summary = "获取所有 HDFS 配置")
    @GetMapping("/list")
    public AjaxResult list() {
        return AjaxResult.success(hdfsService.list());
    }

    @LogRecord(
            type = "HDFS数据源管理",
            subType = "配置新增",
            bizNo = "",
            success = "添加HDFS配置成功",
            fail = "添加HDFS配置失败",
            extra = "新增参数：{{#config}}"
    )
    @Operation(summary = "添加 HDFS 配置")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody HdfsDataSource config) {
        return AjaxResult.success(hdfsService.save(config));
    }

    @LogRecord(
            type = "HDFS数据源管理",
            subType = "配置修改",
            bizNo = "",
            success = "更新HDFS配置成功",
            fail = "更新HDFS配置失败",
            extra = "修改参数：{{#config}}"
    )
    @Operation(summary = "更新 HDFS 配置")
    @PutMapping("/update")
    public AjaxResult update(@RequestBody HdfsDataSource config) {
        return AjaxResult.success(hdfsService.updateById(config));
    }

    @LogRecord(
            type = "HDFS数据源管理",
            subType = "配置删除",
            bizNo = "",
            success = "删除HDFS配置成功",
            fail = "删除HDFS配置失败",
            extra = "删除参数：{{#id}}"
    )
    @Operation(summary = "删除 HDFS 配置")
    @DeleteMapping("/{id}")
    public AjaxResult delete(@PathVariable String id) {
        return AjaxResult.success(hdfsService.removeById(id));
    }

    @LogRecord(
            type = "HDFS数据源管理",
            subType = "连接测试",
            bizNo = "",
            success = "测试HDFS连接成功",
            fail = "测试HDFS连接失败",
            extra = "测试参数：{{#config}}"
    )
    @Operation(summary = "测试连接")
    @PostMapping("/test")
    public AjaxResult testConnection(@RequestBody HdfsDataSource config) {
        return AjaxResult.success(hdfsService.testConnection(config));
    }

    @LogRecord(
            type = "HDFS数据源管理",
            subType = "文件下载",
            bizNo = "",
            success = "下载HFTP文件成功",
            fail = "下载HFTP文件失败",
            extra = "下载参数：{{#config}}"
    )
    @Operation(summary = "下载HFTP文件")
    @PostMapping("/downloadHFtpFile")
    public AjaxResult downloadHFtpFile(@RequestBody HdfsDataSource config , HttpServletResponse response) {
        return AjaxResult.success(hdfsService.downloadHFtpFile(config,response));
    }
}
