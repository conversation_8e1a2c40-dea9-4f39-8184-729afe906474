package com.dib.web.controller.system;

import com.dib.common.core.domain.AjaxResult;
import com.dib.system.domain.vo.DataSourceSysVo;
import com.dib.system.query.SysDataSourceQuery;
import com.dib.system.service.DataSysService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/dataSource/system")
public class DataSysController {

    @Autowired
    private DataSysService dataSysService;
    @LogRecord(
            type = "数据源管理",
            subType = "数据源新增",
            bizNo = "",
            success = "新增数据源成功",
            fail = "新增数据源失败",
            extra = "新增参数：{{#dataSourceSysVo}}"
    )
    @PostMapping("/saveSys")
    public AjaxResult saveSys(@RequestBody DataSourceSysVo dataSourceSysVo) {
        return AjaxResult.success(dataSysService.saveSys(dataSourceSysVo));
    }

    @LogRecord(
            type = "数据源管理",
            subType = "数据源修改",
            bizNo = "",
            success = "修改数据源成功",
            fail = "修改数据源失败",
            extra = "修改参数：{{#dataSourceSysVo}}"
    )
    @PostMapping("/updateSys")
    public AjaxResult updateSys(@RequestBody DataSourceSysVo dataSourceSysVo) {
        return AjaxResult.success(dataSysService.updateSys(dataSourceSysVo));
    }

    @LogRecord(
            type = "数据源管理",
            subType = "数据源删除",
            bizNo = "",
            success = "删除数据源成功",
            fail = "删除数据源失败",
            extra = "删除参数：{{#dataSourceSysVo}}"
    )
    @PostMapping("/deleteSys")
    public AjaxResult deleteSys(@RequestBody DataSourceSysVo dataSourceSysVo) {
        return AjaxResult.success(dataSysService.deleteSys(dataSourceSysVo));
    }

    @LogRecord(
            type = "数据源管理",
            subType = "数据源列表",
            bizNo = "",
            success = "查询数据源列表成功",
            fail = "查询数据源列表失败",
            extra = "查询参数：{{#sysDataSourceQuery}}"
    )
    @PostMapping("/pageQuery")
    public AjaxResult pageQuery(@RequestBody SysDataSourceQuery sysDataSourceQuery) {
        return AjaxResult.success(dataSysService.pageQuery(sysDataSourceQuery));
    }


    @PostMapping("/queryAllMount")
    public AjaxResult queryAllMount() {
        return AjaxResult.success(dataSysService.queryAllMount());
    }


//    /**
//     * 查询指定父级下的菜单列表
//     */
//    @GetMapping("/treeSelectByDataSource")
//    public AjaxResult treeSelectByDataSource()
//    {
//        return AjaxResult.success(dataSysService.treeSelectByDataSource());
//    }

}
