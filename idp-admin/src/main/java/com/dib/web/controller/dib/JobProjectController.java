package com.dib.web.controller.dib;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dib.bigdata.entity.JobProject;
import com.dib.bigdata.service.JobProjectService;
import com.dib.common.core.data.R;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * project manage controller
 *
 * <AUTHOR> 2022-05-24 16:13:16
 */
@RestController
@RequestMapping("/api/jobProject")
@Tag(name = "项目管理模块")
public class JobProjectController extends BaseController {

    @Autowired
    private JobProjectService jobProjectService;


    /**
     * 分页查询所有数据
     *
     * @return 所有数据
     */
    @LogRecord(
            type = "项目管理",
            subType = "项目分页查询",
            bizNo = "",
            success = "分页查询项目成功",
            fail = "分页查询项目失败",
            extra = "查询参数：searchVal={{#searchVal}}, pageSize={{#pageSize}}, pageNo={{#pageNo}}"
    )
    @GetMapping
    @Operation(summary = "分页查询所有数据")
    @PreAuthorize("@ss.hasPermi('datax:jobProject:list')")
    public R selectAll(@RequestParam(value = "searchVal", required = false) String searchVal,
                       @RequestParam("pageSize") Integer pageSize,
                       @RequestParam("pageNo") Integer pageNo) {

        return R.ok(jobProjectService.getProjectListPaging(pageSize, pageNo, searchVal));
    }

    /**
     * Get all project
     *
     * @return
     */
    @LogRecord(
            type = "项目管理",
            subType = "项目列表查询",
            bizNo = "",
            success = "获取所有项目数据成功",
            fail = "获取所有项目数据失败",
            extra = ""
    )
    @Operation(summary = "获取所有数据")
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermi('datax:jobProject:query')")
    public R selectList() {
        QueryWrapper<JobProject> query = new QueryWrapper();
        query.eq("flag", true);
        return R.ok(jobProjectService.list(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @LogRecord(
            type = "项目管理",
            subType = "项目详情查询",
            bizNo = "",
            success = "查询项目详情成功",
            fail = "查询项目详情失败",
            extra = "查询参数：{{#id}}"
    )
    @Operation(summary = "通过主键查询单条数据")
    @GetMapping("{id}")
    @PreAuthorize("@ss.hasPermi('datax:jobProject:query')")
    public R selectOne(@PathVariable Serializable id) {
        return R.ok(this.jobProjectService.getById(id));
    }

    /**
     * 新增数据
     *
     * @param entity 实体对象
     * @return 新增结果
     */
    @LogRecord(
            type = "项目管理",
            subType = "项目新增",
            bizNo = "",
            success = "新增项目成功",
            fail = "新增项目失败",
            extra = "新增参数：{{#entity}}"
    )
    @Operation(summary = "新增数据")
    @PostMapping
    @PreAuthorize("@ss.hasPermi('datax:jobProject:add')")
    public R insert(HttpServletRequest request, @RequestBody JobProject entity) {
        entity.setUserId(getUserId());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        LambdaUpdateWrapper<JobProject> lqw = new LambdaUpdateWrapper<>();
        lqw.eq(JobProject::getName, entity.getName());
        long count = jobProjectService.count(lqw);
        if (count > 0){
            return R.error("项目已存在");
        }
        return R.ok(this.jobProjectService.save(entity));
    }


    /**
     * 修改数据
     *
     * @param entity 实体对象
     * @return 修改结果
     */
    @LogRecord(
            type = "项目管理",
            subType = "项目修改",
            bizNo = "",
            success = "修改项目成功",
            fail = "修改项目失败",
            extra = "修改参数：{{#entity}}"
    )
    @PutMapping
    @Operation(summary = "修改数据")
    @PreAuthorize("@ss.hasPermi('datax:jobProject:edit')")
    public R update(@RequestBody JobProject entity) {
        JobProject project = jobProjectService.getById(entity.getId());
        project.setName(entity.getName());
        project.setDescription(entity.getDescription());
        entity.setUpdateTime(new Date());
        return R.ok(this.jobProjectService.updateById(entity));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @LogRecord(
            type = "项目管理",
            subType = "项目删除",
            bizNo = "",
            success = "删除项目成功",
            fail = "删除项目失败",
            extra = "删除参数：{{#idList}}"
    )
    @DeleteMapping
    @Operation(summary = "删除数据")
    @PreAuthorize("@ss.hasPermi('datax:jobProject:remove')")
    public R delete(@RequestParam("idList") List<String> idList) {
        return R.ok(this.jobProjectService.removeByIds(idList));
    }
}