package com.dib.web.controller.dib;

import com.dib.bigdata.service.DatasourceQueryService;
import com.dib.common.core.data.R;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

/**
 * 查询数据库表名，字段的控制器
 *
 * <AUTHOR>
 * @ClassName MetadataController
 * @Version 2.1.2
 * @since 2022/05/31 20:48
 */
@RestController
@RequestMapping("api/metadata")
@Tag(name = "jdbc数据库查询控制器")
public class MetadataController extends BaseController {

    @Autowired
    private DatasourceQueryService datasourceQueryService;

    /**
     * 根据数据源id获取mongo库名
     *
     * @param datasourceId
     * @return
     */
    @LogRecord(
            type = "元数据管理",
            subType = "数据库查询",
            bizNo = "",
            success = "获取Mongo库名成功",
            fail = "获取Mongo库名失败",
            extra = "查询参数：{{#datasourceId}}"
    )
    @GetMapping("/getDBs")
    @Operation(summary = "根据数据源id获取mongo库名")
    public R getDBs(Long datasourceId) throws IOException {
        return R.ok(datasourceQueryService.getDBs(datasourceId));
    }


    /**
     * 根据数据源id,dbname获取CollectionNames
     *
     * @param datasourceId
     * @return
     */
    @LogRecord(
            type = "元数据管理",
            subType = "集合名称查询",
            bizNo = "",
            success = "获取集合名称成功",
            fail = "获取集合名称失败",
            extra = "查询参数：datasourceId={{#datasourceId}}, dbName={{#dbName}}"
    )
    @GetMapping("/collectionNames")
    @Operation(summary = "根据数据源id,dbname获取CollectionNames")
    public R getCollectionNames(Long datasourceId,String dbName) throws IOException {
        return R.ok(datasourceQueryService.getCollectionNames(datasourceId,dbName));
    }

    /**
     * 获取PG table schema
     *
     * @param datasourceId
     * @return
     */
    @LogRecord(
            type = "元数据管理",
            subType = "数据库模式查询",
            bizNo = "",
            success = "获取数据库模式成功",
            fail = "获取数据库模式失败",
            extra = "查询参数：{{#datasourceId}}"
    )
    @GetMapping("/getDBSchema")
    @Operation(summary = "根据数据源id获取 db schema")
    public R getTableSchema(Long datasourceId) {
        return R.ok(datasourceQueryService.getTableSchema(datasourceId));
    }

    /**
     * 根据数据源id获取可用表名
     *
     * @param datasourceId
     * @return
     */
    @LogRecord(
            type = "元数据管理",
            subType = "表名查询",
            bizNo = "",
            success = "获取可用表名成功",
            fail = "获取可用表名失败",
            extra = "查询参数：datasourceId={{#datasourceId}}, tableSchema={{#tableSchema}}"
    )
    @GetMapping("/getTables")
    @Operation(summary = "根据数据源id获取可用表名")
    public R getTableNames(Long datasourceId,String tableSchema) throws IOException {
        return R.ok(datasourceQueryService.getTables(datasourceId,tableSchema));
    }

    /**
     * 根据数据源id和表名获取所有字段
     *
     * @param datasourceId 数据源id
     * @param tableName    表名
     * @return
     */
    @LogRecord(
            type = "元数据管理",
            subType = "字段查询",
            bizNo = "",
            success = "获取表字段成功",
            fail = "获取表字段失败",
            extra = "查询参数：datasourceId={{#datasourceId}}, tableName={{#tableName}}"
    )
    @GetMapping("/getColumns")
    @Operation(summary = "根据数据源id和表名获取所有字段")
    public R getColumns(Long datasourceId, String tableName) throws IOException {
        return R.ok(datasourceQueryService.getColumns(datasourceId, tableName));
    }

    /**
     * 根据数据源id和sql语句获取所有字段
     *
     * @param datasourceId 数据源id
     * @param querySql     表名
     * @return
     */
    @LogRecord(
            type = "元数据管理",
            subType = "SQL字段查询",
            bizNo = "",
            success = "根据SQL获取字段成功",
            fail = "根据SQL获取字段失败",
            extra = "查询参数：datasourceId={{#datasourceId}}, querySql={{#querySql}}"
    )
    @GetMapping("/getColumnsByQuerySql")
    @Operation(summary = "根据数据源id和sql语句获取所有字段")
    public R getColumnsByQuerySql(Long datasourceId, String querySql) throws SQLException {
        return R.ok(datasourceQueryService.getColumnsByQuerySql(datasourceId, querySql));
    }
}
