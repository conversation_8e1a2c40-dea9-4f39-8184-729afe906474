package com.dib.web.controller.version;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.domain.AjaxResult;
import com.dib.version.entity.VersionData;
import com.dib.version.service.VersionDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/version")
@Tag(name = "版本记录")
public class VersionController {

    @Autowired
    VersionDataService versionDataService;

    @Operation(summary = "创建版本记录")
    @PostMapping("/create")
    public AjaxResult create(@RequestBody VersionData versionData) {
        return AjaxResult.success(versionDataService.save(versionData));
    }

    @Operation(summary = "更新版本记录")
    @PutMapping("/update")
    public AjaxResult update(@RequestBody VersionData versionData) {
        return AjaxResult.success(versionDataService.updateById(versionData));
    }

    @Operation(summary = "删除版本记录")
    @DeleteMapping("/{id}")
    public AjaxResult delete(@PathVariable Long id) {
        return AjaxResult.success(versionDataService.removeById(id));
    }

    @Operation(summary = "获取版本记录详情")
    @GetMapping("/{id}")
    public AjaxResult getById(@PathVariable Long id) {
        return AjaxResult.success(versionDataService.getById(id));
    }

    @Operation(summary = "分页查询版本记录")
    @GetMapping("/page")
    public AjaxResult page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String versionName
            ) {
        // 构建查询条件
        LambdaQueryWrapper<VersionData> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(versionName)) {
            queryWrapper.like(VersionData::getVersion, versionName);
        }
        return AjaxResult.success(versionDataService.page(new Page<>(pageNum, pageSize), queryWrapper));
    }


}
