package com.dib;

import com.mzt.logapi.starter.annotation.EnableLogRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class})
@EnableAsync
@EnableLogRecord(tenant = "com.dib.idp")
public class IdpApplication
{
    public static void main(String[] args) {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(IdpApplication.class, args);

    }
}
