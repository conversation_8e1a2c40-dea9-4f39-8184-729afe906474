package io.dataease.datasource.provider;

import io.dataease.exception.DEException;
import io.dataease.extensions.datasource.dto.DatasourceRequest;
import io.dataease.extensions.datasource.dto.DatasourceDTO;
import io.dataease.extensions.datasource.dto.DatasetTableDTO;
import io.dataease.extensions.datasource.dto.TableField;
import io.dataease.extensions.datasource.dto.ConnectionObj;
import io.dataease.extensions.datasource.provider.Provider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 模型管理Provider - 用于调用idp-model-manage服务获取模型数据
 *
 * @Author: AI Assistant
 */
@Service("modelManageProvider")
public class ModelManageProvider extends Provider {

    private static final Logger logger = LoggerFactory.getLogger(ModelManageProvider.class);

    @Autowired
    private ModelManageClient modelManageClient;

    @Override
    public List<String> getSchema(DatasourceRequest datasourceRequest) {
        // 模型管理不需要schema概念，返回空列表
        return new ArrayList<>();
    }

    @Override
    public List<DatasetTableDTO> getTables(DatasourceRequest datasourceRequest) {
        // 模型管理不需要表概念，返回空列表
        return new ArrayList<>();
    }

    @Override
    public ConnectionObj getConnection(DatasourceDTO coreDatasource) throws Exception {
        // 模型管理使用HTTP接口，不需要数据库连接
        return new ConnectionObj();
    }

    @Override
    public String checkStatus(DatasourceRequest datasourceRequest) throws Exception {
        try {
            // 通过调用模型管理服务检查状态
            modelManageClient.modelTableByModelNo();
            return "Success";
        } catch (Exception e) {
            logger.error("检查模型管理服务状态失败", e);
            return "Error: " + e.getMessage();
        }
    }

    @Override
    public List<TableField> fetchTableField(DatasourceRequest datasourceRequest) throws DEException {
        // 模型管理的字段信息通过其他接口获取
        return new ArrayList<>();
    }

    @Override
    public void hidePW(DatasourceDTO datasourceDTO) {
        // 模型管理不涉及密码，无需处理
    }

    /**
     * 核心方法：获取模型数据
     * 直接调用模型管理服务API
     */
    @Override
    public Map<String, Object> fetchResultField(DatasourceRequest datasourceRequest) throws DEException {
        Map<String, Object> result = new HashMap<>();
        List<TableField> fieldList = new ArrayList<>();
        List<String[]> dataList = new ArrayList<>();

        try {
            // 从SQL中解析模型编号和字段列表
            String modelNo = extractModelNoFromQuery(datasourceRequest.getQuery());
            List<String> indicatorAttrs = extractFieldsFromQuery(datasourceRequest.getQuery());

            if (modelNo == null || modelNo.isEmpty()) {
                DEException.throwException("无法从请求中获取模型编号");
            }

            logger.info("调用模型管理服务查询数据，模型编号: {}, 字段列表: {}", modelNo, indicatorAttrs);

            // 调用模型管理服务的selectModelData接口
            List<SqlConsoleVo> sqlConsoleVos = modelManageClient.selectModelData(indicatorAttrs, modelNo);

            if (sqlConsoleVos != null && !sqlConsoleVos.isEmpty()) {
                SqlConsoleVo sqlConsoleVo = sqlConsoleVos.get(0);

                if (sqlConsoleVo.getSuccess()) {
                    // 构建字段信息
                    if (sqlConsoleVo.getColumnList() != null) {
                        for (String columnName : sqlConsoleVo.getColumnList()) {
                            TableField field = new TableField();
                            field.setName(columnName);
                            field.setOriginName(columnName);
                            field.setType("VARCHAR");
                            field.setFieldType("VARCHAR");
                            fieldList.add(field);
                        }
                    }

                    // 构建数据列表
                    if (sqlConsoleVo.getDataList() != null) {
                        for (Map<String, Object> dataMap : sqlConsoleVo.getDataList()) {
                            String[] row = new String[fieldList.size()];
                            for (int i = 0; i < fieldList.size(); i++) {
                                String columnName = fieldList.get(i).getName();
                                Object value = dataMap.get(columnName);
                                row[i] = value != null ? value.toString() : "";
                            }
                            dataList.add(row);
                        }
                    }

                    logger.info("成功获取模型数据，字段数: {}, 数据行数: {}", fieldList.size(), dataList.size());
                } else {
                    DEException.throwException("模型数据查询失败");
                }
            }

        } catch (Exception e) {
            logger.error("调用模型管理服务失败", e);
            DEException.throwException("调用模型管理服务失败: " + e.getMessage());
        }

        result.put("fields", fieldList);
        result.put("data", dataList);
        return result;
    }

    /**
     * 从SQL中提取模型编号
     */
    private String extractModelNoFromQuery(String sql) {
        if (sql == null) {
            return null;
        }

        try {
            // 匹配 model_no = 'value' 格式
            String pattern = "model_no\\s*=\\s*['\"]([^'\"]+)['\"]";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern, java.util.regex.Pattern.CASE_INSENSITIVE);
            java.util.regex.Matcher m = p.matcher(sql);
            if (m.find()) {
                return m.group(1);
            }
        } catch (Exception e) {
            logger.warn("无法从SQL中提取模型编号: {}", sql);
        }
        return null;
    }

    /**
     * 从SQL中提取字段列表
     */
    private List<String> extractFieldsFromQuery(String sql) {
        List<String> fields = new ArrayList<>();
        if (sql == null) {
            return fields;
        }

        try {
            String upperSql = sql.toUpperCase();
            int selectIndex = upperSql.indexOf("SELECT");
            int fromIndex = upperSql.indexOf("FROM");

            if (selectIndex >= 0 && fromIndex > selectIndex) {
                String fieldsPart = sql.substring(selectIndex + 6, fromIndex).trim();
                if (!"*".equals(fieldsPart.trim())) {
                    String[] fieldArray = fieldsPart.split(",");
                    for (String field : fieldArray) {
                        fields.add(field.trim());
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("无法从SQL中提取字段列表: {}", sql);
        }
        return fields;
    }
}
