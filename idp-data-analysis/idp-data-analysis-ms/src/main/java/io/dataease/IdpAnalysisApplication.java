package io.dataease;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;

@SpringBootApplication(scanBasePackages = {"io.dataease","com.dib"})
public class IdpAnalysisApplication {

    public static void main(String[] args) {
        SpringApplication context = new SpringApplication(IdpAnalysisApplication.class);
        //context.addInitializers(new EhCacheStartListener());
        context.run(args);
    }
}
