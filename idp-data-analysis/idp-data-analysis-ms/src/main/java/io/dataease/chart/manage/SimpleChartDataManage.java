package io.dataease.chart.manage;

import io.dataease.chart.charts.ChartHandlerManager;
import io.dataease.chart.constant.ChartConstants;
import io.dataease.datasource.provider.ModelManageProvider;
import io.dataease.exception.DEException;
import io.dataease.extensions.datasource.dto.DatasourceRequest;
import io.dataease.extensions.datasource.dto.DatasourceSchemaDTO;
import io.dataease.extensions.view.dto.*;
import io.dataease.extensions.view.factory.PluginsChartFactory;
import io.dataease.extensions.view.plugin.AbstractChartPlugin;
import io.dataease.i18n.Translator;
import io.dataease.result.ResultCode;
import io.dataease.utils.BeanUtils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 简化的图表数据管理类 - 只使用ModelManageProvider
 * 去掉权限校验、复杂的过滤逻辑等，专注于调用模型管理服务
 * 
 * @Author: AI Assistant
 */
@Component
public class SimpleChartDataManage {
    
    private static final Logger logger = LoggerFactory.getLogger(SimpleChartDataManage.class);
    
    @Resource
    private ChartHandlerManager chartHandlerManager;
    
    @Autowired
    private ModelManageProvider modelManageProvider;
    
    /**
     * 简化的calcData方法 - 只使用ModelManageProvider
     * 
     * @param view 图表视图配置
     * @param modelNo 模型编号
     * @param indicatorAttrs 指标属性列表（字段列表）
     * @return 计算后的图表数据
     */
    public ChartViewDTO calcData(ChartViewDTO view, String modelNo, List<String> indicatorAttrs) throws Exception {
        logger.info("开始计算图表数据，模型编号: {}, 字段列表: {}", modelNo, indicatorAttrs);
        
        // 1. 基本参数校验
        if (ObjectUtils.isEmpty(view)) {
            DEException.throwException(ResultCode.DATA_IS_WRONG.code(), Translator.get("i18n_chart_delete"));
        }
        
        // 2. 初始化ChartExtRequest
        ChartExtRequest chartExtRequest = view.getChartExtRequest();
        if (chartExtRequest == null) {
            chartExtRequest = new ChartExtRequest();
            view.setChartExtRequest(chartExtRequest);
        }
        
        // 3. 处理Excel导出模式
        if (view.getIsExcelExport()) {
            view.setResultMode(ChartConstants.VIEW_RESULT_MODE.CUSTOM);
        } else if (ChartConstants.VIEW_RESULT_MODE.CUSTOM.equals(chartExtRequest.getResultMode())) {
            view.setResultMode(chartExtRequest.getResultMode());
            view.setResultCount(chartExtRequest.getResultCount());
        }
        
        // 4. 获取图表处理器
        AbstractChartPlugin chartHandler = getChartHandler(view);
        if (chartHandler == null) {
            DEException.throwException(ResultCode.DATA_IS_WRONG.code(), 
                Translator.get("i18n_chart_not_handler") + ": " + view.getRender() + "," + view.getType());
        }
        
        // 5. 格式化轴信息
        AxisFormatResult formatResult = chartHandler.formatAxis(view);
        var xAxis = formatResult.getAxisMap().get(ChartAxis.xAxis);
        var yAxis = formatResult.getAxisMap().get(ChartAxis.yAxis);
        
        // 6. 检查是否有有效的轴数据
        if (ObjectUtils.isEmpty(xAxis) && ObjectUtils.isEmpty(yAxis)) {
            return emptyChartViewDTO(view);
        }
        
        // 7. 构建模型数据源请求
        DatasourceRequest datasourceRequest = buildModelDatasourceRequest(modelNo, indicatorAttrs);
        
        // 8. 调用ModelManageProvider获取数据
        Map<String, Object> providerResult = modelManageProvider.fetchResultField(datasourceRequest);
        
        // 9. 构建虚拟的sqlMap和sqlMeta（为了兼容chartHandler）
        Map<String, Object> sqlMap = buildVirtualSqlMap();
        
        // 10. 创建自定义过滤结果
        CustomFilterResult filterResult = new CustomFilterResult();
        filterResult.setContext(new HashMap<>());
        filterResult.getContext().put("querySql", "MODEL_QUERY");
        
        // 11. 计算图表结果
        ChartCalcDataResult calcResult = new ChartCalcDataResult();
        calcResult.setData(providerResult);
//        calcResult.setFieldMap(new HashMap<>());
//        calcResult.setTotalCount(0L);
        
        // 12. 构建最终图表
        ChartViewDTO result = chartHandler.buildChart(view, calcResult, formatResult, filterResult);
        
        logger.info("图表数据计算完成");
        return result;
    }
    
    /**
     * 获取图表处理器
     */
    private AbstractChartPlugin getChartHandler(ChartViewDTO view) {
        if (BooleanUtils.isTrue(view.getIsPlugin())) {
            return PluginsChartFactory.getInstance(view.getRender(), view.getType());
        } else {
            return chartHandlerManager.getChartHandler(view.getRender(), view.getType());
        }
    }
    
    /**
     * 构建模型数据源请求
     */
    private DatasourceRequest buildModelDatasourceRequest(String modelNo, List<String> indicatorAttrs) {
        DatasourceRequest request = new DatasourceRequest();
        
        // 构建虚拟查询SQL（实际不会被使用，只是为了传递参数）
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT ");
        
//        if (indicatorAttrs != null && !indicatorAttrs.isEmpty()) {
//            sqlBuilder.append(String.join(",", indicatorAttrs));
//        } else {
//            sqlBuilder.append("*");
//        }
//
//        sqlBuilder.append(" FROM model_table WHERE model_no = '").append(modelNo).append("'");
//        request.setQuery(sqlBuilder.toString());
        
        // 设置虚拟数据源信息
        Map<Long, DatasourceSchemaDTO> dsMap = new HashMap<>();
        DatasourceSchemaDTO modelDs = new DatasourceSchemaDTO();
        modelDs.setType("model-manage");
        modelDs.setName("模型管理数据源");
        dsMap.put(1L, modelDs);
        request.setDsList(dsMap);
        //存放模型编号
        request.setTable(modelNo);
        //存放字段列表
        request.setQuery(String.join(",", indicatorAttrs));
        return request;
    }
    
    /**
     * 构建虚拟的sqlMap（为了兼容chartHandler）
     */
    private Map<String, Object> buildVirtualSqlMap() {
        Map<String, Object> sqlMap = new HashMap<>();
        sqlMap.put("sql", "SELECT * FROM model_table");
        
        Map<Long, DatasourceSchemaDTO> dsMap = new HashMap<>();
        DatasourceSchemaDTO modelDs = new DatasourceSchemaDTO();
        modelDs.setType("model-manage");
        modelDs.setName("模型管理数据源");
        dsMap.put(1L, modelDs);
        sqlMap.put("dsMap", dsMap);
        
        return sqlMap;
    }
    
    /**
     * 创建空的图表视图
     */
    private ChartViewDTO emptyChartViewDTO(ChartViewDTO view) {
        ChartViewDTO dto = new ChartViewDTO();
        BeanUtils.copyBean(dto, view);
        return dto;
    }
    
    /**
     * 从图表视图中提取指标属性（字段列表）
     */
    public List<String> extractIndicatorAttrs(ChartViewDTO view) {
        List<String> attrs = new ArrayList<>();
        
        // 从X轴字段中提取
        if (view.getXAxis() != null) {
            attrs.addAll(view.getXAxis().stream()
                    .map(ChartViewFieldDTO::getDataeaseName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }
        
        // 从Y轴字段中提取
        if (view.getYAxis() != null) {
            attrs.addAll(view.getYAxis().stream()
                    .map(ChartViewFieldDTO::getDataeaseName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }
        
        // 从扩展轴字段中提取
        if (view.getXAxisExt() != null) {
            attrs.addAll(view.getXAxisExt().stream()
                    .map(ChartViewFieldDTO::getDataeaseName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }
        
        // 去重
        return attrs.stream().distinct().collect(Collectors.toList());
    }
    
    /**
     * 简化版本的calcData方法 - 自动提取字段
     */
    public ChartViewDTO calcData(ChartViewDTO view, String modelNo) throws Exception {
        List<String> indicatorAttrs = extractIndicatorAttrs(view);
        return calcData(view, modelNo, indicatorAttrs);
    }
}
