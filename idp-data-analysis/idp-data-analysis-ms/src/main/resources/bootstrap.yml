server:
  port: 8100

spring:
  application:
    name: idp-data-analysis-ms
  profiles:
    active: prod
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      server-addr: 192.168.10.49:8848
      username: admin
      password: nacos
      discovery:
        namespace: f246106a-f43e-4e69-b2f0-f5ff1b15d589
        group: DEFAULT_GROUP
      config:
        namespace: f246106a-f43e-4e69-b2f0-f5ff1b15d589
        file-extension: yaml
        shared-configs:
          - data-id: common-druid.yaml
            group: DEFAULT_GROUP
            refresh: true
